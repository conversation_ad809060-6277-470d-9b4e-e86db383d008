FROM ghcr.io/astral-sh/uv:python3.10-bookworm-slim AS builder

ENV UV_COMPILE_BYTECODE=1 \
    UV_LINK_MODE=copy \
    UV_PYTHON_DOWNLOADS=0 \
    UV_HTTP_TIMEOUT=180 \
    UV_PYTHON_REPOSITORY=https://mirrors.aliyun.com/pypi/simple/

WORKDIR /app

COPY pyproject.toml uv.lock ./

RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync

FROM python:3.10.18-slim-bookworm

ENV DEBIAN_FRONTEND=noninteractive \
    TZ=Asia/Shanghai \
    VIRTUAL_ENV=/app/.venv \
    PYTHONUNBUFFERED=1 \
    PATH="/app/.venv/bin:$PATH"

# 将构建好的 app 和虚拟环境复制过来
COPY --from=builder /app /app

COPY . /app/
WORKDIR /app

VOLUME ["/web_data", "/weights"]

EXPOSE 8000

CMD ["python", "main.py"]