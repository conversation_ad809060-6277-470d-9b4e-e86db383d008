from abc import ABC, abstractmethod
from typing import Union, AsyncGenerator, List
from aikit.core.data_types import ModelConfig, ModelType, ChatRequest, ChatResponse, EmbeddingRequest, EmbeddingResponse


class BaseModel(ABC):
    """模型基类"""

    def __init__(self, config: ModelConfig):
        self.config = config
        self._validate_config()

    def _validate_config(self):
        """验证配置"""
        if not self.config.model_name:
            raise ValueError("model_name is required")
        if not self.config.api_key:
            raise ValueError("api_key is required")
        if not self.config.base_url:
            raise ValueError("base_url is required")

    @abstractmethod
    async def health_check(self) -> bool:
        """健康检查"""
        pass


class BaseLLMModel(BaseModel):
    """LLM模型基类"""

    def __init__(self, config: ModelConfig):
        if config.model_type != ModelType.LLM:
            raise ValueError("ModelType must be LLM for LLMModel")
        super().__init__(config)

    @abstractmethod
    async def chat_completion(
            self,
            request: ChatRequest
    ) -> Union[ChatResponse, AsyncGenerator[str, None]]:
        """聊天补全"""
        pass

    @abstractmethod
    async def generate(
            self,
            prompt: str,
            stream: bool = False,
            **kwargs
    ) -> Union[str, AsyncGenerator[str, None]]:
        """文本生成"""
        pass

    async def chat_completion_stream(
            self,
            request: ChatRequest
    ) -> AsyncGenerator[str, None]:
        """流式聊天补全"""
        request.stream = True
        async for chunk in await self.chat_completion(request):
            yield chunk


class BaseEmbeddingModel(BaseModel):
    """Embedding模型基类"""

    def __init__(self, config: ModelConfig):
        if config.model_type != ModelType.EMBEDDING:
            raise ValueError("ModelType must be EMBEDDING for EmbeddingModel")
        super().__init__(config)

    @abstractmethod
    async def create_embedding(
            self,
            request: EmbeddingRequest
    ) -> EmbeddingResponse:
        """创建向量嵌入"""
        pass

    async def embed_text(self, text: str) -> List[float]:
        """单个文本嵌入"""
        request = EmbeddingRequest(
            input=text,
            model=self.config.model_name
        )
        response = await self.create_embedding(request)
        return response.data[0]["embedding"]

    async def embed_texts(self, texts: List[str]) -> List[List[float]]:
        """批量文本嵌入"""
        request = EmbeddingRequest(
            input=texts,
            model=self.config.model_name
        )
        response = await self.create_embedding(request)
        return [item["embedding"] for item in response.data]

