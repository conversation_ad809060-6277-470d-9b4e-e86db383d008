from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from enum import Enum
from pydantic import BaseModel


class ModelType(Enum):
    """模型类型枚举"""
    LLM = "llm"
    EMBEDDING = "embedding"
    IMAGE = "image"
    AUDIO = "audio"
    MULTIMODAL = "multimodal"


class ResponseFormat(Enum):
    """响应格式枚举"""
    TEXT = "text"
    JSON = "json"
    STREAM = "stream"


class ModelConfig(BaseModel):
    """模型配置类"""
    model_name: str
    api_key: str
    base_url: str
    model_type: ModelType
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    top_p: Optional[float] = None
    timeout: int = 30
    max_retries: int = 3
    extra_params: Optional[Dict[str, Any]] = None


class Message(BaseModel):
    """消息类"""
    role: str  # system, user, assistant
    content: str
    name: Optional[str] = None


class ChatRequest(BaseModel):
    """聊天请求类"""
    messages: List[Message]
    model: str
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    top_p: Optional[float] = None
    stream: bool = False
    response_format: Optional[Dict[str, str]] = None
    tools: Optional[List[Dict[str, Any]]] = None
    tool_choice: Optional[Union[str, Dict[str, Any]]] = None


class ChatResponse(BaseModel):
    """聊天响应类"""
    id: str
    object: str
    created: int
    model: str
    choices: List[Dict[str, Any]]
    usage: Optional[Dict[str, Optional[Any]]] = None


class EmbeddingRequest(BaseModel):
    """Embedding请求类"""
    input: Union[str, List[str]]
    model: str
    encoding_format: str = "float"
    dimensions: Optional[int] = None


class EmbeddingResponse(BaseModel):
    """Embedding响应类"""
    object: str
    data: List[Dict[str, Any]]
    model: str
    usage: Dict[str, int]
