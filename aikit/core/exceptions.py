"""异常定义模块"""

class ModelError(Exception):
    """模型相关错误的基类"""
    pass


class ConfigError(ModelError):
    """配置错误"""
    pass


class APIError(ModelError):
    """API调用错误"""
    def __init__(self, message: str, status_code: int = None, response_data: dict = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data


class RateLimitError(APIError):
    """速率限制错误"""
    def __init__(self, message: str, retry_after: int = None):
        super().__init__(message, status_code=429)
        self.retry_after = retry_after


class ModelNotFoundError(ModelError):
    """模型未找到错误"""
    pass


class ProviderError(ModelError):
    """模型提供商错误"""
    pass


class ValidationError(ModelError):
    """数据验证错误"""
    pass


class ModelTimeoutError(ModelError):
    """超时错误"""
    pass


class AuthenticationError(APIError):
    """认证错误"""
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(message, status_code=401)