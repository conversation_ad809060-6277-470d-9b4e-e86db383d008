from typing import List, Optional, Union, AsyncGenerator
from aikit.core.base import BaseLLMModel, BaseEmbeddingModel
from aikit.core.data_types import ModelConfig, ModelType, ChatRequest, Message
from aikit.core.exceptions import ModelNotFoundError
from aikit.core.registry import ModelRegistry
from utils.base import singleton


@singleton
class ModelManager:
    """模型管理器"""

    def __init__(self):
        self.registry = ModelRegistry()
        self._default_llm: Optional[str] = None
        self._default_embedding: Optional[str] = None

    def add_model(self, name: str, provider: str, config: ModelConfig):
        """添加模型"""
        model = self.registry.create_model(provider, config)
        self.registry.register_model(name, model)

        # 设置默认模型
        if config.model_type == ModelType.LLM and not self._default_llm:
            self._default_llm = name
        elif config.model_type == ModelType.EMBEDDING and not self._default_embedding:
            self._default_embedding = name

    def get_llm(self, name: Optional[str] = None) -> BaseLLMModel:
        """获取LLM模型"""
        model_name = name or self._default_llm
        if not model_name:
            raise ModelNotFoundError("No LLM model available")

        model = self.registry.get_model(model_name)
        if not isinstance(model, BaseLLMModel):
            raise TypeError(f"Model {model_name} is not an LLM model")
        return model

    def get_embedding(self, name: Optional[str] = None) -> BaseEmbeddingModel:
        """获取Embedding模型"""
        model_name = name or self._default_embedding
        if not model_name:
            raise ModelNotFoundError("No embedding model available")

        model = self.registry.get_model(model_name)
        if not isinstance(model, BaseEmbeddingModel):
            raise TypeError(f"Model {model_name} is not an embedding model")
        return model

    async def chat(
            self,
            messages: List[Message],
            model: Optional[str] = None,
            stream: bool = False,
            **kwargs
    ) -> Union[str, AsyncGenerator[str, None]]:
        """聊天接口"""
        llm = self.get_llm(model)

        request = ChatRequest(
            messages=messages,
            model=llm.config.model_name,
            stream=stream,
            **kwargs
        )

        response = await llm.chat_completion(request)

        if stream:
            return response
        else:
            return response.choices[0]["message"]["content"]

    async def embed(
            self,
            text: Union[str, List[str]],
            model: Optional[str] = None
    ) -> Union[List[float], List[List[float]]]:
        """文本嵌入接口"""
        embedding_model = self.get_embedding(model)

        if isinstance(text, str):
            return await embedding_model.embed_text(text)
        else:
            return await embedding_model.embed_texts(text)


# 全局模型管理器实例
model_manager = ModelManager()