"""模型注册器 - 支持装饰器注册"""

from typing import Dict, List, Type, Callable

from utils.base import singleton
from .base import BaseModel, BaseLLMModel, BaseEmbeddingModel
from .data_types import ModelConfig
from .exceptions import ModelError


@singleton
class ModelRegistry:
    """模型注册器"""

    def __init__(self):
        self._models: Dict[str, BaseModel] = {}
        self._model_classes: Dict[str, Type[BaseModel]] = {}
        self._provider_factories: Dict[str, Callable] = {}

    def register_model_class(self, provider: str, model_class: Type[BaseModel]):
        """注册模型类"""
        self._model_classes[provider] = model_class

    def register_provider_factory(self, provider: str, factory: Callable):
        """注册提供商工厂函数"""
        self._provider_factories[provider] = factory

    def create_model(self, provider: str, config: ModelConfig) -> BaseModel:
        """创建模型实例"""
        if provider in self._provider_factories:
            # 使用提供商工厂创建
            factory = self._provider_factories[provider]
            return factory(config)
        elif provider in self._model_classes:
            # 使用模型类创建
            model_class = self._model_classes[provider]
            return model_class(config)
        else:
            raise ValueError(f"Unknown provider: {provider}")

    def register_model(self, name: str, model: BaseModel):
        """注册模型实例"""
        self._models[name] = model

    def get_model(self, name: str) -> BaseModel:
        """获取模型实例"""
        if name not in self._models:
            raise ValueError(f"Model {name} not found")
        return self._models[name]

    def list_models(self) -> List[str]:
        """列出所有模型"""
        return list(self._models.keys())

    def list_providers(self) -> List[str]:
        """列出所有提供商"""
        providers = set()
        providers.update(self._model_classes.keys())
        providers.update(self._provider_factories.keys())
        return list(providers)

    # 装饰器方法
    def provider(self, name: str):
        """提供商装饰器 - 用于注册提供商类"""
        def decorator(cls):
            self._model_classes[name] = cls
            return cls
        return decorator

    def llm_provider(self, name: str):
        """LLM 提供商装饰器"""
        def decorator(cls):
            if not issubclass(cls, BaseLLMModel):
                raise ModelError(f"Class {cls.__name__} must inherit from BaseLLMModel")
            self._model_classes[name] = cls
            return cls
        return decorator

    def embedding_provider(self, name: str):
        """嵌入提供商装饰器"""
        def decorator(cls):
            if not issubclass(cls, BaseEmbeddingModel):
                raise ModelError(f"Class {cls.__name__} must inherit from BaseEmbeddingModel")
            self._model_classes[name] = cls
            return cls
        return decorator

    def factory(self, name: str):
        """工厂函数装饰器 - 用于注册提供商工厂"""
        def decorator(func):
            self._provider_factories[name] = func
            return func
        return decorator


# 全局注册器实例
registry = ModelRegistry()

# 导出装饰器函数，方便使用
def provider(name: str):
    """注册提供商装饰器"""
    return registry.provider(name)

def llm_provider(name: str):
    """注册 LLM 提供商装饰器"""
    return registry.llm_provider(name)

def embedding_provider(name: str):
    """注册嵌入提供商装饰器"""
    return registry.embedding_provider(name)

def factory(name: str):
    """注册工厂函数装饰器"""
    return registry.factory(name)

def register_model_instance(name: str, provider: str, config: ModelConfig):
    """便捷函数：创建并注册模型实例"""
    model = registry.create_model(provider, config)
    registry.register_model(name, model)
    return model