"""自动发现和注册示例"""

import asyncio
import os

from aikit.core.manager import ModelManager
from aikit.core.registry import registry, register_model_instance
from aikit.core.data_types import ModelType
from aikit.providers.openai_compatible.config import OpenAIConfig
from dotenv import load_dotenv, find_dotenv
from loguru import logger
import aikit.providers.openai_compatible

load_dotenv(find_dotenv())


def auto_register_ai_models():
    """自动注册 OpenAI 相关模型"""

    # 阿里云 API Key
    dashscope_key = os.getenv("DASHSCOPE_API_KEY")
    if dashscope_key:
        # 注册阿里云模型
        qwen_models = [
            ("qwen-turbo", "qwen-turbo"),
            ("qwen-plus", "qwen-plus"),
            ("qwen-max", "qwen-max"),
        ]

        for name, model_name in qwen_models:
            config = OpenAIConfig(
                model_name=model_name,
                api_key=dashscope_key,
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
                model_type=ModelType.LLM
            )
            register_model_instance(name, "openai_compatible", config)
            logger.info(f"已注册阿里云模型: {name}")


async def main():
    """自动发现示例"""

    print("=== 自动发现和注册模型 ===")
    auto_register_ai_models()

    print(f"\n已注册的模型: {registry.list_models()}")
    print(f"可用的提供商: {registry.list_providers()}")

    async for resp in await ModelManager().get_llm('qwen-turbo').generate("你好", stream=True):
        print(resp, end='', flush=True)


if __name__ == "__main__":
    asyncio.run(main())