"""OpenAI 提供商模块"""

from .models import OpenAILLMModel, OpenAIEmbeddingModel
from .config import OpenAIConfig


class OpenAIProvider:
    """OpenAI 提供商"""

    @staticmethod
    def create_llm_model(cfg):
        return OpenAILLMModel(cfg)

    @staticmethod
    def create_embedding_model(cfg):
        return OpenAIEmbeddingModel(cfg)


__all__ = [
    "OpenAIProvider",
    "OpenAILLMModel",
    "OpenAIEmbeddingModel",
    "OpenAIConfig"
]