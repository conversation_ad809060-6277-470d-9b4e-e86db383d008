"""OpenAI 配置模块"""

from dataclasses import dataclass
from typing import Optional
from ...core.data_types import ModelConfig


class OpenAIConfig(ModelConfig):
    """OpenAI 兼容配置"""

    # OpenAI 特有参数
    organization: Optional[str] = None
    project: Optional[str] = None

    # 代理设置
    proxy: Optional[str] = None

    def __post_init__(self):
        super().__post_init__()

        # 设置默认值
        if not self.base_url:
            self.base_url = "https://api.openai.com/v1"