"""OpenAI 模型实现"""

import openai
from httpx import Client, AsyncClient
from typing import Union, AsyncGenerator
from openai import OpenAI, AsyncOpenAI
from ...core.base import BaseLLMModel, BaseEmbeddingModel
from ...core.data_types import Message, ChatRequest, ChatResponse, EmbeddingRequest, EmbeddingResponse, ModelType
from ...core.exceptions import APIError, RateLimitError, AuthenticationError
from ...core.registry import llm_provider, embedding_provider, factory
from .config import OpenAIConfig
from loguru import logger


@llm_provider("openai_llm")
class OpenAILLMModel(BaseLLMModel):
    """OpenAI LLM 模型"""

    def __init__(self, config):
        super().__init__(config)

        # 如果传入的不是 OpenAIConfig，则转换
        if not isinstance(config, OpenAIConfig):
            self.config = OpenAIConfig(**config.__dict__)

        # 初始化客户端
        self._init_clients()

    def _init_clients(self):
        """初始化同步和异步客户端"""
        client_kwargs = {
            "api_key": self.config.api_key,
            "base_url": self.config.base_url,
            "timeout": self.config.timeout,
            "max_retries": self.config.max_retries,
        }

        if self.config.organization:
            client_kwargs["organization"] = self.config.organization

        if self.config.project:
            client_kwargs["project"] = self.config.project

        # 如果有代理设置
        if self.config.proxy:
            client_kwargs["http_client"] = Client(proxies=self.config.proxy) # type: ignore

        # 创建同步和异步客户端
        self.sync_client = OpenAI(**client_kwargs)

        # 异步客户端配置
        async_kwargs = client_kwargs.copy()
        if self.config.proxy:
            async_kwargs["http_client"] = AsyncClient(proxies=self.config.proxy) # type: ignore

        self.async_client = AsyncOpenAI(**async_kwargs)

    async def health_check(self) -> bool:
        """健康检查"""
        try:
            models = await self.async_client.models.list()
            return len(models.data) > 0
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False

    async def chat_completion(
            self,
            request: ChatRequest
    ) -> Union[ChatResponse, AsyncGenerator[str, None]]:
        """聊天补全"""
        try:
            # 转换消息格式
            messages = []
            for msg in request.messages:
                openai_msg = {"role": msg.role, "content": msg.content}
                if hasattr(msg, 'name') and msg.name:
                    openai_msg["name"] = msg.name
                messages.append(openai_msg)

            # 构建参数
            params = {
                "model": request.model,
                "messages": messages,
                "stream": request.stream,
            }

            # 添加可选参数
            if request.max_tokens:
                params["max_tokens"] = request.max_tokens # type: ignore
            if request.temperature is not None:
                params["temperature"] = request.temperature # type: ignore
            if request.top_p is not None:
                params["top_p"] = request.top_p # type: ignore
            if request.response_format:
                params["response_format"] = request.response_format # type: ignore
            if request.tools:
                params["tools"] = request.tools
            if request.tool_choice:
                params["tool_choice"] = request.tool_choice

            if request.stream:
                return self._stream_chat_completion(params)
            else:
                return await self._non_stream_chat_completion(params)

        except Exception as e:
            raise self._handle_openai_error(e)

    async def _non_stream_chat_completion(self, params) -> ChatResponse:
        """非流式聊天补全"""
        response = await self.async_client.chat.completions.create(**params)

        # 转换为我们的响应格式
        response_dict = response.model_dump()
        return ChatResponse(
            id=response_dict["id"],
            object=response_dict["object"],
            created=response_dict["created"],
            model=response_dict["model"],
            choices=response_dict["choices"],
            usage=response_dict.get("usage")
        )

    async def _stream_chat_completion(self, params) -> AsyncGenerator[str, None]:
        """流式聊天补全"""
        stream = await self.async_client.chat.completions.create(**params)

        async for chunk in stream:
            if chunk.choices and chunk.choices[0].delta.content:
                yield chunk.choices[0].delta.content

    async def generate(
            self,
            prompt: str,
            stream: bool = False,
            **kwargs
    ) -> Union[str, AsyncGenerator[str, None]]:
        """文本生成"""

        messages = [Message(role="user", content=prompt)]
        request = ChatRequest(
            messages=messages,
            model=self.config.model_name,
            stream=stream,
            **kwargs
        )

        response = await self.chat_completion(request)

        if stream:
            return response
        else:
            return response.choices[0]["message"]["content"]

    def _handle_openai_error(self, error):
        """处理 OpenAI 错误"""
        if isinstance(error, openai.AuthenticationError):
            return AuthenticationError(f"Authentication failed: {error}")
        elif isinstance(error, openai.RateLimitError):
            retry_after = getattr(error, 'retry_after', None)
            return RateLimitError(f"Rate limit exceeded: {error}", retry_after)
        elif isinstance(error, openai.APIError):
            return APIError(f"API error: {error}", getattr(error, 'status_code', None))
        else:
            return APIError(f"Unexpected error: {error}")


@embedding_provider("openai_embedding")
class OpenAIEmbeddingModel(BaseEmbeddingModel):
    """OpenAI Embedding 模型"""

    def __init__(self, config):
        super().__init__(config)

        # 如果传入的不是 OpenAIConfig，则转换
        if not isinstance(config, OpenAIConfig):
            self.config = OpenAIConfig(**config.__dict__)

        # 初始化客户端
        self._init_clients()

    def _init_clients(self):
        """初始化同步和异步客户端"""
        client_kwargs = {
            "api_key": self.config.api_key,
            "base_url": self.config.base_url,
            "timeout": self.config.timeout,
            "max_retries": self.config.max_retries,
        }

        if self.config.organization:
            client_kwargs["organization"] = self.config.organization

        if self.config.project:
            client_kwargs["project"] = self.config.project

        # 如果有代理设置
        if self.config.proxy:
            client_kwargs["http_client"] = Client(proxies=self.config.proxy) # type: ignore

        # 创建同步和异步客户端
        self.sync_client = OpenAI(**client_kwargs)

        # 异步客户端配置
        async_kwargs = client_kwargs.copy()
        if self.config.proxy:
            async_kwargs["http_client"] = AsyncClient(proxies=self.config.proxy) # type: ignore

        self.async_client = AsyncOpenAI(**async_kwargs)

    async def health_check(self) -> bool:
        """健康检查"""
        try:
            models = await self.async_client.models.list()
            return len(models.data) > 0
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False

    async def create_embedding(
            self,
            request: EmbeddingRequest
    ) -> EmbeddingResponse:
        """创建向量嵌入"""
        try:
            params = {
                "input": request.input,
                "model": request.model,
                "encoding_format": request.encoding_format
            }

            if request.dimensions:
                params["dimensions"] = request.dimensions # type: ignore

            response = await self.async_client.embeddings.create(**params)

            # 转换为我们的响应格式
            response_dict = response.model_dump()
            return EmbeddingResponse(
                object=response_dict["object"],
                data=response_dict["data"],
                model=response_dict["model"],
                usage=response_dict["usage"]
            )

        except Exception as e:
            raise self._handle_openai_error(e)

    def _handle_openai_error(self, error):
        """处理 OpenAI 错误"""
        if isinstance(error, openai.AuthenticationError):
            return AuthenticationError(f"Authentication failed: {error}")
        elif isinstance(error, openai.RateLimitError):
            retry_after = getattr(error, 'retry_after', None)
            return RateLimitError(f"Rate limit exceeded: {error}", retry_after)
        elif isinstance(error, openai.APIError):
            return APIError(f"API error: {error}", getattr(error, 'status_code', None))
        else:
            return APIError(f"Unexpected error: {error}")


@factory("openai_compatible")
def create_openai_model(config: OpenAIConfig):
    """OpenAI 模型工厂函数"""
    if config.model_type == ModelType.LLM:
        return OpenAILLMModel(config)
    elif config.model_type == ModelType.EMBEDDING:
        return OpenAIEmbeddingModel(config)
    else:
        raise ValueError(f"Unsupported model type: {config.model_type}")