from models.base import CamelCaseModel
from settings import ROOT
from pydantic import Field
from typing import Generic, TypeVar, Optional, Any, Dict
from datetime import datetime

T = TypeVar('T')


class ApiResponse(CamelCaseModel, Generic[T]):
    """统一 API 响应格式"""
    success: bool = Field(description="请求是否成功")
    data: Optional[T] = Field(None, description="响应数据")
    message: str = Field(description="响应消息")
    error_code: Optional[str] = Field(None, description="错误代码")
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat(), description="响应时间")
    extra: Dict[str, Any] = Field(default_factory=dict, description="扩展字段")

    @classmethod
    def success_response(
            cls,
            data: Optional[T] = None,
            message: str = "操作成功",
            **kwargs
    ) -> "ApiResponse[T]":
        """创建成功响应"""
        return cls(
            success=True,
            data=data,
            message=message,
            error_code=None,
            extra=kwargs
        )

    @classmethod
    def error_response(
            cls,
            message: str,
            error_code: Optional[str] = None,
            data: Optional[T] = None
    ) -> "ApiResponse[T]":
        """创建错误响应"""
        return cls(
            success=False,
            data=data,
            message=message,
            error_code=error_code
        )


class Config:
    STATIC = ROOT / "web_data/static"  # 静态文件目录
    UPLOAD_DIR = ROOT / "web_data/uploads"  # 上传文件目录
    OUTPUT_DIR = ROOT / "web_data/outputs"  # 输出文件目录
    TEMP_DIR = ROOT / "web_data/temp"  # 临时文件目录
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 最大文件大小 10MB
    ALLOWED_EXTENSIONS = {".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".webp"}
    DEFAULT_CONFIDENCE = 0.5
    DEFAULT_IOU = 0.45


for path in [
    Config.STATIC,
    Config.UPLOAD_DIR,
    Config.OUTPUT_DIR,
    Config.TEMP_DIR
]:
    if not path.exists():
        path.mkdir(parents=True, exist_ok=True)

