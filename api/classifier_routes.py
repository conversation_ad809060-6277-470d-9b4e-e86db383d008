"""
图像分类路由模块
提供图像分类API接口
"""

import uuid
from fastapi import File, UploadFile, HTTPException, Form
from fastapi import APIRouter
from datetime import datetime
from loguru import logger

from vision.classification import ClassifierModelManager
from vision.classification.classifer_holder import ClassifierModelType
from api.base import Config, ApiResponse
from utils.web import validate_image_file, uploadfile_to_image_or_numpy


# 定义API路由
router = APIRouter(prefix="/classifier", tags=["图像分类API"])


# 响应模型已移动到 api.models 模块中


@router.post("/classify")
async def classify_image(
    file: UploadFile = File(...),
    confidence: float = Form(default=Config.DEFAULT_CONFIDENCE, description="分类置信度阈值")
):
    """
    图像分类接口
    
    Args:
        file: 上传的图片文件
        confidence: 分类置信度阈值 (0.0-1.0)
        
    Returns:
        分类结果，包含预测类别、置信度等信息
    """
    # 验证参数
    if not (0.0 <= confidence <= 1.0):
        raise HTTPException(status_code=400, detail="置信度阈值必须在0.0-1.0之间")
    
    # 验证文件
    if not validate_image_file(file, Config.ALLOWED_EXTENSIONS):
        raise HTTPException(status_code=400, detail="不支持的文件格式")
    
    file_id = str(uuid.uuid4())

    try:
        # 保存上传的文件
        uploaded_image = await uploadfile_to_image_or_numpy(file, to_numpy=False)
        
        # 创建分类器并执行分类
        classifier = ClassifierModelManager().load_model(ClassifierModelType.PAGE_TYPE)
        classification_result = classifier.predict_single(uploaded_image)

        if not classification_result:
            raise HTTPException(status_code=500, detail="分类结果为空，请检查模型和输入数据")
        
        # 准备响应数据
        response_data = {
            'predicted_class_id': classification_result.class_id,
            'predicted_class_name': classification_result.class_name,
            'confidence': classification_result.confidence,
            "file_id": file_id,
            "filename": file.filename,
            "timestamp": datetime.now().isoformat()
        }
        logger.info(f"分类完成: {file.filename}, "
                    f"类别编号: {classification_result.class_id}, "
                    f"类别: {classification_result.class_name}, "
                    f"置信度: {classification_result.confidence}")

        return ApiResponse.success_response(data=response_data)
        
    except Exception as e:
        logger.error(f"图像分类失败: {e}")
        raise HTTPException(status_code=500, detail=f"图像分类失败: {str(e)}")
