from collections import defaultdict
from typing import List
from fastapi import APIRouter, Body
from api.base import ApiResponse
from drawing_service.detection_review import investigate_hole_info, compare_drill_spacings, investigate_layer_info
from models.integrated_models import LogicResult
from models.pdf_info_models import PdfPageType
from models.report_models import CheckingReportEntity

router = APIRouter(prefix="/invest", tags=["审查报告相关 API"])


@router.post("/invest_report")
async def invest_report(
        layout: List[LogicResult] = Body(...),
        geo_report: List[LogicResult] = Body(..., alias="geoReport"),
):
    """
    对逻辑结果进行修补
    """
    logic_result_map = defaultdict(list)
    for logic_result in geo_report:
        logic_result_map[logic_result.detect_type].append(logic_result)

    section_results = logic_result_map[PdfPageType.SECTION.desc]
    plane_results = logic_result_map[PdfPageType.FLOOR_PLAN.desc]
    table_results = logic_result_map[PdfPageType.TABLE.desc]

    # 构建返回列表
    check_point_list = []

    # 审查钻口信息
    check_point_list += investigate_hole_info(plane_results, section_results, table_results)

    # 审查勘探孔间距
    check_point_list += compare_drill_spacings(section_results, plane_results)
    # 审查分层信息
    check_point_list += investigate_layer_info(section_results, table_results)

    # export_check_point_list_to_excel(check_point_list)

    # 返回数据结构待定
    response_data = CheckingReportEntity(
        checking_reports=check_point_list
    )
    return ApiResponse.success_response(data=response_data)
