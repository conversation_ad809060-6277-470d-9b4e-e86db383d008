from fastapi import APIRouter, UploadFile, File, HTTPException, Form
from fastapi.responses import JSONResponse
from vision.ocr_engine.engine import OcrEng<PERSON>, OcrModelType
from PIL import Image
import io

router = APIRouter(prefix="/ocr", tags=["OCR 文本识别 API"])


# API路由
@router.post("/ocr_img")
async def ocr_img(
    file: UploadFile = File(...),
    model_name: OcrModelType = Form(default=OcrModelType.RAPID, alias="modelName")
):
    """
    OCR图像识别接口
    """
    # 检查文件类型
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="请上传图片文件")
    try:
        # 读取图片内容为PIL Image
        contents = await file.read()
        image = Image.open(io.BytesIO(contents)).convert('RGB')
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"图片读取失败: {e}")

    try:
        # 将枚举值转换为字符串
        ocr_engine = OcrEngine.get_instance(model_name)
        result = ocr_engine.recognize(image)
        return JSONResponse(content=result.to_dict())
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"OCR识别失败: {e}")
