from fastapi import APIRouter, UploadFile, File, HTTPException, Form
from fastapi.responses import JSONResponse
from typing import List, Optional

from api.base import ApiResponse
from utils.pdf import PdfTextItem, PdfProcessor, PageInfo

router = APIRouter(prefix="/pdf", tags=["PDF 相关 API"])


@router.post("/pdf_metadata")
async def pdf_metadata(
        file: UploadFile = File(...),
        page_numbers: Optional[str] = Form(None),  # 例如: "1,3,5" 或 "1-5"
        granularity: str = Form("span")  # "span", "line", "block"
):
    """
    提取PDF文本信息，支持指定页面

    Args:
        file: PDF文件
        page_numbers: 页码范围，如 "1,3,5" 或 "1-5" 或 "1,3-5,8"
        granularity: 提取粒度 - span(默认), line, block
    """
    try:
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="只支持PDF文件")

        pdf_content = await file.read()

        # 解析页码参数
        parsed_pages = None
        if page_numbers:
            parsed_pages = _parse_page_numbers(page_numbers)

        with PdfProcessor(pdf_content=pdf_content) as processor:
            data = processor.extract_text_items(
                page_numbers=parsed_pages,
                granularity=granularity
            )
            return ApiResponse.success_response(data=data)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理PDF文件时出错: {str(e)}")


@router.post("/pdf_page_info")
async def pdf_page_info(
        file: UploadFile = File(...),
        page_numbers: Optional[str] = Form(None)
):
    """获取PDF页面信息"""
    try:
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="只支持PDF文件")

        pdf_content = await file.read()

        parsed_pages = None
        if page_numbers:
            parsed_pages = _parse_page_numbers(page_numbers)

        with PdfProcessor(pdf_content=pdf_content) as processor:
            data = processor.get_page_info(page_numbers=parsed_pages)
            return ApiResponse.success_response(data=data)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理PDF文件时出错: {str(e)}")


@router.post("/pdf_text_by_page")
async def pdf_text_by_page(
        file: UploadFile = File(...),
        page_numbers: Optional[str] = Form(None)
):
    """按页提取PDF纯文本"""
    try:
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="只支持PDF文件")

        pdf_content = await file.read()

        parsed_pages = None
        if page_numbers:
            parsed_pages = _parse_page_numbers(page_numbers)

        with PdfProcessor(pdf_content=pdf_content) as processor:
            page_texts = processor.extract_page_text(page_numbers=parsed_pages)
            metadata = processor.get_metadata()

            return ApiResponse.success_response(data={
                "total_pages": processor.total_pages,
                "processed_pages": list(page_texts.keys()),
                "page_texts": page_texts,
                "metadata": metadata
            })

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理PDF文件时出错: {str(e)}")


@router.post("/pdf_search")
async def pdf_search(
        file: UploadFile = File(...),
        query: str = Form(...),
        page_numbers: Optional[str] = Form(None)
):
    """在PDF中搜索文本"""
    try:
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="只支持PDF文件")

        pdf_content = await file.read()

        parsed_pages = None
        if page_numbers:
            parsed_pages = _parse_page_numbers(page_numbers)

        with PdfProcessor(pdf_content=pdf_content) as processor:
            data = processor.search_text(query, page_numbers=parsed_pages)
            return ApiResponse.success_response(data=data)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理PDF文件时出错: {str(e)}")


@router.post("/pdf_high_dpi")
async def pdf_high_dpi(
        file: UploadFile = File(...),
        page_numbers: Optional[str] = Form(None),
        dpi: int = Form(300)
) :
    """高DPI转换提取PDF文本"""
    try:
        if not file.filename.lower().endswith('.pdf'):
            raise HTTPException(status_code=400, detail="只支持PDF文件")

        pdf_content = await file.read()

        parsed_pages = None
        if page_numbers:
            parsed_pages = _parse_page_numbers(page_numbers)

        with PdfProcessor(pdf_content=pdf_content) as processor:
            data = processor.extract_text_with_dpi_conversion(
                page_numbers=parsed_pages,
                dpi=dpi
            )
            return ApiResponse.success_response(data=data)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理PDF文件时出错: {str(e)}")


def _parse_page_numbers(page_str: str) -> List[int]:
    """
    解析页码字符串
    支持格式: "1,3,5" 或 "1-5" 或 "1,3-5,8"
    """
    pages = []
    parts = page_str.split(',')

    for part in parts:
        part = part.strip()
        if '-' in part:
            start, end = map(int, part.split('-'))
            pages.extend(range(start, end + 1))
        else:
            pages.append(int(part))

    return sorted(list(set(pages)))  # 去重并排序