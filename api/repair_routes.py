from collections import defaultdict
from typing import List
from fastapi import APIRouter, Body

from api.base import ApiResponse
from models.integrated_models import LogicResult
from models.pdf_info_models import PdfPageType
from models.section_models import DetectedSectionResult
from vision.ocr_engine.external.roman import validate_labels, repair_labels

router = APIRouter(prefix="/repair", tags=["数据修复相关 API"])


def repair_section_result(section_results: List[DetectedSectionResult]):
    """
    修补剖面结果中的剖面编号
    """
    section_numbers = []
    for section_result in section_results:
        for result in section_result.sections:
            if result.section_number and result.section_number.original_text:  # 判空
                section_numbers.append((result.id, result.section_number.original_text))
            else:
                section_numbers.append((result.id, None))  # 保留占位，后面 zip 对应

    # 过滤掉 None 去校验
    valid_numbers = [num for _, num in section_numbers]
    need_fix = validate_labels(valid_numbers)

    if need_fix:
        repaired_numbers = repair_labels(valid_numbers)

        # 写回 section_results
        repaired_iter = iter(repaired_numbers)
        for (sec_id, num) in section_numbers:
            if num is None:
                continue  # 原本就是 None，跳过
            repaired_num = next(repaired_iter)
            # 找回对应对象并更新
            for section_result in section_results:
                for result in section_result.sections:
                    if result.id == sec_id and result.section_number:
                        result.section_number.original_text = repaired_num

    return section_results


@router.post("/logic_vision")
async def repair_logic_vision(
    logic_results: List[LogicResult] = Body(...)
):
    """
    对逻辑结果进行修补
    """
    logic_result_map = defaultdict(list)
    for logic_result in logic_results:
        logic_result_map[logic_result.detect_type].append(logic_result)

    # 修补剖面数据
    repair_section_result(logic_result_map[PdfPageType.SECTION.desc] or [])

    return ApiResponse.success_response(data=logic_results)

