import json
from typing import List

from fastapi import File, UploadFile, HTTPException, Form
from fastapi import APIRouter
from fastapi.responses import <PERSON>SONResponse
import os
from datetime import datetime
from loguru import logger

from vision.core import DetectionResult
from vision.obj_det import Y<PERSON>OModelManager
from vision.obj_det.detector import Y<PERSON>ODetector
from vision.obj_det.yolo_holder import Y<PERSON>O_MODEL_CONFIG, YoloModelType
from api.base import Config, ApiResponse
from utils.web import validate_image_file, uploadfile_to_image_or_numpy, validate_detection_file

router = APIRouter(prefix="/yolo", tags=["YOLO目标检测API"])


@router.post("/detect")
async def detect_objects(
        file: UploadFile = File(...),
        model_type: YoloModelType = Form(default=YoloModelType.SECTION_FULL, alias="modelType"),
        confidence: float = Form(default=Config.DEFAULT_CONFIDENCE),
        iou_threshold: float = Form(default=Config.DEFAULT_IOU, alias="iouThreshold"),
):
    """
    单张图片目标检测接口

    Args:
        file: 上传的图片文件
        model_type: 模型类型
        confidence: 置信度阈值 (0.0-1.0)
        iou_threshold: IoU阈值 (0.0-1.0)

    Returns:
        检测结果JSON和可选的图片数据
    """

    # 验证参数
    validate_detection_file(file, confidence, iou_threshold, Config.ALLOWED_EXTENSIONS)

    try:
        test_img = await uploadfile_to_image_or_numpy(file, to_numpy=True)

        # 创建检测器实例
        detector = YOLOModelManager().load_model(model_type)

        # 执行检测
        logger.info(f"开始检测图片: {file.filename}")
        result = detector.predict_single(test_img)

        # 构建响应数据
        response_data = {
            "success": True,
            "filename": file.filename,
            "detections": [item.model_dump() for item in result],
            "parameters": {
                "confidence_threshold": confidence,
                "iou_threshold": iou_threshold
            },
            "timestamp": datetime.now().isoformat()
        }

        logger.info(f"检测完成: {file.filename}, 检测到 {len(result)} 个目标")

        return ApiResponse.success_response(data=response_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检测过程中发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"检测失败: {str(e)}")
