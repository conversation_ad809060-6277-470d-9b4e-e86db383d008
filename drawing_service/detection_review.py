from enum import Enum
from typing import List, Union

from loguru import logger

from drawing_service import ComplexTableVisionComputer
from models.pdf_info_models import DataSourceType
from models.plane_models import DetectedPlainResult
from models.section_models import DetectedSectionResult
from models.report_models import CheckingReport, DrillHoleInfo, LayerInfoData, CompareDataType, DataInfo
from models.sheet_models import DetectedTableResult
from utils.number import compare_arrays


def float_differs(a: Union[float, None], b: Union[float, None], tol: float = 0.01) -> bool:
    if a is None and b is None:
        return False
    if a is None or b is None:
        return True
    return abs(a - b) > tol


def get_data_source(data: List[DataInfo]):
    for d in data:
        if d.number and d.data_type:
            return d.data_type
    return DataSourceType.UNDEFINED


class DrillInfoExtractor:

    @staticmethod
    def get_by_plane(plane_results: List[DetectedPlainResult]) -> List[DrillHoleInfo]:
        """
        获取平面图中的所有钻孔信息

        Args:
            plane_results: 平面图结果列表

        Returns:
            包含所有钻孔信息
        """
        drill_infos = []

        for plane_result in plane_results:
            for drill in plane_result.drills:
                if drill.binding_info and drill.binding_info.drill_number:
                    real_coordinate = drill.real_coordinate
                    drill_infos.append(DrillHoleInfo(
                        page_info=plane_result.file_name,
                        data_type=DataSourceType.PLANE,
                        number=drill.binding_info.drill_number if drill.binding_info else "未知编号",
                        # hole_type=drill.hole_type,
                        surface_elevation=drill.binding_info.drill_elevation if drill.binding_info else None,
                        depth=drill.binding_info.drill_depth if drill.binding_info else None,
                        x_coordinate=real_coordinate.x if real_coordinate else None,
                        y_coordinate=real_coordinate.y if real_coordinate else None,
                    ))

        return sorted(drill_infos, key=lambda x: x.number)

    @staticmethod
    def get_by_section(section_results: List[DetectedSectionResult]) -> List[DrillHoleInfo]:
        """
        获取剖面图中的所有钻孔信息

        Args:
            section_results: 剖面图结果列表

        Returns:
            包含所有钻孔信息
        """
        drill_infos = []

        for section_result in section_results:
            for section in section_result.sections:
                for drill_hole in section.drill_holes:
                    if drill_hole.drill_name:
                        drill_infos.append(DrillHoleInfo(
                            page_info=section_result.file_name,
                            data_type=DataSourceType.SECTION,
                            number=drill_hole.drill_name,
                            hole_type=None,
                            surface_elevation=drill_hole.surface_elevation,
                            depth=drill_hole.depth_elevation_pairs[-1].depth if drill_hole.depth_elevation_pairs else None,
                        ))

        return sorted(drill_infos, key=lambda x: x.number)

    @staticmethod
    def get_by_table(table_results: List[DetectedTableResult], table_type: DataSourceType) -> List[DrillHoleInfo]:
        drill_infos = []
        for table_result in table_results:
            if not table_result.table_type:
                continue
            drill_info = (ComplexTableVisionComputer
                          .get_drill_hole_info(table_result.file_name, table_type.value, table_result.roi_ocr_result))
            drill_infos.extend(drill_info)

        return drill_infos


class LayerInfoExtractor:

    @staticmethod
    def get_by_section(section_results: List[DetectedSectionResult]) -> List[LayerInfoData]:
        """
        获取剖面图中的所有层级信息

        Args:
            section_results: 剖面图结果列表

        Returns:
            包含所有钻孔信息
        """
        layer_infos = []

        for section_result in section_results:
            for section in section_result.sections:
                for drill_hole in section.drill_holes:
                    if drill_hole.drill_name and drill_hole.depth_elevation_pairs:
                        for idx, pair in enumerate(drill_hole.depth_elevation_pairs):
                            layer_infos.append(LayerInfoData(
                                page_info=section_result.file_name,
                                data_type=DataSourceType.SECTION,
                                drill_id=drill_hole.drill_name,
                                depth=pair.depth,
                                elevation=pair.elevation
                            ))

        return sorted(layer_infos, key=lambda x: x.drill_id)

    @staticmethod
    def get_by_table(table_results: List[DetectedTableResult], table_type: DataSourceType) -> List[DrillHoleInfo]:
        layer_infos = []
        for table_result in table_results:
            if table_result == None or table_result.table_type == DataSourceType.UNDEFINED.value:
                continue
            layer_info = ComplexTableVisionComputer.get_layer_info(table_result.file_name, table_type.value,
                                                                   table_result.roi_ocr_result, table_result.vision_results)
            layer_infos.extend(layer_info)

        return layer_infos


def compare_drill_spacings(section_results: List[DetectedSectionResult],
                           plane_results: List[DetectedPlainResult]) -> List[CheckingReport]:
    """
    对比剖面图和平面图中的钻孔间距，直接返回检查报告

    Args:
        section_results: 剖面图结果列表
        plane_results: 平面图结果列表

    Returns:
        钻孔间距对比检查报告列表
    """
    check_point_list = []

    # 构建平面图钻孔间距的映射 {(drill1_name, drill2_name): (spacing_value, page_number)}
    plane_spacing_map = {}
    for plane_result in plane_results:
        for spacing in plane_result.drill_spacings:
            if spacing.drill1.binding_info and spacing.drill2.binding_info:
                drill1_name = spacing.drill1.binding_info.drill_number
                drill2_name = spacing.drill2.binding_info.drill_number
                # 确保钻孔对的一致性（按字母顺序排列）
                drill_pair = tuple(sorted([drill1_name, drill2_name]))
                plane_spacing_map[drill_pair] = (spacing.plane_spacing, plane_result.file_name)

    # 遍历剖面图结果，与平面图进行对比
    for section_result in section_results:
        for spacing_with_drill in section_result.spacing_with_drill_holes:
            if not spacing_with_drill.drill_hole1 or not spacing_with_drill.drill_hole2 or not spacing_with_drill.section_spacing:
                continue
            drill1_name = spacing_with_drill.drill_hole1.drill_name
            drill2_name = spacing_with_drill.drill_hole2.drill_name

            # 确保钻孔对的一致性（按字母顺序排列）
            drill_pair = tuple(sorted([drill1_name, drill2_name]))
            section_spacing = spacing_with_drill.section_spacing.value

            # 检查平面图中是否存在对应的钻孔对
            if drill_pair in plane_spacing_map:
                plane_spacing, plane_page = plane_spacing_map[drill_pair]
                difference = abs(section_spacing - plane_spacing)
                has_issue = abs(difference) > 0.5

                issue_desc = "存在不一致问题" if has_issue else "无问题"
                result_text = f"钻孔 {drill1_name} 与钻孔 {drill2_name} 间距对比: " \
                             f"剖面图中为 {section_spacing}米, 平面图中为 {plane_spacing}米"
                
                if has_issue:
                    result_text += f", 差值为 {difference}米, {issue_desc}"
                else:
                    result_text += f", {issue_desc}"

                report = CheckingReport(
                    name=CompareDataType.HOLE_SPACE,
                    result=result_text,
                    is_valid=not has_issue,
                    source=DataSourceType.SECTION,
                    source_page=section_result.file_name,
                    reference=DataSourceType.PLANE,
                    reference_page=plane_page,
                    usage="确认：工作量的合理性。",
                    original_value=f"间距: {section_spacing}",
                    compared_value=f"间距: {plane_spacing}",
                    comments=""
                )
                check_point_list.append(report)
            else:
                result_text = f"钻孔 {drill1_name} 与钻孔 {drill2_name} 间距对比: " \
                             f"剖面图中为 {section_spacing}米, 存在不一致问题 (未找到对应的平面图钻孔对)"

                report = CheckingReport(
                    name=CompareDataType.HOLE_SPACE,
                    result=result_text,
                    is_valid=False,
                    source=DataSourceType.SECTION,
                    source_page=section_result.file_name,
                    reference=DataSourceType.PLANE,
                    reference_page=None,
                    usage="确认：工作量的合理性。",
                    original_value=f"间距: {section_spacing}",
                    compared_value="无数据",
                    comments="未找到对应的平面图钻孔对"
                )
                check_point_list.append(report)

    return check_point_list


def compare_drill_infos(
        src1: List[DrillHoleInfo], src2: List[DrillHoleInfo],
        validate_fields: List[str] | None = None
) -> List[CheckingReport]:
    """
    对比两个钻孔信息列表，找出不一致的地方

    Args:
        src1: 第一个钻孔信息列表
        src2: 第二个钻孔信息列表
        validate_fields: 需要校验的字段列表，默认校验全部字段

    Returns:
        不一致的描述列表
    """
    issues = []

    # 默认校验全部字段
    if validate_fields is None:
        validate_fields = [
            "hole_type",
            "surface_elevation",
            "depth",
            "x_coordinate",
            "y_coordinate",
        ]

    # 索引到字典，按钻孔编号比对
    src1_map = {d.number: d for d in src1 if d.number}
    src2_map = {d.number: d for d in src2 if d.number}

    data_source = get_data_source(src1)
    data_comparison = get_data_source(src2)

    all_numbers = sorted(set(src1_map.keys()) | set(src2_map.keys()))

    for number in all_numbers:

        d1 = src1_map.get(number)
        d2 = src2_map.get(number)

        if d1 is None:
            issues.append(CheckingReport(
                name="勘探孔信息",
                result=f"在源数据1中缺少钻孔 {number}，但在源数据2中存在",
                is_valid=False,
                source=data_source,
                source_page=None,
                reference=data_comparison,
                reference_page=d2.page_info,
                usage="确认：钻孔信息一致性。",
                original_value="缺少",
                compared_value=str(d2.model_dump_json()),
                comments="编号缺失",
            ))
            continue

        if d2 is None:
            issues.append(CheckingReport(
                name="勘探孔信息",
                result=f"在源数据2中缺少钻孔 {number}，但在源数据1中存在",
                is_valid=False,
                source=data_source,
                source_page=d1.page_info,
                reference=data_comparison,
                reference_page=None,
                usage="确认：钻孔信息一致性。",
                original_value=str(d1.model_dump_json()),
                compared_value="缺少",
                comments="编号缺失"
            ))
            continue

        # 字段比对（合并为单条记录，并包含一致与不一致）
        comparisons = []
        any_issue = False

        def add_comp(name: str, v1, v2, equal: bool, remark: str = ""):
            nonlocal any_issue
            status = "一致" if equal else "不一致"
            if not equal:
                any_issue = True
            comparisons.append(f"{name}: 源1={v1}，源2={v2}（{status}{('，' + remark) if remark else ''}）")

        # 1) 孔口类型
        if "hole_type" in validate_fields:
            equal = (d1.hole_type or None) == (d2.hole_type or None)
            add_comp("孔口类型", d1.hole_type, d2.hole_type, equal)

        # 2) 孔口标高
        if "surface_elevation" in validate_fields:
            differs = float_differs(d1.surface_elevation, d2.surface_elevation)
            add_comp("孔口标高(m)", d1.surface_elevation, d2.surface_elevation, not differs, "容差0.01m")

        # 3) 钻孔深度
        if "depth" in validate_fields:
            differs = float_differs(d1.depth, d2.depth)
            add_comp("钻孔深度(m)", d1.depth, d2.depth, not differs, "容差0.01m")

        # 4) 坐标（如果两侧至少一侧有值则进行展示与比较）
        if "x_coordinate" in validate_fields and (d1.x_coordinate is not None or d2.x_coordinate is not None):
            differs = float_differs(d1.x_coordinate, d2.x_coordinate)
            add_comp("X坐标", d1.x_coordinate, d2.x_coordinate, not differs, "容差0.01")

        if "y_coordinate" in validate_fields and (d1.y_coordinate is not None or d2.y_coordinate is not None):
            differs = float_differs(d1.y_coordinate, d2.y_coordinate)
            add_comp("Y坐标", d1.y_coordinate, d2.y_coordinate, not differs, "容差0.01")

        # 生成合并后的报告
        if comparisons:
            result_text = "；".join(comparisons)
            issues.append(CheckingReport(
                name="勘探孔信息",
                result=f"钻孔 {number} 对比结果：{result_text}",
                is_valid=not any_issue,
                source=data_source,
                source_page=d1.page_info,
                reference=data_comparison,
                reference_page=d2.page_info,
                usage="确认：钻孔信息一致性。",
                original_value=str(d1.model_dump_json()),
                compared_value=str(d2.model_dump_json()),
                comments=""
            ))

    return issues


def compare_section_layer_infos(layer_infos: List[LayerInfoData]) -> List[CheckingReport]:
    """
    对比分层信息的准确性（仅针对剖面图内部的不同页面之间的同一钻孔）。

    规则：
    1. 对同一钻孔号，先比较其在不同剖面页面中的分层条目数量是否一致；
       - 若不一致，则直接判为不一致并给出报告；
    2. 若数量一致，再逐条对比深度/高程是否一致（含容差判断）。

    返回：逐个页面两两对比的检查报告列表。
    """
    if not layer_infos:
        return []

    reports: List[CheckingReport] = []

    # 仅考虑剖面图来源
    section_layers = [li for li in layer_infos if li and li.data_type == DataSourceType.SECTION]
    if not section_layers:
        return reports

    # 归集：drill_id -> page_info -> List[(depth, elevation)]
    drill_page_to_pairs: dict[str, dict[str, List[tuple[float | None, float | None]]]] = {}
    for li in section_layers:
        drill_map = drill_page_to_pairs.setdefault(li.drill_id, {})
        pairs = drill_map.setdefault(li.page_info, [])
        pairs.append((li.depth, li.elevation))

    extra_error_comments = "剖面数据存在异常，不再进行不同来源数据的对比，请人工矫正数据"

    # 对每个钻孔进行页面两两对比
    for drill_id, page_map in drill_page_to_pairs.items():
        pages = sorted(page_map.keys())
        # 仅当同一钻孔出现在至少两个页面时才进行对比
        if len(pages) < 2:
            continue

        page_to_sorted = {p: page_map[p] for p in pages}

        for i in range(len(pages)):
            for j in range(i + 1, len(pages)):
                p1, p2 = pages[i], pages[j]
                seq1 = page_to_sorted[p1]
                seq2 = page_to_sorted[p2]

                # 先对比分层数量
                if len(seq1) != len(seq2):
                    status = (
                        f"钻孔 {drill_id} 分层数量对比：{p1}为 {len(seq1)} 条，{p2}为 {len(seq2)} 条，数量存在不一致问题"
                    )
                    reports.append(CheckingReport(
                        name=CompareDataType.LAYER_INFO,
                        result=status,
                        is_valid=False,
                        source=DataSourceType.SECTION,
                        source_page=p1,
                        reference=DataSourceType.SECTION,
                        reference_page=p2,
                        usage="确认：分层信息的一致性。",
                        original_value=str(seq1),
                        compared_value=str(seq2),
                        comments=f"数量不一致，{extra_error_comments}"
                    ))
                    continue

                # 数量一致，逐项对比深度/高程
                mismatches: List[str] = []
                for idx, ((d1, e1), (d2, e2)) in enumerate(zip(seq1, seq2)):
                    depth_diff = float_differs(d1, d2)
                    elev_diff = float_differs(e1, e2)
                    if depth_diff or elev_diff:
                        parts = []
                        if depth_diff:
                            parts.append(f"深度不一致: {d1} vs {d2} (容差0.01m)")
                        if elev_diff:
                            parts.append(f"高程不一致: {e1} vs {e2} (容差0.01m)")
                        mismatches.append(f"第{idx + 1}层（按深度序）: " + "，".join(parts))

                if mismatches:
                    detail = "；".join(mismatches)
                    status = (
                        f"钻孔 {drill_id} 分层数据对比：数量一致，共 {len(seq1)} 条，但存在不一致；{detail}"
                    )
                    reports.append(CheckingReport(
                        name=CompareDataType.LAYER_INFO,
                        result=status,
                        is_valid=False,
                        source=DataSourceType.SECTION,
                        source_page=p1,
                        reference=DataSourceType.SECTION,
                        reference_page=p2,
                        usage="确认：分层信息的一致性。",
                        original_value=str(seq1),
                        compared_value=str(seq2),
                        comments=f"数值不一致，{extra_error_comments}"
                    ))
                else:
                    status = (
                        f"钻孔 {drill_id} 分层数据对比：数量一致，共 {len(seq1)} 条，数据一致，无问题"
                    )
                    reports.append(CheckingReport(
                        name=CompareDataType.LAYER_INFO,
                        result=status,
                        is_valid=True,
                        source=DataSourceType.SECTION,
                        source_page=p1,
                        reference=DataSourceType.SECTION,
                        reference_page=p2,
                        usage="确认：分层信息的一致性。",
                        original_value=str(seq1),
                        compared_value=str(seq2),
                        comments=""
                    ))

    return reports


def compare_layer_infos(
    src1: List[LayerInfoData], src2: List[LayerInfoData],
    is_data_integrity: bool = True,
    validate_fields: List[str] | None = None,
) -> List[CheckingReport]:
    """
    对比两个层级信息列表（按钻孔编号进行对比，不使用层级索引）。

    - 当 is_data_integrity=True：src1 与 src2 都应包含全部钻孔编号；任一侧缺失则报问题。
    - 当 is_data_integrity=False：src1 数据完整，src2 可能是子集；
        · 若 src1 有而 src2 无：跳过；
        · 若 src2 有而 src1 无：判定为误识别并报问题。

    深度与高程以 (depth, elevation) 成对序列进行对比，使用 compare_arrays。
    若需要仅对比部分字段，可通过 validate_fields 指定 ["depth"], ["elevation"] 或两者。
    """
    issues: List[CheckingReport] = []

    # 解析需要校验的字段
    allowed_fields = {"depth", "elevation"}
    if validate_fields is None:
        selected_fields = ["depth", "elevation"]
    else:
        selected_fields = [f for f in validate_fields if f in allowed_fields]
        if not selected_fields:
            selected_fields = ["depth", "elevation"]

    # 构建 drill_id -> [(depth, elevation)] 的映射，同时记录示例页码以用于报告
    def build_map(items: List[LayerInfoData]):
        drill_to_pairs = {}
        drill_to_page = {}
        data_type = DataSourceType.UNDEFINED if not items else items[0].data_type
        for it in items:
            if not it or not it.drill_id:
                continue
            pairs = drill_to_pairs.setdefault(it.drill_id, [])
            pair = (it.depth, it.elevation)
            # 避免重复添加相同的深度高程对
            if pair not in pairs:
                pairs.append(pair)
            if it.drill_id not in drill_to_page:
                drill_to_page[it.drill_id] = it.page_info
        return drill_to_pairs, drill_to_page, data_type

    src1_pairs_map, src1_page_map, data_source = build_map(src1)
    src2_pairs_map, src2_page_map, data_comparison = build_map(src2)

    all_drill_ids = sorted(set(src1_pairs_map.keys()) | set(src2_pairs_map.keys()))

    # 归一化：根据选择的字段进行组装与排序，并四舍五入减少抖动
    def normalize_pairs(pairs: List[tuple[float | None, float | None]]):
        if not pairs:
            return []

        def round_opt(x):
            return None if x is None else round(float(x), 2)

        # 使用深度进行排序（若可用）。当只对比高程时仍沿用深度排序策略以稳定顺序；若所有深度为 None 则保持原始顺序。
        has_none_depth = any(p[0] is None for p in pairs)
        ordered = pairs if has_none_depth else sorted(pairs, key=lambda p: (p[0] is None, p[0]))

        if set(selected_fields) == {"depth", "elevation"}:
            return [(round_opt(d), round_opt(e)) for d, e in ordered]
        elif selected_fields == ["depth"]:
            return [round_opt(d) for d, _ in ordered]
        elif selected_fields == ["elevation"]:
            return [round_opt(e) for _, e in ordered]
        else:
            # 理论上不会到达，这里回退为双字段
            return [(round_opt(d), round_opt(e)) for d, e in ordered]

    for drill_id in all_drill_ids:
        in1 = drill_id in src1_pairs_map
        in2 = drill_id in src2_pairs_map

        if is_data_integrity:
            if not in1 and in2:
                issues.append(CheckingReport(
                    name=CompareDataType.LAYER_INFO,
                    result=f"在源数据1中缺少钻孔 {drill_id} 的层级数据，但在源数据2中存在",
                    is_valid=False,
                    source=data_source,
                    source_page=None,
                    reference=data_comparison,
                    reference_page=src2_page_map.get(drill_id),
                    usage="确认：层级信息一致性。",
                    original_value="缺少",
                    compared_value=str(src2_pairs_map.get(drill_id)),
                    comments="钻孔缺失"
                ))
                continue
            if in1 and not in2:
                issues.append(CheckingReport(
                    name=CompareDataType.LAYER_INFO,
                    result=f"在源数据2中缺少钻孔 {drill_id} 的层级数据，但在源数据1中存在",
                    is_valid=False,
                    source=data_source,
                    source_page=src1_page_map.get(drill_id),
                    reference=data_comparison,
                    reference_page=None,
                    usage="确认：层级信息一致性。",
                    original_value=str(src1_pairs_map.get(drill_id)),
                    compared_value="缺少",
                    comments="钻孔缺失"
                ))
                continue
            if not in1 and not in2:
                continue
        else:
            # src1 完整，src2 可能是子集
            if not in1 and in2:
                issues.append(CheckingReport(
                    name=CompareDataType.LAYER_INFO,
                    result=f"钻孔 {drill_id} 仅出现在源数据2，疑似误识别",
                    is_valid=False,
                    source=data_source,
                    source_page=None,
                    reference=data_comparison,
                    reference_page=src2_page_map.get(drill_id),
                    usage="确认：层级信息一致性。",
                    original_value="缺少",
                    compared_value=str(src2_pairs_map.get(drill_id)),
                    comments="疑似误识别"
                ))
                continue
            if in1 and not in2:
                # 允许跳过
                continue
            if not in1 and not in2:
                continue

        # 双方均存在该钻孔，进行序列对比
        seq1 = normalize_pairs(src1_pairs_map.get(drill_id, []))
        seq2 = normalize_pairs(src2_pairs_map.get(drill_id, []))

        diffs = compare_arrays(seq1, seq2)

        if not diffs:
            issues.append(CheckingReport(
                name=CompareDataType.LAYER_INFO,
                result=f"钻孔 {drill_id} 分层对比：一致，无问题（共{len(seq1)}条）",
                is_valid=True,
                source=data_source,
                source_page=src1_page_map.get(drill_id),
                reference=data_comparison,
                reference_page=src2_page_map.get(drill_id),
                usage="确认：层级信息一致性。",
                original_value=str(seq1),
                compared_value=str(seq2),
                comments=""
            ))
        else:
            parts = []
            for diff in diffs:
                if not diff:
                    continue
                if diff[0] == "missing_in_B" and len(diff) >= 2:
                    parts.append(f"源2缺少{diff[1]}")
                elif diff[0] == "missing_in_A" and len(diff) >= 2:
                    parts.append(f"源1缺少{diff[1]}")
                elif diff[0] == "mismatch" and len(diff) >= 3:
                    parts.append(f"不匹配 {diff[1]} vs {diff[2]}")
            detail = ";\n".join(parts) if parts else "存在不一致"
            issues.append(CheckingReport(
                name=CompareDataType.LAYER_INFO,
                result=f"钻孔 {drill_id} 分层对比：\n{detail}",
                is_valid=False,
                source=data_source,
                source_page=src1_page_map.get(drill_id),
                reference=data_comparison,
                reference_page=src2_page_map.get(drill_id),
                usage="确认：层级信息一致性。",
                original_value=str(seq1),
                compared_value=str(seq2),
                comments=""
            ))

    return issues


def investigate_hole_info(
        plane_results,
        section_results,
        table_results: List[DetectedTableResult],
):
    table_infos_map = {
        DataSourceType.PLANE: DrillInfoExtractor.get_by_plane(plane_results),
        DataSourceType.SECTION: DrillInfoExtractor.get_by_section(section_results),
        DataSourceType.DRILL_BAR_CHART: DrillInfoExtractor.get_by_table(table_results, DataSourceType.DRILL_BAR_CHART),
        DataSourceType.STATIC_PENETRATION_LAYER: DrillInfoExtractor.get_by_table(table_results, DataSourceType.STATIC_PENETRATION_LAYER),
        DataSourceType.STATIC_PENETRATION_TEST: DrillInfoExtractor.get_by_table(table_results, DataSourceType.STATIC_PENETRATION_TEST),
    }

    required_fields_map = {
        DataSourceType.PLANE: ["number", "surface_elevation", "depth", "x_coordinate", "y_coordinate"],
        DataSourceType.SECTION: ["number", "surface_elevation", "depth"],
        DataSourceType.DRILL_BAR_CHART: ["number", "surface_elevation", "depth", "x_coordinate", "y_coordinate"],
        DataSourceType.STATIC_PENETRATION_LAYER: ["number", "surface_elevation"],
        DataSourceType.STATIC_PENETRATION_TEST: ["number", "surface_elevation", "depth", "x_coordinate", "y_coordinate"],
    }

    all_sources = [(k, v) for k, v in table_infos_map.items()]

    issues = []

    for i in range(len(all_sources)):
        name1, infos1 = all_sources[i]
        fields1 = set(required_fields_map.get(name1, []))
        for j in range(i + 1, len(all_sources)):
            name2, infos2 = all_sources[j]
            fields2 = set(required_fields_map.get(name2, []))

            # 动态交集，只校验双方都有的字段
            validate_fields = list(fields1 & fields2)

            if validate_fields:
                issues += compare_drill_infos(infos1, infos2, validate_fields=validate_fields)
            else:
                issues += compare_drill_infos(infos1, infos2)

    return issues


def investigate_layer_info(section_results, table_results: List[DetectedTableResult]):
    table_results_penetration, table_results_test, table_results_bar = [], [], []
    for table_result in table_results:
        if table_result == None or table_result.table_type == DataSourceType.UNDEFINED.value:
            continue
        elif table_result.table_type == DataSourceType.STATIC_PENETRATION_LAYER.value:
            table_results_penetration.append(table_result)
        elif table_result.table_type == DataSourceType.STATIC_PENETRATION_TEST.value:
            table_results_test.append(table_result)
        elif table_result.table_type == DataSourceType.DRILL_BAR_CHART.value:
            table_results_bar.append(table_result)
    layer_info_map = {
        DataSourceType.SECTION: LayerInfoExtractor.get_by_section(section_results),
        DataSourceType.DRILL_BAR_CHART: LayerInfoExtractor.get_by_table(table_results_bar, DataSourceType.DRILL_BAR_CHART),
        DataSourceType.STATIC_PENETRATION_LAYER: LayerInfoExtractor.get_by_table(table_results_penetration,
                                                                            DataSourceType.STATIC_PENETRATION_LAYER),
        DataSourceType.STATIC_PENETRATION_TEST: LayerInfoExtractor.get_by_table(table_results_test,
                                                                           DataSourceType.STATIC_PENETRATION_TEST),
    }

    issues = []
    issues += compare_section_layer_infos(layer_info_map[DataSourceType.SECTION])

    # 剖面检测不通过，则不再进行审查
    if len([issue for issue in issues if not issue.is_valid]) > 0:
        logger.warning("剖面检测不通过，不再进行审查表格数据")
        return issues

    static_penetration_layer_drill_holes = {li.drill_id for li in layer_info_map[DataSourceType.STATIC_PENETRATION_LAYER] if li and li.drill_id}
    exclude_spl_section_layer_info = [li for li in layer_info_map[DataSourceType.SECTION] if li and li.drill_id not in static_penetration_layer_drill_holes]

    issues += compare_layer_infos(layer_info_map[DataSourceType.SECTION], layer_info_map[DataSourceType.STATIC_PENETRATION_LAYER], is_data_integrity=False, validate_fields=["depth"])
    issues += compare_layer_infos(layer_info_map[DataSourceType.SECTION], layer_info_map[DataSourceType.STATIC_PENETRATION_TEST], is_data_integrity=False)
    issues += compare_layer_infos(layer_info_map[DataSourceType.DRILL_BAR_CHART], exclude_spl_section_layer_info, is_data_integrity=False, validate_fields=["depth"])
    issues += compare_layer_infos(layer_info_map[DataSourceType.STATIC_PENETRATION_TEST], layer_info_map[DataSourceType.STATIC_PENETRATION_LAYER], is_data_integrity=True, validate_fields=["depth"])

    return issues


def flatten_layer_infos(infos):
    """
    将 infos 中的各种 {}, [], [LayerInfoData...]
    统一扁平化为 List[LayerInfoData]
    """
    if not infos:
        return []

    result = []
    for elem in infos:
        if isinstance(elem, list):
            result.extend(x for x in elem if isinstance(x, LayerInfoData))
        elif isinstance(elem, LayerInfoData):
            result.append(elem)
    return result