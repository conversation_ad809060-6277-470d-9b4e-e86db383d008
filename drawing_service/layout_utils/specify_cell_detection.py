from typing import List, Optional, TypeVar, Type
import numpy as np

from utils.grouper import Grouper
from vision.core.data_types import BoundingBox
from itertools import combinations

from vision.core.utils import non_maximum_suppression

# 定义泛型类型，支持BoundingBox的子类
T = TypeVar('T', bound=BoundingBox)


def detect_left_merged_cell_groups(bboxes: List[T],
                                   height_tolerance: float = 10.0,
                                   width_tolerance: float = 10.0,
                                   min_height_ratio: float = 2.5) -> List[T]:
    """
    检测【左侧纵向合并大单元格 + 右侧三个小单元格】的组合，返回左侧大单元格的BoundingBox对象

    Args:
        bboxes: BoundingBox对象列表（支持子类）
        height_tolerance: 判断单元格是否垂直对齐的容差
        width_tolerance: 判断单元格是否水平对齐的容差
        min_height_ratio: 大单元格与小单元格的最小高度比例

    Returns:
        左侧纵向合并大单元格的BoundingBox对象列表
    """
    if len(bboxes) < 4:  # 至少需要4个单元格（1个大+3个小）
        return []

    # 过滤出有效的BoundingBox
    valid_bboxes = [bbox for bbox in bboxes if bbox.is_valid()]

    if len(valid_bboxes) < 4:
        return []

    merged_cells = []

    # 遍历每个单元格，检查是否为左侧大单元格
    for candidate in valid_bboxes:
        # 寻找右侧的单元格
        right_cells = []

        for other in valid_bboxes:
            if other is candidate:  # 使用对象身份比较
                continue

            # 检查是否在右侧（左边界大于候选单元格的右边界）
            if other.x1 >= candidate.x2 - width_tolerance:
                # 检查垂直方向是否有重叠
                if candidate.y_overlap_ratio(other) > 0:
                    right_cells.append(other)

        # 如果右侧没有找到足够的单元格，跳过
        if len(right_cells) < 3:
            continue

        # 寻找最近的3个右侧单元格
        closest_three_cells = find_closest_three_cells(candidate, right_cells)

        if closest_three_cells:
            # 验证这是一个有效的组合
            if is_valid_group_combination(candidate, closest_three_cells, min_height_ratio, height_tolerance):
                merged_cells.append(candidate)

    # 去重（避免同一个单元格被多次检测）
    return remove_duplicate_bboxes(merged_cells)


def find_closest_three_cells(left_cell: BoundingBox, right_cells: List[BoundingBox]) -> Optional[List[BoundingBox]]:
    """
    找到与左侧单元格最近的3个右侧单元格
    优先考虑水平距离，然后考虑垂直对齐程度
    """
    if len(right_cells) < 3:
        return None

    # 如果正好3个，直接返回
    if len(right_cells) == 3:
        return sorted(right_cells, key=lambda x: x.y1)

    # 计算每个右侧单元格与左侧单元格的距离分数
    cell_scores = []

    for cell in right_cells:
        # 水平距离（主要因素）
        horizontal_distance = cell.x1 - left_cell.x2

        # 垂直对齐程度（次要因素）
        # 计算中心点的垂直距离
        vertical_center_distance = abs(cell.center_y - left_cell.center_y)

        # 综合分数：水平距离权重更大
        score = horizontal_distance + vertical_center_distance * 0.5

        cell_scores.append((cell, score))

    # 按分数排序，取前3个最近的
    cell_scores.sort(key=lambda x: x[1])
    closest_three = [item[0] for item in cell_scores[:3]]

    # 按y坐标排序返回
    return sorted(closest_three, key=lambda x: x.y1)


def is_valid_group_combination(left_cell: BoundingBox, three_cells: List[BoundingBox],
                               min_height_ratio: float, height_tolerance: float) -> bool:
    """
    验证左侧大单元格和右侧三个小单元格是否构成有效组合
    """
    # 按y坐标排序
    three_cells = sorted(three_cells, key=lambda x: x.y1)

    # 1. 检查左侧单元格是否足够高（相对于右侧单元格）
    avg_right_height = np.mean([cell.height for cell in three_cells])
    if left_cell.height < avg_right_height * min_height_ratio:
        return False

    # 2. 检查三个右侧单元格是否垂直相邻且有序排列
    for i in range(len(three_cells) - 1):
        gap = three_cells[i + 1].y1 - three_cells[i].y2
        if gap > height_tolerance:  # 间隔太大
            return False
        if gap < -height_tolerance:  # 重叠太多
            return False

    # 3. 检查左侧单元格的垂直范围是否大致覆盖右侧三个单元格
    right_top = three_cells[0].y1
    right_bottom = three_cells[-1].y2

    # 允许一定的误差
    top_diff = abs(left_cell.y1 - right_top)
    bottom_diff = abs(left_cell.y2 - right_bottom)

    # 放宽容差，因为合并单元格可能不完全对齐
    max_allowed_diff = height_tolerance * 3
    if top_diff > max_allowed_diff or bottom_diff > max_allowed_diff:
        return False

    # 4. 检查右侧三个单元格的宽度是否相似（应该是同一列）
    widths = [cell.width for cell in three_cells]
    if len(set(widths)) > 1:  # 有不同的宽度
        width_std = np.std(widths)
        avg_width = np.mean(widths)
        if avg_width > 0 and width_std > avg_width * 0.4:  # 放宽到40%
            return False

    # 5. 检查右侧三个单元格的x坐标是否大致对齐
    x1_coords = [cell.x1 for cell in three_cells]
    x2_coords = [cell.x2 for cell in three_cells]

    x1_std = np.std(x1_coords)
    x2_std = np.std(x2_coords)

    # 放宽对齐要求
    alignment_tolerance = height_tolerance * 2
    if x1_std > alignment_tolerance or x2_std > alignment_tolerance:
        return False

    # 6. 检查右侧三个单元格是否确实组成了一个连续的垂直序列
    total_right_height = three_cells[-1].y2 - three_cells[0].y1
    individual_heights_sum = sum(cell.height for cell in three_cells)

    # 如果总高度与各个高度之和相差太大，说明有较大间隔或重叠
    height_consistency = abs(total_right_height - individual_heights_sum)
    if height_consistency > height_tolerance * len(three_cells):
        return False

    return True


def remove_duplicate_bboxes(bboxes: List[T], tolerance: float = 1.0) -> List[T]:
    """
    移除重复的BoundingBox对象
    """
    if not bboxes:
        return []

    unique_bboxes = []

    for bbox in bboxes:
        is_duplicate = False
        for existing_bbox in unique_bboxes:
            # 检查是否是同一个bbox（在容差范围内）
            if (abs(bbox.x1 - existing_bbox.x1) < tolerance and
                    abs(bbox.y1 - existing_bbox.y1) < tolerance and
                    abs(bbox.x2 - existing_bbox.x2) < tolerance and
                    abs(bbox.y2 - existing_bbox.y2) < tolerance):
                is_duplicate = True
                break

        if not is_duplicate:
            unique_bboxes.append(bbox)

    return unique_bboxes


def visualize_detected_groups(all_bboxes: List[BoundingBox],
                              detected_left_cells: List[BoundingBox]):
    """
    可视化检测结果（可选，需要matplotlib）
    """
    try:
        import matplotlib.pyplot as plt
        import matplotlib.patches as patches

        fig, ax = plt.subplots(1, 1, figsize=(12, 8))

        # 绘制所有单元格
        for bbox in all_bboxes:
            if bbox.is_valid():
                rect = patches.Rectangle((bbox.x1, bbox.y1), bbox.width, bbox.height,
                                         linewidth=1, edgecolor='gray', facecolor='lightblue', alpha=0.3)
                ax.add_patch(rect)

        # 高亮检测到的左侧大单元格
        for bbox in detected_left_cells:
            if bbox.is_valid():
                rect = patches.Rectangle((bbox.x1, bbox.y1), bbox.width, bbox.height,
                                         linewidth=3, edgecolor='red', facecolor='yellow', alpha=0.6)
                ax.add_patch(rect)

        # 设置坐标轴
        valid_bboxes = [bbox for bbox in all_bboxes if bbox.is_valid()]
        if valid_bboxes:
            all_x = [coord for bbox in valid_bboxes for coord in [bbox.x1, bbox.x2]]
            all_y = [coord for bbox in valid_bboxes for coord in [bbox.y1, bbox.y2]]

            ax.set_xlim(min(all_x) - 10, max(all_x) + 10)
            ax.set_ylim(min(all_y) - 10, max(all_y) + 10)
            ax.invert_yaxis()  # 翻转y轴，使原点在左上角
            ax.set_aspect('equal')
            ax.grid(True, alpha=0.3)
            ax.set_title(f'检测到 {len(detected_left_cells)} 个左侧纵向合并大单元格')

        plt.tight_layout()
        plt.show()

    except ImportError:
        print("matplotlib未安装，跳过可视化")


def detect_merge_cells(table_cells: List[BoundingBox], target_cell, sub_cell_cnt = 3) -> List[BoundingBox]:
    content_cells = [cell for cell in table_cells if
                     cell.center_x >= target_cell.x_max and cell.center_y >= target_cell.y_max]

    # 统计content_cells中所有框的高度
    heights = [cell.height for cell in content_cells]

    # 使用Grouper按相似高度分组
    grouper = Grouper(tolerance=0.1)  # 10%的容差
    height_groups = grouper.simple_group(heights, lambda x: x)

    # 计算每组框高度的平均值
    grouped_heights = [[heights[i] for i in group] for group in height_groups]
    avg_heights = [sum(group) / len(group) for group in grouped_heights]

    # 找到最小框组的高度
    min_group_height = min(avg_heights)

    big_cells = []

    for cell in content_cells:
        # 如果高度除以3与最小框组的高度差不多，则放入big_cells
        if abs(cell.height / sub_cell_cnt - min_group_height) <= min_group_height * 0.3:  # 10%容差
            big_cells.append(cell)

    return big_cells


# 使用示例
if __name__ == "__main__":
    import pickle

    with open("detections.pkl", "rb") as f:
        detections = pickle.load(f)

    # 检测左侧纵向合并大单元格
    detections = non_maximum_suppression(detections)
    detected_cells = detect_left_merged_cell_groups(detections)

    print(f"检测到 {len(detected_cells)} 个左侧纵向合并大单元格:")
    for i, bbox in enumerate(detected_cells, 1):
        print(f"  大单元格 {i}: {bbox}")

    # 可视化结果（如果安装了matplotlib）
    visualize_detected_groups(detections, detected_cells)