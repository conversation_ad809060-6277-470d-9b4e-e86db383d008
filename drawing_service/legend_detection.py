from typing import Union, Any, List

from loguru import logger

from drawing_service.base import Detector<PERSON><PERSON><PERSON>, VisionComputer
from models.base import VisionDetectedResult
from models.legend_models import DetectedLegendResult, LegendDetail
from models.pdf_info_models import PdfPageType
from vision.core import <PERSON><PERSON><PERSON><PERSON>, DetectionResult
from vision.core.data_types import BoundingBox
from vision.core.utils import crop_bboxes
from vision.cv_det.rect import extract_rectangle
from vision.obj_det import YoloModelType
from vision.obj_det.yolo_holder import LegendElemType
from vision.ocr_engine.base import OcrResult
from vision.ocr_engine.engine import OcrModelType, OcrEngine


class LegendVisionComputer(VisionComputer, DetectorHelper):
    """图例区域检测器"""

    def __init__(
            self,
            ocr_model: OcrModelType = OcrModelType.RAPID,
            confidence_threshold: float = 0.5,
            iou_threshold: float = 0.45,
            use_pdf_metadata: bool = False,
    ):
        super().__init__(PdfPageType.LEGEND, ocr_model, use_pdf_metadata, confidence_threshold, iou_threshold)

    def handle_vision_detection(self, image):
        self.add_large_image_predict(YoloModelType.LEGEND_DETECTION, image, tile_size=self.tile_size)

    def handle_logic_compute(self, image, vision_result: VisionDetectedResult) -> DetectedLegendResult:

        if not vision_result:
            return DetectedLegendResult(
                file_name=vision_result.file_name,
                detect_type=self.page_type.desc,
                image_width=vision_result.image_width,
                image_height=vision_result.image_height,
                legends=[]
            )

        return DetectedLegendResult(
            file_name=vision_result.file_name,
            detect_type=self.page_type.desc,
            image_width=vision_result.image_width,
            image_height=vision_result.image_height,
            legends=self._find_legends(image)
        )

    def _find_legends(self, image):
        legends: List[LegendDetail] = []

        for detection in self.detections:
            # 在检测到的边界框内查找文本
            text = OcrEngine.find_best_item(detection, self.ocr_result)

            if text:
                roi, _ = crop_bboxes(image, [detection.xyxy])
                area = extract_rectangle(roi[0])
                if len(area) == 0:
                    logger.warning(f"未找到图例区域")
                    continue
                elif len(area) > 1:
                    logger.warning(f"检测到多个图例图片区域，可能存在误检，将使用第一个区域作为图例图片")
                area = area[0]

                legend_detail = LegendDetail(
                    **detection.model_dump(),
                    legend_area=DetectionResult(**detection.model_dump()).model_copy(update=dict(
                        **area.model_dump(),
                        class_name=LegendElemType.LEGEND_IMG.value,
                    )),
                    legend_text=DetectionResult(**text.model_dump()).model_copy(update=dict(
                        class_name=LegendElemType.LEGEND_TEXT.value,
                        confidence=text.confidence,
                        original_text=text.text,
                    ))
                )
                legends.append(legend_detail)

        return legends


if __name__ == '__main__':
    pass
