import logging
import re
from typing import Optional, List, Tuple, Container

import pandas as pd
from loguru import logger

from models.base import TABLE_CELL
from models.report_models import LayerInfoData, CheckingReport
from vision.core import DetectionResult
from vision.core.data_types import OcrItem
from vision.ocr_engine.base import OcrResult
from vision.ocr_engine.engine import OcrEngine


class DrillHoleBarParser:

    @staticmethod
    def extract_depth(depth_str) -> Optional[float]:
        """
        提取字符串中的深度信息并转换为浮点数

        参数:
            depth_str (str): 深度字符串，可能是纯数字或带单位（如'50.00m' 或 '40. 78m'）

        返回:
            float: 转换后的深度值
        """
        if depth_str is None:
            return None
        # 去除字符串两端的空白字符
        depth_str = depth_str.strip()

        # 提取数字部分（包括可能的小数点、负号和内部空格）
        import re
        # 这个正则表达式允许数字部分内部有空格，但会忽略这些空格
        match = re.search(r'([-+]?\d+\.?\s*\d*)', depth_str)

        if match:
            # 移除数字部分中的所有空格，然后转换为浮点数
            number_str = match.group(1).replace(' ', '')
            try:
                return float(number_str)
            except ValueError:
                logger.info(f"无法从字符串 '{depth_str}' 中提取有效的深度值")
                return None
        else:
            # 如果没有匹配到数字，记录日志并返回None
            logger.info(f"无法从字符串 '{depth_str}' 中提取有效的深度值")
            return None

    @staticmethod
    def extract_right_value(
            ocr_items,
            keywords: list[str],
            pattern: str = r"(\d+\s*(?:\.\s*\d+)?\s*[mM]?)"
    ) -> Optional[str]:
        """
        从 OCR 项中提取 keywords 右侧的数值（如孔深、标高等）
        :param ocr_items: OCR 结果列表
        :param keywords: 匹配关键字的列表（例如 ["孔深"] 或 ["标高", "孔口标高"]）
        :param pattern: 提取的数值模式
        """
        for item in ocr_items:
            text = (item.text or "").strip()
            if any(kw in text for kw in keywords):
                candidates = []
                for other in ocr_items:
                    if other is item:
                        continue
                    # 必须在 item 的右边，且大致同行
                    if other.x1 is not None and item.x2 is not None and other.center[0] > item.center[0] \
                            and item.y_overlap_ratio(other) > 0.6:
                        candidates.append(other)

                if candidates:
                    candidates.sort(key=lambda c: c.x1)  # 取最近的一个
                    right_text = (candidates[0].text or "").strip()
                    match = re.match(pattern, right_text)
                    if match:
                        return match.group(1)
        return None

    @staticmethod
    def parse_hole_number(ocr_result: OcrResult) -> Optional[str]:
        """
        解析钻口柱状图钻口编号
        """
        ocr_items = ocr_result.items

        for ocr_item in ocr_items:
            text = (ocr_item.text or "").replace("\n", "").strip()
            if not text:
                continue

            # 情况1: A21钻口柱状图 或 B2-1钻口柱状图,钻孔柱状图中可能会有干扰项
            match = re.search(r"([A-Za-z]?\d+(?:-\d+)?)[^\dA-Za-z]*钻孔.{0,2}柱状.{0,2}图", text)
            if match:
                return match.group(1)

            # 情况2: 形如 孔号：A21 或 孔号:A21
            # 这里严格要求 "孔号" 紧跟 冒号
                # 情况2: "孔号:A21" / "孔号：B2-2"
            match = re.search(r"孔号\s*[:：]\s*([A-Za-z]?\d+(?:-\d+)?)", text)
            if match:
                return match.group(1)

            match = re.search(r"(\d+)[^\d]*钻孔.{0,2}柱状.{0,2}图", text)
            if match:
                return match.group(1)

        return DrillHoleBarParser.extract_right_value(ocr_items, ["孔号"], "^([a-zA-Z0-9-]+)$")

    @staticmethod
    def parse_hole_depth(ocr_result: OcrResult) -> Optional[str]:
        """
        解析孔深
        """
        ocr_items = ocr_result.items

        # 情况1: "钻孔深度：50.00m"
        for item in ocr_items:
            text = (item.text or "").strip()
            match = re.search(r"钻孔深度\s*[:：]\s*(\S+)", text)
            if match:
                return match.group(1)

        # 情况2: "孔深" 在左边，右边是数值
        return DrillHoleBarParser.extract_right_value(ocr_items, ["孔深"])

    @staticmethod
    def parse_hole_elevation(ocr_result: OcrResult) -> Optional[str]:
        """
        解析标高
        """
        ocr_items = ocr_result.items

        # 情况1: "孔口标高：50.00m"
        for item in ocr_items:
            text = (item.text or "").strip()
            match = re.search(r"孔口标高\s*[:：]\s*(\S+)", text)
            if match:
                return match.group(1)

        # 情况2: ["标高", "孔口标高"] 在左边，右边是数值
        return DrillHoleBarParser.extract_right_value(ocr_items, ["标高", "孔口标高"])

    @staticmethod
    def parse_hole_type(ocr_result: OcrResult) -> Optional[str]:
        ocr_items = ocr_result.items
        return DrillHoleBarParser.extract_right_value(ocr_items, ["钻孔类型"])

    @staticmethod
    def parse_hole_x(ocr_result: OcrResult) -> Optional[str]:
        """
        提取 X 坐标
        """
        ocr_items = ocr_result.items

        # 左侧关键字 X坐标，右侧数值
        x_val = DrillHoleBarParser.extract_right_value(
            ocr_items, keywords=["X坐标"], pattern=r"(-?\d+(\.\d+)?)"
        )
        if x_val:
            return x_val

        # 孔位坐标文本中包含 x=...
        for item in ocr_items:
            text = (item.text or "").strip()
            text_lower = text.lower()
            if "孔位坐标" in text_lower or "x=" in text_lower:
                match = re.search(r"x\s*=\s*(-?\d+(\.\d+)?)", text_lower)
                if match:
                    return match.group(1)
        return None

    @staticmethod
    def parse_hole_y(ocr_result: OcrResult) -> Optional[str]:
        """
        提取 Y 坐标
        """
        ocr_items = ocr_result.items

        # 左侧关键字 Y坐标，右侧数值
        y_val = DrillHoleBarParser.extract_right_value(
            ocr_items, keywords=["Y坐标"], pattern=r"(-?\d+(\.\d+)?)"
        )
        if y_val:
            return y_val

        # 孔位坐标文本中包含 y=...
        for item in ocr_items:
            text = (item.text or "").strip()
            text_lower = text.lower()
            if "孔位坐标" in text_lower or "y=" in text_lower:
                match = re.search(r"y\s*=\s*(-?\d+(\.\d+)?)", text_lower)
                if match:
                    return match.group(1)
        return None

    @staticmethod
    def extract_layer_depth_and_elevation(ocr_result: OcrResult, drill_id: str) -> List[LayerInfoData]:
        """
        从 OCR 识别结果中提取深度和标高
        :param ocr_result: OCR 识别结果列表
        :param drill_id: 钻孔编号
        :return: 深度和标高列表
        """
        if drill_id is None:
            return []

        layer_infos = []
        ocr_items = ocr_result.items

        layer_depth_box = None
        elevation_box = None

        for ocr_item in ocr_items:
            text = (ocr_item.text or "").strip()
            if "深度" in text and "层底" in text:
                layer_depth_box = ocr_item
            if "标高" in text and "层底" in text:
                elevation_box = ocr_item

        if not layer_depth_box or not elevation_box:
            return []

        depth_candidates, elevation_candidates = [], []

        # 获取深度和标高
        for ocr_item in ocr_items:
            if abs(ocr_item.x1 - layer_depth_box.x1) < 60 and abs(
                    ocr_item.x2 - layer_depth_box.x2) < 60 and ocr_item != layer_depth_box:
                try:
                    layer_depth = float(ocr_item.text.strip())
                    depth_candidates.append((ocr_item.y1, ocr_item.y2, layer_depth))
                except ValueError:
                    continue
            if abs(ocr_item.x1 - elevation_box.x1) < 60 and abs(
                    ocr_item.x2 - elevation_box.x2) < 60 and ocr_item != elevation_box:
                try:
                    elevation = float(ocr_item.text.strip())
                    elevation_candidates.append((ocr_item.y1, ocr_item.y2, elevation))
                except ValueError:
                    continue

        depth_candidates.sort(key=lambda x: x[0])
        elevation_candidates.sort(key=lambda x: x[0])

        # 检查是否存在未检测到单元格
        for idx, depth, elevation in zip(range(len(depth_candidates)), depth_candidates, elevation_candidates):
            if idx > 0:
                if abs(abs(depth[0] - depth_candidates[idx - 1][1])) > 200:
                    logger.warning("两个深度之间的距离过大,请检查是否存在未检测到的单元格")
                if abs(elevation[0] - elevation_candidates[idx - 1][1]) > 200:
                    logger.warning("两个深度之间的距离过大,请检查是否存在未检测到的单元格")


        try:
            for depth_data, elevation_data in zip(depth_candidates, elevation_candidates):
                drill_data = LayerInfoData(
                    drill_id=drill_id,
                    depth=depth_data[2],
                    elevation=elevation_data[2],
                )
                layer_infos.append(drill_data)
            logger.debug(f"提取提取钻孔柱状图数据数据: 钻孔号 {drill_id} 提取成功 {len(depth_candidates)} 条数据")

        except (ValueError, TypeError) as e:
            logger.warning(f"提取钻孔柱状图数据数据解析失败:, 错误: {e}")

        return layer_infos



class StaticPenetrationTest(DrillHoleBarParser):
    pass


class StaticPenetrationLayer(DrillHoleBarParser):
    @staticmethod
    def parse_hole_number(ocr_result: OcrResult) -> Optional[str]:
        """
        解析孔号，支持中英文冒号和换行
        示例：
            孔号: A21
            孔号：A21
        """
        ocr_items = ocr_result.items

        for item in ocr_items:
            text = (item.text or "").replace("\n", "").strip()
            match = re.search(r"孔号\s*[:：]\s*(\S+)", text, re.IGNORECASE)
            if match:
                return match.group(1)
        return None

    @staticmethod
    def parse_hole_elevation(ocr_result: OcrResult) -> Optional[str]:
        """
        解析标高，支持中英文冒号和换行
        示例：
            标高: 4.0
            标高：4.0
        """
        ocr_items = ocr_result.items

        for item in ocr_items:
            text = (item.text or "").replace("\n", "").strip()
            match = re.search(r"标高\s*[:：]\s*(\S+)", text, re.IGNORECASE)
            if match:
                return match.group(1)
        return None

    @staticmethod
    def parse_multiple_holes(ocr_result: OcrResult) -> List[Tuple[Optional[str], Optional[str]]]:
        """
        解析多组孔号和标高信息，保持它们的关联性

        返回: 包含(孔号, 标高)元组的列表
        """
        ocr_items = ocr_result.items
        results = []

        # 首先尝试在同一文本项中匹配孔号和标高（包括换行格式）
        for item in ocr_items:
            text = item.text or ""

            # 保留换行符进行匹配，因为孔号和标高可能在不同行
            combined_match = re.search(r"孔号\s*[:：]\s*([^\s\n]+)\s*\n?\s*标高\s*[:：]\s*([^\s\n]+)", text,
                                       re.IGNORECASE | re.MULTILINE)
            if combined_match:
                hole_number = combined_match.group(1).strip()
                elevation = combined_match.group(2).strip()
                results.append((hole_number, elevation))

        # 如果已经找到组合匹配，直接返回
        if results:
            return results

        # 如果没有找到组合匹配，则分别匹配孔号和标高
        current_hole = None
        processed_items = []

        for item in ocr_items:
            text = item.text or ""

            # 尝试匹配孔号（更灵活的模式，支持各种字符组合）
            hole_match = re.search(r"孔号\s*[:：]\s*([A-Za-z0-9\-_]+)", text, re.IGNORECASE)
            if hole_match:
                # 如果之前已经找到孔号但没有匹配到标高，先保存
                if current_hole is not None:
                    results.append((current_hole, None))
                current_hole = hole_match.group(1).strip()

            # 尝试匹配标高（支持小数点）
            elevation_match = re.search(r"标高\s*[:：]\s*([\d\.\-]+)", text, re.IGNORECASE)
            if elevation_match:
                elevation = elevation_match.group(1).strip()
                # 如果有当前孔号，则配对
                if current_hole is not None:
                    results.append((current_hole, elevation))
                    current_hole = None
                else:
                    # 如果没有孔号，则保存孤立的标高
                    results.append((None, elevation))

        # 处理最后一个孔号（如果没有匹配到标高）
        if current_hole is not None:
            results.append((current_hole, None))

        # 如果上面的方法都没有找到结果，尝试更宽松的匹配
        if not results:
            for item in ocr_items:
                text = item.text or ""
                lines = text.split('\n')

                hole_number = None
                elevation = None

                for i, line in enumerate(lines):
                    line = line.strip()

                    # 匹配孔号行
                    if re.search(r"孔号", line, re.IGNORECASE):
                        hole_match = re.search(r"孔号\s*[:：]\s*([A-Za-z0-9\-_]+)", line, re.IGNORECASE)
                        if hole_match:
                            hole_number = hole_match.group(1).strip()
                        else:
                            # 如果孔号和值在不同位置，尝试在下一行或同行后面找
                            remaining_text = line.split('：')[-1].strip() if '：' in line else line.split(':')[
                                -1].strip() if ':' in line else ''
                            if remaining_text and re.match(r'^[A-Za-z0-9\-_]+$', remaining_text):
                                hole_number = remaining_text
                            elif i + 1 < len(lines):
                                next_line = lines[i + 1].strip()
                                if re.match(r'^[A-Za-z0-9\-_]+$', next_line):
                                    hole_number = next_line

                    # 匹配标高行
                    if re.search(r"标高", line, re.IGNORECASE):
                        elevation_match = re.search(r"标高\s*[:：]\s*([\d\.\-]+)", line, re.IGNORECASE)
                        if elevation_match:
                            elevation = elevation_match.group(1).strip()
                        else:
                            # 如果标高和值在不同位置，尝试在下一行或同行后面找
                            remaining_text = line.split('：')[-1].strip() if '：' in line else line.split(':')[
                                -1].strip() if ':' in line else ''
                            if remaining_text and re.match(r'^[\d\.\-]+$', remaining_text):
                                elevation = remaining_text
                            elif i + 1 < len(lines):
                                next_line = lines[i + 1].strip()
                                if re.match(r'^[\d\.\-]+$', next_line):
                                    elevation = next_line

                # 如果在同一个文本项中找到了孔号和标高，添加到结果中
                if hole_number or elevation:
                    results.append((hole_number, elevation))

        return results

    @staticmethod
    def parse_multiple_layers(ocr_result: OcrResult, detections: List[DetectionResult]) -> List[Tuple[Optional[str], Optional[str]]]:
        """
        解析多组钻孔的层级信息，

        返回: 包含的层级信息列表
        """
        logger.info("开始解析静力触探分层参数表的层级信息")
        layerinfos = []
        ocr_items = ocr_result.items

        # 提取钻孔号
        drill_ids = []
        for ocr_item in ocr_items:
            match = re.search(r"孔号\s*[:：]\s*([A-Za-z0-9\-_]+)", ocr_item.text)
            if match:
                drill_ids.append({
                    "id": match.group(1).strip(),
                    "item": ocr_item
                })

        if len(drill_ids) == 0:
            logger.warning("未找到钻孔号")
            return []

        # 提取深度范围数据
        for drill_ocr_item in drill_ids:
            container = drill_ocr_item["item"].get_outer_bboxes(detections)
            container = [item for item in container]
            if len(container) > 1:
                logger.warning(f"文本存在多个外部单元格，将使用第一个")
            container = container[0]

            container_x_min = container.x_min
            container_x_max = container.x_min + container.width / 2

            match_cells = [cell for cell in detections if container_x_min < cell.center_x < container_x_max and cell.center_y > container.y_max]
            match_cells = sorted(match_cells, key=lambda match_cell: match_cell.y1)

            depth_ranges = []
            for cell in match_cells:
                items = OcrEngine.find_overlap_items(cell, ocr_items)
                text = "\n".join([item.text for item in items])
                # 仅允许包含数字、负号、换行符以及空格，且必须包含至少一个数字
                whitelist_pattern = r"^(?=.*\d)[0-9\-.\s]+$"

                if not re.match(whitelist_pattern, text):
                    logger.debug(f"文本 {text} 格式不匹配，跳过")
                    continue
                depth_ranges.append(text)

            if not depth_ranges:
                continue

            logger.debug(f"钻孔 {drill_ocr_item['id']} 提取到 {len(depth_ranges)} 个深度范围: {depth_ranges}")

            # 处理每个深度范围
            depths = set()
            for layer_index, depth_range in enumerate(depth_ranges):
                try:
                    parts = depth_range.split('\n')
                    start_depth = float(OcrEngine.fix_ocr_float_separation(parts[0])) # 上侧深度
                    end_depth = float(OcrEngine.fix_ocr_float_separation(parts[1])) # 下侧深度
                    depths.update([start_depth, end_depth])
                except Exception as e:
                    logger.error(f"处理深度范围 {depth_range} 时出错: {e}")

            depths = sorted(list(depths))
            layerinfos.extend([(drill_ocr_item["id"], depth) for depth in depths if depth != 0.0])
        return layerinfos


def export_check_point_list_to_excel(check_point_list: List[CheckingReport], file_path: str = None) -> str:
    """
    将 check_point_list 转换为 Excel 报表

    Args:
        check_point_list: 检查点报告列表，包含钻孔信息、间距信息、分层信息等
        file_path: Excel 文件保存路径，如果为 None 则使用默认路径

    Returns:
        str: 生成的 Excel 文件路径
    """
    if file_path is None:
        file_path = r'地质钻探检查报告.xlsx'

    logger.info(f"开始导出检查点报告到 Excel: {file_path}")

    try:
        # 创建Excel工作簿
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            # 1. 创建审查报告汇总表
            _create_check_point_summary_sheet(writer, check_point_list)

            # 2. 创建分类详细表
            _create_check_point_detail_sheets(writer, check_point_list)

        # 应用格式化
        _format_check_point_excel_file(file_path)

        logger.info(f"检查点报告已成功导出到: {file_path}")
        return file_path

    except Exception as e:
        logger.error(f"导出 Excel 文件失败: {e}")
        raise


def _create_check_point_summary_sheet(writer, check_point_list: List[CheckingReport]):
    """创建检查点报告汇总表"""
    summary_data = []

    for i, report in enumerate(check_point_list, 1):
        summary_data.append({
            "序号": i,
            "数据名称": report.name or "",
            "审查结果": report.result or "",
            "是否正确": "✓" if report.is_valid else "✗",
            "数据来源": report.source or "",
            "数据对比": report.reference or "",
            "数据用途": report.usage or "",
            "源数据": report.original_value or "",
            "对照数据": report.compared_value or "",
            "备注": report.comments or ""
        })

    df_summary = pd.DataFrame(summary_data)
    df_summary.to_excel(writer, sheet_name="检查报告汇总", index=False)
    logger.info(f"创建汇总表，共 {len(summary_data)} 条记录")


def _create_check_point_detail_sheets(writer, check_point_list: List[CheckingReport]):
    """创建分类详细表"""
    # 按数据名称分类
    categories = {
        "钻孔信息": [],
        "勘探孔间距": [],
        "分层信息": [],
        "其他": []
    }

    for report in check_point_list:
        data_name = report.name or ""
        if "钻孔" in data_name or "孔口" in data_name or "孔深" in data_name or "坐标" in data_name:
            categories["钻孔信息"].append(report)
        elif "间距" in data_name or "距离" in data_name:
            categories["勘探孔间距"].append(report)
        elif "层" in data_name or "分层" in data_name or "深度" in data_name or "标高" in data_name:
            categories["分层信息"].append(report)
        else:
            categories["其他"].append(report)

    # 为每个有数据的分类创建工作表
    for category_name, reports in categories.items():
        if reports:  # 只为有数据的分类创建表
            _create_category_detail_sheet(writer, category_name, reports)


def _create_category_detail_sheet(writer, category_name: str, reports: List[CheckingReport]):
    """创建特定分类的详细表"""
    detail_data = []

    for i, report in enumerate(reports, 1):
        detail_data.append({
            "序号": i,
            "数据名称": report.name or "",
            "审查结果": report.result or "",
            "是否正确": "✓" if report.is_valid else "✗",
            "数据来源": report.source or "",
            "数据对比": report.reference or "",
            "数据用途": report.usage or "",
            "源数据": report.original_value or "",
            "对照数据": report.compared_value or "",
            "备注": report.comments or ""
        })

    df_detail = pd.DataFrame(detail_data)
    df_detail.to_excel(writer, sheet_name=f"{category_name}详细", index=False)
    logger.info(f"创建 {category_name} 详细表，共 {len(detail_data)} 条记录")


def _format_check_point_excel_file(file_path: str):
    """格式化检查点 Excel 文件"""
    try:
        from openpyxl import load_workbook
        from openpyxl.styles import Font, PatternFill, Border, Side, Alignment

        wb = load_workbook(file_path)

        # 定义样式
        header_font = Font(name='微软雅黑', size=11, bold=True, color='FFFFFF')
        header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        correct_fill = PatternFill(start_color='C6EFCE', end_color='C6EFCE', fill_type='solid')  # 绿色背景
        incorrect_fill = PatternFill(start_color='FFC7CE', end_color='FFC7CE', fill_type='solid')  # 红色背景
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        center_alignment = Alignment(horizontal='center', vertical='center')
        left_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)

        # 格式化每个工作表
        for sheet_name in wb.sheetnames:
            ws = wb[sheet_name]

            # 格式化表头
            for col in range(1, ws.max_column + 1):
                cell = ws.cell(row=1, column=col)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_alignment
                cell.border = border

            # 格式化数据行
            for row in range(2, ws.max_row + 1):
                for col in range(1, ws.max_column + 1):
                    cell = ws.cell(row=row, column=col)
                    cell.border = border

                    # 根据"是否正确"列设置行颜色
                    if col == 4:  # "是否正确"列
                        cell.alignment = center_alignment
                        if cell.value == "✓":
                            # 为整行设置绿色背景
                            for c in range(1, ws.max_column + 1):
                                ws.cell(row=row, column=c).fill = correct_fill
                        elif cell.value == "✗":
                            # 为整行设置红色背景
                            for c in range(1, ws.max_column + 1):
                                ws.cell(row=row, column=c).fill = incorrect_fill
                    else:
                        cell.alignment = left_alignment

            # 自动调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if cell.value and len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                # 根据列内容调整宽度
                if column_letter in ['A']:  # 序号列
                    adjusted_width = 8
                elif column_letter in ['D']:  # 是否正确列
                    adjusted_width = 12
                elif column_letter in ['B', 'E']:  # 数据名称、数据来源列
                    adjusted_width = min(max_length + 2, 25)
                else:  # 其他列
                    adjusted_width = min(max_length + 2, 40)
                ws.column_dimensions[column_letter].width = adjusted_width

        wb.save(file_path)
        logger.info("Excel 文件格式化完成")

    except Exception as e:
        logger.warning(f"Excel 格式化失败: {e}")


if __name__ == '__main__':
    ocr_res = OcrResult(items=[
        OcrItem(text="孔号标高：2.4"),
        OcrItem(text="孔号：A2")
    ])
    print(DrillHoleBarParser.parse_hole_number(ocr_res))
