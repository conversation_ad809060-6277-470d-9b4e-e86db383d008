import json
import os
from typing import List, <PERSON>, Tu<PERSON>, Dict, Any, Optional

import re
import cv2
import numpy as np

from exceptiongroup import catch
from loguru import logger
from networkx.algorithms.operators.binary import difference
from openai import OpenAI
from collections import Counter

from aikit.core.manager import ModelManager
from aikit.core.registry import registry, register_model_instance
from aikit.core.data_types import ModelType
from aikit.providers.openai_compatible.config import OpenAIConfig
from aikit.providers.auto_discovery import auto_register_ai_models
from models.anomaly_enums import AnomalyType
from models.base import VisionDetectedResult
from models.pdf_info_models import PdfPageType
from settings import MODEL_CONFIG, OUTPUT
from utils.pdf import PdfTextItem
from vision.core import DetectionResult, ImageHandler
from vision.core.data_types import BoundingBox, OcrItem, Anomaly, SeverityLevel
from vision.core.utils import crop_bboxes, non_maximum_suppression
from vision.obj_det import <PERSON><PERSON><PERSON><PERSON>lManager, YoloModelType
from vision.obj_det.yolo_holder import SectionElemType
from vision.ocr_engine.engine import Ocr<PERSON><PERSON><PERSON>, OcrModelType, batch_text_rec
from drawing_service.base import DetectorHelper, VisionComputer
from models.section_models import DetectedSectionResult, SectionDetail, SectionNumber, DrillHole, Spacing, SamplePoint, \
    DepthElevationPair, Ruler, RulerItem, SpacingWithDrillHole
from vision.utils.grid_image_builder import GridImageBuilder

SPACING_TEXT = "间距"


class SectionVisionComputer(VisionComputer, DetectorHelper):
    """剖面区域检测器"""

    def __init__(
            self,
            ocr_model: OcrModelType = OcrModelType.RAPID,
            confidence_threshold: float = 0.5,
            iou_threshold: float = 0.45,
            pair_nms_threshold: float = 0.1,
            use_pdf_metadata: bool = False,
    ):
        super().__init__(PdfPageType.SECTION, ocr_model, use_pdf_metadata, confidence_threshold, iou_threshold)
        self.pair_nms_threshold = pair_nms_threshold
        self.all_verticical_lines = List[List[int]]

    def handle_vision_detection(self, image: np.ndarray):
        self.add_image_predict(YoloModelType.SECTION_FULL, image)
        self.add_large_image_predict(YoloModelType.SECTION_SOIL_SAMPLE_DETECTION, image)
        self.add_large_image_predict(YoloModelType.SECTION_PAIR_DETECTION, image, tile_size=(896, 896), overlap_ratio=0.5, merge_nms_threshold=0.1)

    def handle_ocr_detection(self, image: np.ndarray):
        
        self._detect_vertical_lines_full_image(image)

        # 高程对识别
        self.merge_ocr(
            batch_text_rec(image, self._filter_in_class(
                self.detections,
                SectionElemType.DEPTH_ELEVATION_PAIR.value
            ), 'depth_elevation')
        )

        # 剖面编号、钻口
        self.merge_ocr(self.batch_ocr_processor.process(
            self._filter_in_class(
                self.detections,
                SectionElemType.SECTION_NUMBER.value,
                SectionElemType.DRILL_HOLE.value,
            ), image, self.ocr_engine.recognize
        ))

        grid_image_builder = GridImageBuilder()
        depth_elevation_elem = self._filter_in_class(self.detections, SectionElemType.DEPTH_ELEVATION_PAIR.value)
        crop_images = grid_image_builder.extract_regions(depth_elevation_elem, image)
        texts = [self.ocr_engine.find_best_text(bbox, self.roi_ocr_result) for bbox in depth_elevation_elem]
        img_with_ocr, _, _ = grid_image_builder.build_grid_image(crop_images, texts)
        img_without_ocr, _, _ = grid_image_builder.build_grid_image(crop_images)
        cv2.imwrite(OUTPUT / "depth_elevation_with_ocr.png", img_with_ocr)
        cv2.imwrite(OUTPUT / "depth_elevation_without_ocr.png", img_without_ocr)

    def handle_logic_compute(self, image, vision_result: VisionDetectedResult) -> DetectedSectionResult:
        # 剖面区域
        detected_sections: List[DetectionResult] = self.filter_by_class(SectionElemType.SECTION.value)
        if len(detected_sections) == 0:
            logger.warning("未检测到任何剖面区域")
            return DetectedSectionResult(
                file_name=vision_result.file_name,
                detect_type=self.page_type.desc,
                image_width=vision_result.image_width,
                image_height=vision_result.image_height,
                sections=[]
            )
        detected_sections = BoundingBox.sort_bboxes(detected_sections)
        logger.info(f"检测到 {len(detected_sections)} 个剖面区域")

        # 2、处理剖面区域检测结果
        sections = []
        # 找到所有间距分组
        spacing_groups = self._find_spacing_group()
        if len(spacing_groups) == len(detected_sections):
            for detection, spacings in zip(detected_sections, spacing_groups):
                section = SectionDetail(
                    **detection.model_dump(),
                    section_number=self._find_section_number(image, detection),
                    ruler=self._find_ruler(image, detection),
                    drill_holes=[],
                    spacings=spacings,
                )
                self._fill_drill_hole(image, section, detection)
                sections.append(section)
        else:
            logger.warning(f"间距数量({len(spacing_groups)})与剖面区域数量({len(detected_sections)})不匹配，可能存在错误")
            for detection in detected_sections:
                section = SectionDetail(
                    **detection.model_dump(),
                    section_number=self._find_section_number(image, detection),
                    ruler=self._find_ruler(image, detection),
                    drill_holes=[],
                    spacings=self._find_spacings(image, detection),
                )
                self._fill_drill_hole(image, section, detection)
                sections.append(section)
        spacings_with_drill_holes = self._extract_spacings_with_drill_hole(sections)
        return DetectedSectionResult(
            file_name=vision_result.file_name,
            detect_type=self.page_type.desc,
            image_width=vision_result.image_width,
            image_height=vision_result.image_height,
            sections=sections,
            spacing_with_drill_holes=spacings_with_drill_holes
        )

    def anomaly_check(self, detected_result: DetectedSectionResult):

        for section in detected_result.sections:

            # 检测钻孔间距冲突并标记为 difficult
            self._check_drill_hole_spacing_conflicts(section.drill_holes)

            for drill_hole in section.drill_holes:
                logger.info(f"正在对钻孔 {drill_hole.drill_name} 进行数据一致性校验")
                depth_elevation_pairs = drill_hole.depth_elevation_pairs
                # 计算参考值
                reference_pairs_result = self._calculate_reference_depth_elevation_pairs(
                    drill_hole, depth_elevation_pairs, section.ruler, drill_hole.surface_elevation
                )
                # 检查高程对差异是否超过阈值
                for idx, pair_detection in enumerate(depth_elevation_pairs):
                    threshold = pair_detection.height * section.ruler.value_per_pixel * 2

                    if idx < len(reference_pairs_result):
                        # 获取检测到的深度和高程值
                        detected_depth = getattr(pair_detection, 'depth', None)
                        detected_elevation = getattr(pair_detection, 'elevation', None)
                        
                        # 获取参考值
                        reference_depth = reference_pairs_result[idx].get('depth', 0)
                        reference_elevation = reference_pairs_result[idx].get('elevation', 0)
                        
                        # 计算差异
                        depth_diff = abs(detected_depth - reference_depth) if detected_depth is not None else 0
                        elevation_diff = abs(detected_elevation - reference_elevation) if detected_elevation is not None else 0
                        max_diff = max(depth_diff, elevation_diff)

                        pair_detection.anomaly = None
                        # 如果差异超过阈值，配置difficult
                        if max_diff > threshold:
                            if pair_detection.anomaly is None:
                                pair_detection.anomaly = Anomaly()
                            
                            pair_detection.anomaly.id = pair_detection.id
                            pair_detection.anomaly.anomaly_type = AnomalyType.DATA_INCONSISTENCY
                            pair_detection.anomaly.severity_level = SeverityLevel.HIGH
                            pair_detection.anomaly.origin_text = getattr(pair_detection, 'original_text', '')
                            pair_detection.anomaly.optimized_text = f"{detected_depth}({detected_elevation})"
                            pair_detection.anomaly.details = f"高程对差异超阈值: 检测值={detected_depth}({detected_elevation}), 参考值={reference_depth}({reference_elevation}), 差异={max_diff:.2f}, 阈值={threshold:.2f}"
                            
                            logger.warning(f"高程对差异超阈值: 检测值={detected_depth}({detected_elevation}), 参考值={reference_depth}({reference_elevation}), 差异={max_diff:.2f}, 阈值={threshold:.2f}")



        # ----------- 单调性校验 ----------
        # 对每个钻孔的深度高程对进行单调性检测
        for section in detected_result.sections:
            for drill_hole in section.drill_holes:
                logger.info(f"正在对钻孔 {drill_hole.drill_name} 进行单调性校验")
                depth_elevation_pairs = drill_hole.depth_elevation_pairs
                if not depth_elevation_pairs:
                    continue
                
                # 按Y坐标排序（从上到下）
                sorted_pairs = sorted(depth_elevation_pairs, key=lambda x: x.center[1])
                
                temp_depth = None
                temp_elevation = None
                monotonic_count = 0
                
                for idx, pair_detection in enumerate(sorted_pairs):
                    # 获取当前高程对的深度和高程值
                    detected_depth = getattr(pair_detection, 'depth', None)
                    detected_elevation = getattr(pair_detection, 'elevation', None)
                    
                    # 如果深度或高程值不存在，跳过单调性检测
                    if detected_depth is None or detected_elevation is None:
                        continue
                    
                    notes = []
                    monotonic_ok = True
                    
                    # 单调性校验：深度应该递增，高程应该递减（从上到下）
                    # 只有在不是第一个有效值时才进行比较
                    if temp_depth is not None and temp_elevation is not None:
                        if detected_depth < temp_depth or detected_elevation > temp_elevation:
                            msg = f"单调性异常: 当前深度={detected_depth}m, 高程={detected_elevation}m, 前一个深度={temp_depth}m, 高程={temp_elevation}m"
                            logger.warning(msg)
                            notes.append(msg)
                            monotonic_ok = False
                            monotonic_count += 1

                    pair_detection.anomaly = None
                    # 如果单调性异常，配置difficult
                    if not monotonic_ok:
                        if pair_detection.anomaly is None:
                            pair_detection.anomaly = Anomaly()
                        
                        pair_detection.anomaly.id = pair_detection.id
                        pair_detection.anomaly.anomaly_type = AnomalyType.MONOTONICITY_VIOLATION
                        pair_detection.anomaly.severity_level = SeverityLevel.HIGH
                        pair_detection.anomaly.origin_text = getattr(pair_detection, 'original_text', '')
                        pair_detection.anomaly.optimized_text = f"{detected_depth}({detected_elevation})"
                        pair_detection.anomaly.details = "; ".join(notes) or "单调性校验失败"

                    # 更新临时值用于下次比较
                    temp_depth = detected_depth
                    temp_elevation = detected_elevation
                
                logger.info(f"钻孔 {drill_hole.id} 单调性校验完成，异常数量: {monotonic_count}")

    @property
    def _detected_details(self):
        return self.filter_in_class(
            SectionElemType.SECTION_NUMBER.value,
            SectionElemType.SCALE.value,
            SectionElemType.DRILL_HOLE.value
        )

    def _check_drill_hole_spacing_conflicts(self, drill_holes: List[DetectionResult], tolerance: float = 100.0) -> None:
        """
        检测钻孔之间的距离冲突并标记为 difficult

        :param drill_holes: 已按 x 坐标升序排序的钻孔列表
        :param tolerance: 距离容差阈值（像素）
        """
        if len(drill_holes) < 2:
            return

        logger.info(f"开始检测 {len(drill_holes)} 个钻孔的间距冲突，容差阈值: {tolerance}px")
        conflict_count = 0

        for i in range(1, len(drill_holes)):
            current_hole = drill_holes[i]
            previous_hole = drill_holes[i - 1]

            # 计算当前钻孔与前一个钻孔中心点之间的距离
            current_center = current_hole.center
            previous_center = previous_hole.center
            distance = ((current_center[0] - previous_center[0]) ** 2 +
                       (current_center[1] - previous_center[1]) ** 2) ** 0.5

            # 计算前一个钻孔bbox宽度的一半
            previous_bbox_half_width = (previous_hole.x2 - previous_hole.x1) / 2

            # 判断是否存在冲突：距离 < tolerance + 前一个钻孔bbox宽度的一半
            conflict_threshold = tolerance + previous_bbox_half_width

            if distance < conflict_threshold:
                conflict_count += 1

                # 创建或更新 difficult 属性
                if current_hole.anomaly is None:
                    current_hole.anomaly = Anomaly()

                conflict_msg = (f"钻孔间距冲突: 与前一钻孔距离{distance:.1f}px < "
                              f"阈值{conflict_threshold:.1f}px (容差{tolerance}px + "
                              f"前钻孔半宽{previous_bbox_half_width:.1f}px)")

                current_hole.anomaly.id = current_hole.id
                current_hole.anomaly.anomaly_type = AnomalyType.SPATIAL_CONFLICT
                current_hole.anomaly.severity_level = SeverityLevel.MEDIUM
                current_hole.anomaly.details = conflict_msg
                current_hole.anomaly.origin_text = f"钻孔位置: {current_center}"
                current_hole.anomaly.optimized_text = f"冲突检测结果: 距离过近"

                logger.warning(f"检测到钻孔间距冲突: 钻孔{i} 与钻孔{i-1} 距离{distance:.1f}px < 阈值{conflict_threshold:.1f}px")

        if conflict_count > 0:
            logger.warning(f"共检测到 {conflict_count} 个钻孔存在间距冲突")
        else:
            logger.info("未检测到钻孔间距冲突")

    def _find_section_number(self, image, detection: DetectionResult) -> Optional[SectionNumber]:
        """
        在剖面检测结果中查找对应的剖面编号
        :param image: 原始图像
        :param detection: 剖面检测结果
        """

        section_number = self._find_class_in_bbox(detection, self._detected_details, class_name=SectionElemType.SECTION_NUMBER.value)
        if len(section_number) == 0:
            logger.warning(f"未找到剖面区域的剖面编号")
            return None
        elif len(section_number) > 1:
            logger.warning(f"找到多个剖面编号，可能存在误检，将使用第一个检测到的剖面编号")
        section_number = section_number[0]

        text = OcrEngine.find_best_text(section_number, self.roi_ocr_result)
        if not text:
            text = self.ocr_engine.find_best_text(section_number, self.ocr_result)

        return SectionNumber(**section_number.model_dump()).model_copy(update=dict(
            original_text=text
        ))

    def _find_ruler(self, image, detection) -> Optional[Ruler]:
        # 找到剖面区域的所有标尺元素
        ruler_items = self._find_class_in_bbox(detection, self._detected_details, class_name=SectionElemType.SCALE.value)
        if not ruler_items:
            logger.warning("未找到剖面区域的标尺信息")
            return None
        elif len(ruler_items) > 1:
            logger.warning("找到多个标尺信息，可能存在误检，将使用第一个检测到的标尺信息")
        ruler_item = ruler_items[0]
        # 识别文本
        roi = ImageHandler.crop_bbox(image, ruler_item)
        ocr_result = self.ocr_engine.recognize(roi)
        texts = ocr_result.items

        if not texts:
            logger.warning("OCR 未识别到任何文本")
            return None

        # 过滤文本中的空格
        for item in texts:
            item.text = re.sub(r'\s+', '', item.text)  # 去掉所有空格
            item.text = re.sub(r'_','',item.text) # 去掉错误识别到的 _
        texts = sorted(texts, key=lambda text: text.y1)
        #
        # values = []
        # for item in texts:
        #     text = re.sub(r'\s+', '', item.text)  # 去掉所有空格
        #     text = re.sub(r'_', '', text)  # 去掉错误识别到的 _
        #     try:
        #         values.append(float(text))
        #     except ValueError:
        #         continue  # 不是数字就跳过
        #
        # diffs = [values[i+1]-values[i] for i in range(len(values)-1)]
        #
        # counter = Counter(diffs)
        # most_common_diff ,count= counter.most_common(1)[0]
        #
        # print("出现次数最多的差值:", most_common_diff, "出现次数:", count)
        #
        #

        # 填充标尺基本信息
        ruler_items = []
        for item in texts:
            try:
                item = item.map_to_original(ruler_item)
                value = float(item.text)
                ruler_items.append(RulerItem(
                    **item.model_dump(),
                    value=value,
                    y=item.xyxy[3]
                ).model_copy(update=dict(
                    class_name=SectionElemType.SCALE,
                    original_text=item.text,
                )))
            except ValueError as e:
                logger.warning(f"解析标尺文本失败: {item.text}, 错误: {e}")
                continue
        # 计算标尺每像素对应的实际长度
        if len(ruler_items) < 2:
            logger.warning("标尺信息不足，无法计算每像素对应的实际长度，同时将无法计算采样点深度信息")
            return None
        item1, item2 = ruler_items[:2]
        value_per_pixel = -(item1.value - item2.value) / (item1.y - item2.y)

        return Ruler(
            **ruler_item.model_dump(),
            value_per_pixel=value_per_pixel,
            ruler_items=ruler_items,
        ).model_copy(update=dict(
            original_text=None,
        ))

    def _fill_drill_hole(self, image, section_detail: SectionDetail, detect_section: DetectionResult) -> None:
        """
        查找钻孔信息
        :return: 钻孔列表
        """
        # 找到当前剖面所有钻孔
        detect_drill_holes = self._find_class_in_bbox(detect_section, self._detected_details, class_name=SectionElemType.DRILL_HOLE.value)
        if not detect_drill_holes:
            logger.warning("未找到任何钻孔信息")
            return

        # 钻口按照 x 坐标升序
        detect_drill_holes = sorted(detect_drill_holes, key=lambda x: x.x1)

        # 校验间距数量与钻孔数量是否匹配
        spacings = section_detail.spacings
        if len(spacings) != len(detect_drill_holes) - 1:
            logger.warning("间距数量与钻孔数量不匹配，为钻口分配的间距可能存在错误！")

        # 填装数据
        for idx, hole in enumerate(detect_drill_holes):
            text = OcrEngine.find_best_text(hole, self.roi_ocr_result)
            # 提取深度高程对
            if self.pdf_texts and self.use_pdf_metadata:
                # 基于PDF元数据提取高程对
                ruler = section_detail.ruler
                y_min, y_max = ruler.y_min, ruler.y_max + 100
                depth_elevation_items = self._find_item_in_bbox(detect_section, self.pdf_texts)
                depth_elevation_items = self._find_item_in_y_range(y_min, y_max, depth_elevation_items)
            else:
                # 基于目标检测提取高程对
                depth_elevation_items = self.filter_by_class(SectionElemType.DEPTH_ELEVATION_PAIR.value)
                depth_elevation_items = self._find_class_in_bbox(detect_section, depth_elevation_items,
                                                                 class_name=SectionElemType.DEPTH_ELEVATION_PAIR.value)
            info = self._parse_drill_hole_text(text)
            elevation = float(info.get("elevation", "0"))
            hole_name = info.get("hole_id", "")
            logger.info(f"正在采集钻孔 {hole_name} 相关信息")
            drill_hole = DrillHole(
                **hole.model_dump(),
                drill_name=hole_name,
                surface_elevation=elevation,
                # depth_elevation_pairs=self._extract_depth_elevation(hole, depth_elevation_items),
                depth_elevation_pairs=self._extract_depth_elevation_from_pairs(image, hole, depth_elevation_items, section_detail.ruler, elevation),
                x_coordinate=hole.xyxy[0],
                is_terminal_hole=idx == len(detect_drill_holes) - 1,
                samples=self._find_sample_points(section_detail.ruler, elevation, detect_section, hole)
            ).model_copy(update=dict(
                original_text=text,
            ))
            if idx < len(detect_drill_holes) - 1:
                self._assign_spacing(idx, drill_hole, spacings)
            section_detail.drill_holes.append(drill_hole)

    def _assign_spacing(self, idx: int, drill_hole: DrillHole, spacings: List[Spacing]) -> None:
        """
        为钻孔分配间距信息
        :param idx: 钻孔索引
        :param drill_hole: 钻孔对象
        :param spacings: 间距列表
        """
        if idx < len(spacings):
            spacing = spacings[idx]
            drill_hole.assigned_spacing = spacing
            logger.info(f"为钻孔 {drill_hole.drill_name} 分配间距: {spacing.value}m, 原文='{spacing.original_text}'")
        else:
            logger.warning(f"钻孔 {drill_hole.drill_name} 没有对应的间距信息")

    def _find_spacing_group(self) -> List[List[Spacing]]:
        """
        查找所有间距组
        :return: 间距组列表
        """
        spacing_groups: List[List[Spacing]] = []

        # 找到“间距”文本
        spacing_text_items: List[OcrItem] = []
        for item in self.ocr_result.items:
            if item.text and SPACING_TEXT in item.text:
                spacing_text_items.append(item)
        if len(spacing_text_items) == 0:
            logger.warning("未找到任何间距信息")
            return []

        # 找到同行其他间距信息
        spacing_lines_dict = {}
        for item in spacing_text_items:
            lines = OcrEngine.find_items_in_y_range(item.y_min, item.y_max, self.ocr_result, 0.5)
            for l in lines:
                key = (l.x_min, l.y_min, l.x_max, l.y_max, l.text)  # 唯一标识
                spacing_lines_dict[key] = l

        spacing_lines = list(spacing_lines_dict.values())

        # 按 y 坐标分组
        sorted_items = sorted(spacing_lines, key=lambda i: (i.y_min, i.x_min))

        line_groups: List[List[OcrItem]] = []
        current_line: List[OcrItem] = []

        for item in sorted_items:
            if not current_line:
                current_line.append(item)
                continue
            # 只要与当前行中任意一个元素 y 轴重合比例 >= 0.6 就认为是同一行
            if any(item.y_overlap_ratio(existing) >= 0.6 for existing in current_line):
                current_line.append(item)
            else:
                # 新的一行
                line_groups.append(sorted(current_line, key=lambda i: i.x_min))
                current_line = [item]

        if current_line:
            line_groups.append(sorted(current_line, key=lambda i: i.x_min))

        # 每一行内部根据 SPACING_TEXT 进行二次分组
        for line in line_groups:
            # 找到所有 SPACING_TEXT 的位置（按 x 排序）
            spacing_positions = [idx for idx, it in enumerate(line) if it.text and SPACING_TEXT in it.text]
            if not spacing_positions:
                continue

            # 遍历 SPACING_TEXT，找出它后面的所有元素作为一组
            for i, pos in enumerate(spacing_positions):
                start_idx = pos + 1
                end_idx = spacing_positions[i + 1] if i + 1 < len(spacing_positions) else len(line)
                group_items = line[start_idx:end_idx]
                # 构建 Spacing 对象
                group_items = [self._build_spacing(item) for item in group_items if item.text and SPACING_TEXT not in item.text]
                if group_items:
                    spacing_groups.append(group_items)

        return spacing_groups

    def _build_spacing(self, spacing_item: OcrItem) -> Spacing:
        """
        构建间距对象
        :param spacing_item: 间距文本项
        :return: Spacing 对象
        """
        try:
            # 提取间距数值
            match = re.search(r'(\d+\.?\d*)\s*', spacing_item.text)
            if match:
                spacing_value = float(OcrEngine.fix_ocr_float_separation(match.group(1)))
                return Spacing(
                    **spacing_item.model_dump(),
                    value=spacing_value
                ).model_copy(update=dict(
                    class_name=SectionElemType.SPACING.SPACING,
                    original_text=spacing_item.text,
                ))
            else:
                logger.warning(f"未能从间距文本 '{spacing_item.text}' 中提取数值")
                return Spacing(**spacing_item.model_dump())
        except ValueError as e:
            logger.error(f"解析间距数值失败: {spacing_item.text}, 错误: {e}")
            return Spacing(**spacing_item.model_dump())

    def _find_sample_points(self, ruler: Ruler, elevation: float,
                            detect_section: DetectionResult, detect_hole: DetectionResult) -> List[SamplePoint]:
        """
        查找钻孔下的采样点
        :param ruler: 剖面标尺
        :param elevation: 钻孔孔口标高
        :param detect_section: 检测的剖面
        :param detect_hole: 检测的钻孔
        :return: 采样点列表
        """
        sample_points = []

        # 在当前剖面区域内查找采样点
        detected_samples = self.filter_in_class(
            SectionElemType.SOIL_SAMPLE_CIRCLE.value,
            SectionElemType.SOIL_SAMPLE_TRIANGLE.value
        )
        detect_sample_points = self._find_item_in_bbox(detect_section, detected_samples)
        # 查找钻孔下的采样点
        tolerance = 20  # 容差范围，允许采样点在钻孔区域外一定范围内
        x_min, x_max = detect_hole.xyxy[0] - tolerance, detect_hole.xyxy[2] + tolerance
        detect_sample_points = self._find_item_in_x_range(x_min, x_max, detect_sample_points)

        if not detect_sample_points:
            return []

        # 按 y 轴升序
        detect_sample_points = sorted(detect_sample_points, key=lambda x: x.y1)
        for sample_point in detect_sample_points:
            # 计算采样点的深度和高程
            depth, sample_elevation = self._calculate_sample_depth(ruler, elevation, sample_point)
            sample_points.append(SamplePoint(
                **sample_point.model_dump(),
                depth=depth,
                elevation=sample_elevation,
                sample_type=sample_point.class_name,
            ))
        return sample_points

    def _calculate_sample_depth(self, ruler: Ruler, elevation: float, sp_bbox: BoundingBox) -> Tuple[float, float]:
        """
        计算采样点的深度和高程
        :param ruler: 剖面标尺
        :param elevation: 钻孔孔口标高
        :param sp_bbox: 采样点的边界框
        :return: (深度, 高程)
        """
        if not ruler or not ruler.value_per_pixel:
            logger.warning("无法计算采样点深度，标尺信息不足")
            return 0.0, elevation

        if elevation == 0:
            logger.warning("钻孔孔口标高为0，为异常高度，可能导致采样点深度计算不准确")

        # 计算采样点的 Y 坐标相对于标尺的偏移量
        y_offset = (sp_bbox.center[1]) - ruler.ruler_items[0].y

        # 计算采样点高程
        sample_elevation = ruler.ruler_items[0].value - y_offset * ruler.value_per_pixel

        # 计算采样点深度（单位：米）
        depth = elevation - sample_elevation

        return depth, sample_elevation

    def _parse_drill_hole_text(self, text: str) -> Dict[str, Any]:
        """
        解析钻孔OCR文字，提取钻孔编号和高程
        OCR结果格式:
            第一行：编号（数字、字母、短横线）
            第二行：高程（浮点数）
        """
        result: Dict[str, Any] = {}
        try:
            text = text.strip()
            parts = [p.strip() for p in text.split("\n") if p.strip()]

            if not parts:
                return result

            hole_id_raw = parts[0]
            # 只保留字母、数字、短横线
            hole_id = re.sub(r'[^A-Za-z0-9-]', '', hole_id_raw).upper()
            if hole_id:
                result["hole_id"] = hole_id

            if len(parts) > 1:
                elevation_raw = parts[1]

                # 修复 4 . 96 → 4.96
                elevation_raw = re.sub(r'(\d)\s*\.\s*(\d+)', r'\1.\2', elevation_raw)

                # 修复 4 96 → 4.96（仅当第二个是2位数字时）
                elevation_raw = re.sub(r'(\d)\s+(\d{2})\b', r'\1.\2', elevation_raw)

                # 提取浮点数
                match = re.search(r'(\d+\.?\d*)', elevation_raw)
                if match:
                    try:
                        result["elevation"] = float(match.group(1))
                    except ValueError:
                        pass

        except Exception as e:
            logger.error(f"解析钻孔文字失败: {e}")

        return result

    def _extract_depth_elevation_from_pairs(
            self,
            image: np.ndarray,
            detect_hole: BoundingBox,
            pair_detections: List[BoundingBox],
            ruler: Optional[Ruler] = None,
            surface_elevation: float = 0.0
    ) -> List[DepthElevationPair]:
        """
        基于pair模型检测结果提取深度-高程数据对（两阶段处理）
        第一阶段：pair模型已经检测出边界框位置
        第二阶段：对每个边界框区域单独进行OCR识别

        :param image: 原始图像
        :param detect_hole: 钻孔检测结果
        :param pair_detections: pair模型检测到的边界框列表
        :return: 深度高程对列表
        """

        # 按照bbox边缘分配
        # 过滤出在钻孔附近的pair检测结果
        relevant_pairs = self._assign_pairs_to_drill_holes_by_vertical_lines(detect_hole, pair_detections)
        relevant_pairs.sort(key=lambda pair: pair.y1)

        # 原始正则，支持负数和小数
        pattern = r'(-?\d+(?:\.\d+)?)\s*\(\s*(-?\d+(?:\.\d+)?)\s*\)'

        def normalize_number_text(text: str) -> str:
            """修复 OCR 数字常见错误"""
            text = re.sub(r'\s+', '', text)  # 去掉空白
            text = re.sub(r'\).+', ')', text)  # 去掉 ) 后面的字符
            text = re.sub(r'~', '-', text)  # ~ -> -
            text = re.sub(r'[/\\\[\]]', '', text)  # 去掉 / \ [ ]
            text = re.sub(r'[，,*:]', '.', text)  # , ， * : -> .
            return text

        # ----------- 收集OCR文本 ----------
        ocr_texts: List[str] = []
        for pair_detection in relevant_pairs:
            try:
                ocr_result = OcrEngine.find_best_text(pair_detection, self.roi_ocr_result)
                ocr_texts.append(ocr_result)
            except Exception as e:
                logger.error(f"OCR提取出错: {e}")
                continue

        pairs: List[DepthElevationPair] = []
        for idx, (ocr_text, pair_detection) in enumerate(zip(ocr_texts, relevant_pairs)):
            fixed_text = normalize_number_text(ocr_text)
            matches = re.findall(pattern, fixed_text)
            if not matches:
                elevation, depth = 0, 0
                logger.warning(f"高程对提取出错，原始文本：{ocr_text}，修复后结果：{fixed_text}")
            else:
                if len(matches) > 1:
                    logger.warning(f"匹配到多个结果，默认取第一个: {matches}")
                depth, elevation = matches[0]

            pair = DepthElevationPair(
                **pair_detection.model_dump(),
                value=f"{depth}({elevation})",
                depth=depth,
                elevation=elevation,
            ).model_copy(update=dict(
                class_name=SectionElemType.DEPTH_ELEVATION_PAIR,
                confidence=min(pair_detection.confidence, 1),
                original_text=ocr_texts[idx] if idx < len(ocr_texts) else "",
            ))
            pairs.append(pair)

        return pairs

    def _detect_vertical_lines_full_image(self, image: np.ndarray) -> List[List[int]]:
        """
        在整个图像范围内检测竖直线
        优化后的竖线检测方法，不限制Y坐标范围

        :param image: 输入图像
        :return: 检测到的竖直线列表，格式为[[x1, y1, x2, y2], ...]
        """
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        # 高斯模糊去噪
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # Canny边缘检测
        edges = cv2.Canny(blurred, 80, 120, apertureSize=3)

        # 概率霍夫变换检测直线 - 调整参数以检测更多竖线
        lines = cv2.HoughLinesP(edges, 1, np.pi / 180, threshold=100,
                                minLineLength=100, maxLineGap=8)

        vertical_lines = []

        if lines is not None:
            for line in lines:
                x1, y1, x2, y2 = line[0]
                # 判断是否为竖直线（更宽松的条件）
                if abs(x2 - x1) < 10:
                    vertical_lines.append([x1, y1, x2, y2])

            # 合并相近的竖直线
            merged_lines = self._merge_vertical_lines(vertical_lines, distance_threshold=20)
            logger.debug(f"共检测到 {len(lines)} 条线段，其中 {len(vertical_lines)} 条竖直线，合并后剩下 {len(merged_lines)} 条竖直线")

            self.all_verticical_lines = merged_lines

            return merged_lines
        else:
            logger.warning("未检测到任何线段")
            return []

    def _merge_vertical_lines(self, lines: List[List[int]], distance_threshold: int = 15) -> List[List[int]]:
        """
        合并距离很近的竖直线
        从recognize_line.py提取的合并方法

        :param lines: 竖直线列表
        :param distance_threshold: 判断两条线是否相近的x坐标距离阈值
        :return: 合并后的竖直线列表
        """
        if len(lines) == 0:
            return []

        # 按x坐标排序
        sorted_lines = sorted(lines, key=lambda x: x[0])

        merged_lines = []
        current_group = [sorted_lines[0]]

        for i in range(1, len(sorted_lines)):
            current_line = sorted_lines[i]
            last_line_in_group = current_group[-1]

            # 计算两条线x坐标的距离
            distance = abs(current_line[0] - last_line_in_group[0])

            if distance <= distance_threshold:
                # 距离在阈值内，加入当前组
                current_group.append(current_line)
            else:
                # 距离超出阈值，处理当前组并开始新组
                if current_group:
                    merged_line = self._merge_line_group(current_group)
                    merged_lines.append(merged_line)
                current_group = [current_line]

        # 处理最后一组
        if current_group:
            merged_line = self._merge_line_group(current_group)
            merged_lines.append(merged_line)

        return merged_lines

    def _merge_line_group(self, group: List[List[int]]) -> List[int]:
        """
        将一组相近的竖直线合并为一条线
        从recognize_line.py提取的合并方法

        :param group: 相近的竖直线组
        :return: 合并后的竖直线 [x, y1, x, y2]
        """
        x_values = [line[0] for line in group]
        avg_x = int(np.mean(x_values))

        # 找到y的最小值和最大值
        all_y = []
        for line in group:
            all_y.extend([line[1], line[3]])

        min_y = min(all_y)
        max_y = max(all_y)

        return [avg_x, min_y, avg_x, max_y]

    def _filter_vertical_lines_enhanced(self, vertical_lines: List[List[int]],
                                       spacing_text_items: List, y_min: int, y_max: int) -> List[List[int]]:
        """
        竖线过滤方法

        :param vertical_lines: 检测到的所有竖线
        :param spacing_text_items: 间距文本项列表
        :param y_min: 间距区域最小Y坐标
        :param y_max: 间距区域最大Y坐标
        :return: 过滤后的竖线列表
        """
        if not vertical_lines:
            return []

        logger.info(f"开始过滤竖线，原始数量: {len(vertical_lines)}")

        # 1. 计算每条竖线的长度，按长度排序
        lines_with_length = []
        for line in vertical_lines:
            x1, y1, x2, y2 = line
            length = abs(y2 - y1)
            lines_with_length.append((line, length))

        # 按长度降序排序
        lines_with_length.sort(key=lambda x: x[1], reverse=True)

        # 2. 去掉最长的两条竖线
        if len(lines_with_length) > 2:
            filtered_by_length = [item[0] for item in lines_with_length[2:]]
            logger.info(f"去掉最长的2条竖线后剩余: {len(filtered_by_length)}条")
        else:
            logger.warning("竖线数量不足，无法去掉最长的两条")
            return []

        # 3. 去掉位于间距文本bbox范围内的竖线（带容差）
        x_tolerance = 200  # 容差像素
        filtered_lines = []

        for line in filtered_by_length:
            x1, y1, x2, y2 = line
            line_x = (x1 + x2) // 2  # 竖线的x坐标

            # 检查是否在任何间距文本的x范围内
            should_filter = False
            for spacing_item in spacing_text_items:
                text_x_min = spacing_item.x_min - x_tolerance
                text_x_max = spacing_item.x_max + x_tolerance

                if text_x_min <= line_x <= text_x_max:
                    should_filter = True
                    logger.debug(f"竖线x={line_x}在间距文本范围[{text_x_min}-{text_x_max}]内，将被过滤")
                    break

            if not should_filter:
                filtered_lines.append(line)

        logger.info(f"去掉间距文本范围内的竖线后剩余: {len(filtered_lines)}条")

        # 4. 按x坐标排序
        filtered_lines.sort(key=lambda line: line[0])

        return filtered_lines

    def _find_spacings_with_vertical_lines(self, image: np.ndarray, detect_section: DetectionResult,
                                                   spacing_text_items: List, y_min: int, y_max: int) -> List[Spacing]:
        """
        基于竖线检测的间距识别方法
        合并了竖线检测、过滤和OCR识别的完整流程

        :param image: 原始图像
        :param detect_section: 剖面检测结果
        :param spacing_text_items: 间距文本项列表
        :param y_min: 间距表格区域的最小Y坐标
        :param y_max: 间距表格区域的最大Y坐标
        :return: 间距列表
        """
        logger.info(f"开始基于竖线检测的间距识别，Y范围: {y_min}-{y_max}")

        # 1. 在整个图像范围内检测竖直线
        all_vertical_lines = self._detect_vertical_lines_full_image(image)

        if len(all_vertical_lines) < 4:
            logger.warning(f"检测到的竖线数量不足({len(all_vertical_lines)}条)，无法进行间距识别")
            return []

        # 2. 过滤与间距无关的竖线
        filtered_lines = self._filter_vertical_lines_enhanced(all_vertical_lines, spacing_text_items, y_min, y_max)

        if len(filtered_lines) < 2:
            logger.warning(f"过滤后的竖线数量不足({len(filtered_lines)}条)，无法进行间距识别")
            return []

        logger.info(f"过滤后保留 {len(filtered_lines)} 条竖线用于间距识别")

        # 3. 创建OCR识别区域并进行识别
        spacings = []
        for i in range(len(filtered_lines) - 1):
            left_line = filtered_lines[i]
            right_line = filtered_lines[i + 1]

            # 定义OCR区域的边界
            x_min = left_line[0]  # 左竖线的x坐标
            x_max = right_line[0]  # 右竖线的x坐标
            ocr_y_min = y_min
            ocr_y_max = y_max

            # 确保区域有效
            if x_max <= x_min or ocr_y_max <= ocr_y_min:
                logger.warning(f"无效的OCR区域: x({x_min}-{x_max}), y({ocr_y_min}-{ocr_y_max})")
                continue

            # 4. 裁剪OCR区域并进行识别
            roi = image[ocr_y_min:ocr_y_max, x_min:x_max]

            if roi.size == 0:
                logger.warning(f"OCR区域为空: x({x_min}-{x_max}), y({ocr_y_min}-{ocr_y_max})")
                continue

            logger.info(f"OCR区域 {i+1}: x({x_min}-{x_max}), y({ocr_y_min}-{ocr_y_max}), 尺寸: {roi.shape}")

            # 4. OCR识别
            ocr_result = OcrEngine.get_instance().recognize(roi)
            if not ocr_result.items:
                logger.warning(f"OCR区域 {i + 1} 未识别到任何文本")
                continue

            # === 新增：合并多个 OCR 结果 ===
            if len(ocr_result.items) == 1:
                merged_items = ocr_result.items
            else:
                texts = [it.text for it in ocr_result.items if it.text]
                if not texts:
                    continue

                merged_text = "".join(texts)
                x1 = x_min
                y1 = ocr_y_min
                x2 = x_max
                y2 = ocr_y_max
                confidence = sum(it.confidence for it in ocr_result.items) / len(ocr_result.items)

                merged_items = [OcrItem(
                    text=merged_text,
                    confidence=confidence,
                    x1=x1, y1=y1, x2=x2, y2=y2
                )]

            # 5. 构建 Spacing
            for ocr_item in merged_items:
                if not ocr_item.text or SPACING_TEXT in ocr_item.text:
                    continue

                spacing = self._build_spacing(ocr_item)
                if spacing:
                    spacings.append(spacing)
                    logger.info(f"识别到间距值: {spacing.value}m, 原文: '{spacing.original_text}'")

        logger.info(f"检测共识别到 {len(spacings)} 个间距值")
        return spacings


    def optimize_with_qwen(self, ocr_texts: List[str], reference_pairs: List[str]) -> List[dict]:
        """
        调用 Qwen 模型优化 OCR 文本，返回标准化的深度-高程对
        :param ocr_texts: OCR 原始识别文本数组
        :param reference_pairs: 参考估算的深度高程对数组
        :return: List[{"depth": float, "elevation": float}]
        """
        prompt = f"""
    你是一个OCR文本修复助手。现在有两组数据：
    1. OCR识别结果（主要依据）
    2. 参考估算结果（辅助修正）

    任务：输出修复后的深度-高程对，格式严格为 Depth(Elevation)，并保证为浮点数。
    只输出JSON数组，每个元素包含 "depth" 和 "elevation"。
    深度都是正数，高程可以是正数也可以是负数。
    深度为递增的，高程为递减的。
    优化后的结果必须为数字
    返回的结果数量要与非空的OCR的识别结果数量完全一致，数量方面要更多的参考OCR识别结果，而不是参考估算结果。

    OCR结果: {ocr_texts}
    参考值: {reference_pairs}

    输出示例：
    [
      {{"depth": 6.00, "elevation": -1.59}},
      {{"depth": 9.46, "elevation": 12.20}}
    ]
        """

        client = OpenAI(
            api_key=os.getenv("DASHSCOPE_API_KEY"),
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )

        response = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {"role": "system", "content": "You are a precise data extraction assistant."},
                {"role": "user", "content": prompt},
            ],
            temperature=0.2,
        )

        content = response.choices[0].message.content.strip()

        # --- 清理 Markdown 代码块标记 ---
        if content.startswith("```"):
            content = re.sub(r"^```[a-zA-Z]*", "", content).strip()
            content = re.sub(r"```$", "", content).strip()

        try:
            return json.loads(content)
        except json.JSONDecodeError:
            logger.error(f"模型输出无法解析为 JSON: {content}")
            return []

    def _find_spacings(self, image: np.ndarray, detect_section: DetectionResult) -> List[Spacing]:
        """
        基于竖线检测的间距识别方法，自动从OCR结果中获取间距区域

        :param image: 原始图像
        :param detect_section: 剖面检测结果
        :return: 间距列表
        """
        # 1. 从OCR结果中查找"间距"文本项，自动计算Y坐标范围
        spacing_text_items = []
        for item in self.ocr_result.items:
            if item.text and SPACING_TEXT in item.text:
                spacing_text_items.append(item)

        if not spacing_text_items:
            logger.warning("未找到间距文本，无法进行间距识别")
            return []

        # 2. 基于找到的间距文本计算Y坐标范围
        y_tolerance = 30  # 容差像素
        all_y_mins = [item.y_min for item in spacing_text_items]
        all_y_maxs = [item.y_max for item in spacing_text_items]

        y_min = max(0, min(all_y_mins) - y_tolerance)
        y_max = max(all_y_maxs) + y_tolerance

        logger.info(f"基于{len(spacing_text_items)}个间距文本项自动计算Y坐标范围: {y_min}-{y_max}")

        # 3. 使用基于竖线检测的策略
        return self._find_spacings_with_vertical_lines(image, detect_section,
                                                              spacing_text_items, y_min, y_max)

    def _find_vertical_line_under_drill(self, x_min: int, x_max: int) -> List[int]:
        """
            基于竖直线检测的间距边界识别策略
            params:
                image: 输入图像
            return: 识别到的间距边界框列表
        """
        # 在整个图像范围内检测竖直线
        all_vertical_lines = self.all_verticical_lines
        for line in all_vertical_lines:
            if line[0] >= x_min and line[2] <= x_max:
                return line
        return None

    def _calculate_reference_depth_elevation_pairs(self, detect_hole: BoundingBox,
                                                 relevant_pairs: List[BoundingBox],
                                                 ruler: Optional[Ruler] = None,
                                                 surface_elevation: float = 0.0) -> List[str]:
        """
        基于已有的钻孔数据和间距信息计算逻辑深度高程对作为参考数据
        参考_calculate_sample_depth方法的实现模式

        :param detect_hole: 钻孔检测结果
        :param relevant_pairs: pair模型检测到的边界框列表
        :param ruler: 标尺信息（可选）
        :param surface_elevation: 钻孔孔口标高（可选）
        :return: 深度高程对字符串列表，格式为["深度(高程)", ...]
        """
        reference_pairs = []

        try:
            # 生成参考深度高程对
            for i, pair_bbox in enumerate(relevant_pairs):
                try:

                    # 基于位置计算参考深度和高程，参考_calculate_sample_depth的算法逻辑
                    if ruler and ruler.value_per_pixel and ruler.ruler_items:
                        # 使用标尺信息计算精确的深度高程（与_calculate_sample_depth相同的算法）
                        if i < len(relevant_pairs) - 1:
                            y_offset = pair_bbox.xyxy[3] - ruler.ruler_items[0].y
                        else:
                            y_offset = pair_bbox.xyxy[1] - ruler.ruler_items[0].y
                        calculated_elevation = ruler.ruler_items[0].value - y_offset * ruler.value_per_pixel
                        calculated_depth = surface_elevation - calculated_elevation

                        # logger.debug(f"使用标尺计算: Y偏移={y_offset:.1f}px, "
                        #            f"每像素值={ruler.value_per_pixel:.4f}, "
                        #            f"标尺基准值={ruler.ruler_items[0].value:.2f}")
                    else:
                        # 没有标尺信息时，使用简化的估算方法
                        # 基于Y坐标位置估算深度（假设每100像素约1米深度）
                        pixel_depth = pair_bbox.center[1] - detect_hole.y1
                        estimated_depth = max(0.0, pixel_depth / 100.0)  # 简化的像素到米的转换
                        calculated_depth = estimated_depth
                        calculated_elevation = surface_elevation - calculated_depth

                        logger.debug(f"使用估算方法: 像素深度={pixel_depth:.1f}px, "
                                   f"估算深度={estimated_depth:.2f}m")

                    # 格式化为标准的深度(高程)格式
                    reference_pair = f"{calculated_depth:.2f}({calculated_elevation:.2f})"
                    reference_pairs.append({
                        "reference_pair": reference_pair,
                        "depth": round(calculated_depth, 2),
                        "elevation": round(calculated_elevation, 2)
                    })

                except Exception as e:
                    logger.warning(f"计算第{i+1}个参考深度高程对时出错: {e}")
                    continue
            logger.info(f"生成参考深度高程对: {'; '.join([pair['reference_pair'] for pair in reference_pairs])}")
            logger.info(f"成功生成 {len(reference_pairs)} 个参考深度高程对")

        except Exception as e:
            logger.error(f"计算参考深度高程对时发生错误: {e}")

        return reference_pairs

    def _assign_pairs_to_drill_holes_by_vertical_lines(self, detect_hole, pair_detections):
        """
        基于竖线位置的配对信息分配策略

        根据以下逻辑分配配对信息：
        1. 竖线大概处于钻孔号检测框的中心位置，与钻孔一一对应
        2. 配对信息绝对紧挨位于竖线的右侧
        3. 按竖线x坐标排序，计算每个配对信息到各竖线的距离
        4. 将配对信息分配给距离最近且位于其左侧的竖线对应的钻孔

        :param detect_hole: 当前钻孔检测结果
        :param pair_detections: 所有配对信息检测结果
        :param tolerance: 容差范围
        :return: 分配给当前钻孔的配对信息列表
        """
        if not self.all_verticical_lines or not pair_detections:
            logger.warning("竖线或配对信息为空，无法进行基于竖线的分配")
            return []

        # 1. 按竖线x坐标排序（从左到右）
        sorted_vertical_lines = sorted(self.all_verticical_lines, key=lambda line: line[0])

        # 2. 找到当前钻孔对应的竖线
        drill_hole_center_x = (detect_hole.x1 + detect_hole.x2) / 2
        current_vertical_line = None
        current_line_index = -1

        # 找到距离钻孔中心最近的竖线
        min_distance = float('inf')
        for i, line in enumerate(sorted_vertical_lines):
            line_x = line[0]  # 竖线的x坐标
            distance = abs(line_x - drill_hole_center_x)
            if distance < min_distance:
                min_distance = distance
                current_vertical_line = line
                current_line_index = i

        if current_vertical_line is None:
            logger.warning(f"未找到钻孔 {drill_hole_center_x} 对应的竖线")
            return []

        current_line_x = current_vertical_line[0]
        logger.debug(f"钻孔检测框中心x={drill_hole_center_x:.1f}，对应竖线x={current_line_x}，距离={min_distance:.1f}px")

        # 3. 为每个配对信息计算到各竖线的距离，并找到最佳分配
        relevant_pairs = []

        for pair in pair_detections:
            pair_left_x = pair.x1  # 配对信息的左边框

            # 找到位于配对信息左侧且距离最近的竖线
            best_line_index = -1
            best_distance = float('inf')

            for i, line in enumerate(sorted_vertical_lines):
                line_x = line[0]

                # 只考虑位于配对信息左侧的竖线
                distance = abs(pair_left_x - line_x)
                if distance < best_distance:
                    best_distance = distance
                    best_line_index = i

            # 如果找到的最佳竖线就是当前钻孔对应的竖线，则分配给当前钻孔
            if best_line_index == current_line_index:
                relevant_pairs.append(pair)
            #     logger.debug(f"配对信息 (x={pair_left_x:.1f}) 分配给竖线 {current_line_x}，距离={best_distance:.1f}px")
            # else:
            #     logger.debug(f"配对信息 (x={pair_left_x:.1f}) 分配给其他竖线 (索引={best_line_index})，距离={best_distance:.1f}px")

        logger.debug(f"为钻孔 (中心x={drill_hole_center_x:.1f}) 分配了 {len(relevant_pairs)} 个配对信息")
        return relevant_pairs

    def _extract_spacings_with_drill_hole(self, sections: List[SectionDetail]):
        """
            从剖面数据中提取间距和钻孔组合信息
            :param sections: 剖面数据列表
            :return: 钻孔间距组合列表
        """
        if sections is None or len(sections) < 1:
            logger.warning("无法提取间距信息，剖面数据为空")
            return []

        spacing_with_drill_holes = []

        # 遍历钻孔和间距，构建 SpacingWithDrillHole 对象
        for section in sections:
            spacings = section.spacings
            drill_holes = section.drill_holes
            if len(spacings) >= len(drill_holes):
                continue
            for i in range(len(spacings)):
                drill_hole1 = drill_holes[i]
                spacing = spacings[i]
                drill_hole2 = drill_holes[i + 1]

                # 创建 SpacingWithDrillHole 对象
                spacing_with_drill_hole = SpacingWithDrillHole(
                    section_spacing=spacing,
                    drill_hole1=drill_hole1,
                    drill_hole2=drill_hole2
                )

                spacing_with_drill_holes.append(spacing_with_drill_hole)
                logger.info(f"构建钻孔间距组合: {drill_hole1.drill_name} - {spacing.value}m - {drill_hole2.drill_name}")

        logger.info(f"共构建 {len(spacing_with_drill_holes)} 个钻孔间距组合")
        return spacing_with_drill_holes



if __name__ == '__main__':
    from pathlib import Path

    image_path = r"D:\page_24.png"
    file_name = Path(image_path).name
    detector = SectionVisionComputer()
    result = detector.detect(file_name, image_path)
    detector.vision_detect(file_name, image_path)
    detector.visualization(
        class_names=[
            SectionElemType.DEPTH_ELEVATION_PAIR.value
        ],
        draw_label=False,
        save_path='test.png'
    )

    # print(result.model_dump_json(indent=2))
