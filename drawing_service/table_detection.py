from typing import Any, List, Optional

import cv2
import numpy as np
from loguru import logger
from paddleocr import LayoutDetection
from torch.nn.functional import threshold

from drawing_service.base import VisionComputer, DetectorHelper
from utils.space import find_vertical_split
from drawing_service.layout_utils.specify_cell_detection import detect_left_merged_cell_groups, detect_merge_cells
from drawing_service.ocr_utils.data_parser import DrillHoleBarParser, StaticPenetrationLayer, StaticPenetrationTest
from models.base import VisionDetectedResult, TABLE_ELEM_LABEL, TABLE_CELL
from models.pdf_info_models import PdfPageType, DataSourceType
from models.report_models import DrillHoleInfo, LayerInfoData
from models.sheet_models import SheetConfig, DetectedTableResult
from settings import OUTPUT
from utils.grouper import Grouper
from vision.core import ImageHandler
from vision.core.data_types import BoundingBox, DetectionResult
from vision.core.utils import Visualizer, non_maximum_suppression, split_bbox_vertically
from vision.cv_det.opencv_detector import OpenCVDetector
from vision.ocr_engine.base import OcrResult
from vision.ocr_engine.engine import Ocr<PERSON>odelType, batch_text_rec
from vision.table_det import PPStructureModelManager, PPStructureModelType
from vision.table_det.cell_coordinate_mapper import CoordinateMapper
from vision.table_det.cell_to_table_convertor import convert_bboxes_to_sheet_config
from vision.table_det.table_image_cutter import TableImageCutter



class ComplexTableVisionComputer(VisionComputer, DetectorHelper):
    """错位表格检测器"""

    def __init__(
            self,
            ocr_model: OcrModelType = OcrModelType.RAPID,
            confidence_threshold: float = 0.5,
            iou_threshold: float = 0.45,
            use_pdf_metadata: bool = False,
    ):
        super().__init__(PdfPageType.TABLE, ocr_model, use_pdf_metadata, confidence_threshold, iou_threshold)
        self.batch_size = 8
        self.target_w = 512
        self.target_h = 512

    def handle_vision_detection(self, image: np.ndarray):
        model = PPStructureModelManager().load_model(PPStructureModelType.LAYOUT_DETECTION)
        outputs = model.predict(image, batch_size=1, layout_nms=True)

        # 文档布局检测
        for res in outputs:
            for idx, box in enumerate(res['boxes']):
                self.detections.append(DetectionResult(
                    class_name=box['label'],
                    **BoundingBox.from_array(box['coordinate']).model_dump()
                ))

        image_width, image_height = image.shape[:2]

        # 裁剪表格区域
        table_elem = self._filter_by_class(self.detections, TABLE_ELEM_LABEL)
        crop_images = [ImageHandler.crop_bbox(image, detection) for detection in table_elem]

        # 坐标映射器
        coordinate_mapper = CoordinateMapper(original_size=(image_width, image_height))

        # 表格类型
        table_type = self._get_table_type()

        # 分割表格检测
        for idx, (crop_img, detection) in enumerate(zip(crop_images, table_elem)):
            # 检测水平线以及垂直线
            opencv_detector = OpenCVDetector(image=crop_img)
            horizontal_lines = opencv_detector.detect_horizontal_lines()
            vertical_lines = opencv_detector.detect_vertical_lines()

            # 分割表格
            threshold_ratio = 0.5 if table_type in [DataSourceType.DRILL_BAR_CHART, DataSourceType.STATIC_PENETRATION_TEST] else 0.9
            cutter = TableImageCutter(crop_img, threshold_ratio=threshold_ratio, min_segment_ratio=0.02)
            cutter.set_lines(horizontal_lines, vertical_lines)
            cutter.visualize_cut_lines(target_width=self.target_w, target_height=self.target_h, output_path=OUTPUT / f"cut_preview_{idx}.png")
            crop_tables, crop_areas = cutter.cut_image(target_width=self.target_w, target_height=self.target_h)

            for id_, crop_table in enumerate(crop_tables):
                cv2.imwrite(OUTPUT / f"table_crop_{idx}_{id_}.png", crop_table)

            # 识别表格单元格
            batch_size = min(self.batch_size, len(crop_tables))
            model = PPStructureModelManager().load_model(PPStructureModelType.TABLE_CELL_DET)
            detected_cells = model.predict(crop_tables, batch_size=batch_size)

            # 映射回原表格坐标
            original_coordinates = []
            for part_cells, area in zip(detected_cells, crop_areas):
                cells = [BoundingBox.xyxy_to_corner4(box['coordinate']) for box in part_cells['boxes']]
                original_coordinate = coordinate_mapper.map_from_crop_region(cells, area)
                original_coordinates.extend(original_coordinate)

            # 映射到原图
            original_coordinates = [
                DetectionResult(
                    class_name=TABLE_CELL,
                    image_index=idx,
                    **BoundingBox.from_corner4(coord).map_to_original(detection).model_dump()
                ) for coord in original_coordinates
            ]
            self.detections.extend(original_coordinates)

        # NMS 去除单元格重叠框
        detected_table_cells = self._filter_in_class(self.detections, TABLE_CELL)
        self._detections = self._filter_not_in_class(self.detections, TABLE_CELL)
        self._detections.extend(non_maximum_suppression(detected_table_cells))

    def handle_ocr_detection(self, image: np.ndarray):
        detected_table_cells = self._filter_in_class(self.detections, TABLE_CELL)

        # 识别表格单元格文本
        table_type = self._get_table_type()
        if DataSourceType.STATIC_PENETRATION_LAYER.value == table_type: # 静力触探分层参数表特殊处理
            target_cell = None
            for cell in detected_table_cells:
                ocr_items = self.ocr_engine.find_inner_item(cell, self.ocr_result)
                text = "".join([item.text for item in ocr_items])
                if "名称" in text:
                    target_cell = cell
                    break
            if target_cell:
                big_cells = detect_merge_cells(detected_table_cells, target_cell)
            else:
                big_cells = detect_left_merged_cell_groups(detected_table_cells)

            logger.info(f"静力触探分层识别到 {len(big_cells)} 个大数据单元格")
            big_cells_ids = [cell.id for cell in big_cells]
            other_cells = [cell for cell in detected_table_cells if cell.id not in big_cells_ids]

            # 先识别其他单元格
            self.merge_ocr(self.batch_ocr_processor.process(
                other_cells,
                image, self.ocr_engine.recognize
            ))
            # 再识别大单元格
            split_bboxes = []
            for cell in big_cells:
                split_bboxes.extend(split_bbox_vertically(cell, 3))
            ocr_result = self.batch_ocr_processor.process(
                split_bboxes,
                image, self.ocr_engine.recognize
            )
            self.merge_ocr(self.ocr_engine.build_ocr_result(big_cells, ocr_result))
        else: # 其他默认
            self.merge_ocr(self.batch_ocr_processor.process(
                detected_table_cells,
                image, self.ocr_engine.recognize
            ))

        # 识别非表格单元格文本
        self.merge_ocr(self.batch_ocr_processor.process(
            [detection for detection in self.detections if detection.class_name not in (TABLE_CELL, TABLE_ELEM_LABEL)],
            image, self.ocr_engine.recognize
        ))
        # 将不在表格区域内的所有 ocr_result 合并到 roi_ocr_result
        other_ocr_items = self._find_item_not_in_bboxes(
            self._filter_by_class(self.detections, TABLE_ELEM_LABEL),
            self.ocr_result.items
        )
        
        # 过滤掉已经存在的OCR项目，避免重复添加
        existing_items = set()
        for item in self.roi_ocr_result.items:
            # 使用坐标和文本内容作为唯一标识
            key = (item.x1, item.y1, item.x2, item.y2, item.text)
            existing_items.add(key)
        
        new_items = []
        for item in other_ocr_items:
            key = (item.x1, item.y1, item.x2, item.y2, item.text)
            if key not in existing_items:
                new_items.append(item)
                existing_items.add(key)
        
        self.roi_ocr_result.items.extend(new_items)

    def _get_table_type(self) -> DataSourceType:

        def calc_table_type(ocr_result):
            if not ocr_result or not ocr_result.items:
                return DataSourceType.UNDEFINED
            type_ = DataSourceType.UNDEFINED
            for text in ocr_result.texts:
                if DataSourceType.STATIC_PENETRATION_LAYER in text:
                    type_ = DataSourceType.STATIC_PENETRATION_LAYER
                    break
                elif DataSourceType.STATIC_PENETRATION_TEST in text:
                    type_ = DataSourceType.STATIC_PENETRATION_TEST
                    break
                elif DataSourceType.DRILL_BAR_CHART in text or "钻口柱状图" in text:
                    type_ = DataSourceType.DRILL_BAR_CHART
                    break
            return type_

        table_type = calc_table_type(self.ocr_result)
        if table_type == DataSourceType.UNDEFINED:
            table_type = calc_table_type(self.roi_ocr_result)

        return table_type

    @staticmethod
    def get_drill_hole_info(
        page_info: str,
        table_type: DataSourceType,
        roi_ocr_result: OcrResult
    ) -> Optional[List[DrillHoleInfo]]:
        drill_infos = []

        parser_map = {
            DataSourceType.DRILL_BAR_CHART.value: DrillHoleBarParser,
            DataSourceType.STATIC_PENETRATION_LAYER.value: StaticPenetrationLayer,
            DataSourceType.STATIC_PENETRATION_TEST.value: StaticPenetrationTest,
        }

        parser = parser_map.get(table_type)
        if not parser:
            return None

        if table_type == DataSourceType.STATIC_PENETRATION_LAYER.value:
            hole_infos = parser.parse_multiple_holes(roi_ocr_result)
            for hole, elevation in hole_infos:
                drill_infos.append(DrillHoleInfo(
                    page_info=page_info,
                    data_type=table_type,
                    number=hole,
                    surface_elevation=elevation,
                ))
        else:
            depth = parser.extract_depth(parser.parse_hole_depth(roi_ocr_result))
            surface_elevation = parser.extract_depth(parser.parse_hole_elevation(roi_ocr_result))
            drill_infos.append(DrillHoleInfo(
                page_info=page_info,
                data_type=table_type,
                number=parser.parse_hole_number(roi_ocr_result),
                hole_type=parser.parse_hole_type(roi_ocr_result),
                surface_elevation=surface_elevation,
                depth=depth,
                x_coordinate=parser.parse_hole_x(roi_ocr_result),
                y_coordinate=parser.parse_hole_y(roi_ocr_result),
            ))

        return drill_infos

    @staticmethod
    def get_layer_info(
        page_info: str,
        table_type: DataSourceType,
        roi_ocr_result: OcrResult,
        detections: List[DetectionResult]
    ) -> Optional[List[LayerInfoData]]:

        layer_infos = []

        paser_map = {
            DataSourceType.STATIC_PENETRATION_LAYER.value: StaticPenetrationLayer,
            DataSourceType.STATIC_PENETRATION_TEST.value: StaticPenetrationTest,
            DataSourceType.DRILL_BAR_CHART.value: DrillHoleBarParser,
        }
        parser = paser_map.get(table_type)
        if not parser:
            return None
        if table_type == DataSourceType.STATIC_PENETRATION_LAYER.value:
            detections = [d for d in detections if d.class_name == TABLE_CELL]
            layer_results = parser.parse_multiple_layers(roi_ocr_result, detections)
            for drill_id, depth in layer_results:
                layer_infos.append(LayerInfoData(
                    page_info=page_info,
                    data_type=table_type,
                    drill_id=drill_id,
                    depth=depth,
                ))
        elif table_type == DataSourceType.STATIC_PENETRATION_TEST.value:
            drill_id = parser.extract_right_value(roi_ocr_result.items,["孔号"],"^([a-zA-Z0-9-]+)$")
            layer_infos.extend(parser.extract_layer_depth_and_elevation(roi_ocr_result, drill_id))
        else:
            drill_id = parser.parse_hole_number(roi_ocr_result)
            layer_infos.extend(parser.extract_layer_depth_and_elevation(roi_ocr_result, drill_id))

        return layer_infos

    def handle_logic_compute(self, image, vision_result: VisionDetectedResult) -> DetectedTableResult:
        image_width, image_height = vision_result.image_width, vision_result.image_height

        table_type = self._get_table_type()
        logger.info(f"检测表格类型：{table_type}")

        table_cells = self._filter_in_class(self.detections, TABLE_CELL)
        table_num = len(set([cell.image_index for cell in table_cells]))

        table_infos: List[SheetConfig] = []
        for idx in range(table_num):
            table_idx_cells = [cell for cell in table_cells if cell.image_index == idx]
            texts = [self.ocr_engine.find_best_text(cell, self.roi_ocr_result) for cell in table_idx_cells]

            x_max, y_max = max(b.x_max for b in table_idx_cells), max(b.y_max for b in table_idx_cells)
            img = np.full((y_max, x_max, 3), 255, dtype=np.uint8)
            img = Visualizer().draw_bboxes_with_texts(img, table_idx_cells, texts)
            cv2.imwrite(OUTPUT / f"table_{idx}.png", img)

            table_infos.append(convert_bboxes_to_sheet_config(table_idx_cells, texts=texts))

        return DetectedTableResult(
            file_name=vision_result.file_name,
            detect_type=self.page_type.desc,
            table_type=table_type,
            image_width=image_width,
            image_height=image_height,
            sheets=table_infos,
            vision_results=self.detections,
            roi_ocr_result=self.roi_ocr_result,
            drill_hole_infos=self.get_drill_hole_info(vision_result.file_name, table_type, self.roi_ocr_result),
        )


if __name__ == '__main__':
    image_path = r"E:\CodeData\01-SHProject\drawing-classify\train\drill_bar\siqingfu_page_27.png"

    computer = ComplexTableVisionComputer()
    result = computer.detect("", image_path)
    # result = computer.vision_detect("", image_path)
    for sheet in result.sheets:
        print(sheet.model_dump_json())
    # import pickle
    # with open('texts.pkl', 'rb') as f:  # 'rb' 是以二进制读取模式打开文件
    #     texts = pickle.load(f)
    #
    #
    # with open('original_coordinates.pkl', 'rb') as f:  # 'rb' 是以二进制读取模式打开文件
    #     original_coordinates = pickle.load(f)
    #
    # sheet_config = convert_bboxes_to_sheet_config(original_coordinates, texts=texts)
    #
    # result = DetectedTableResult(
    #     file_name="",
    #     detect_type="复杂表格",
    #     image_width=1024,
    #     image_height=1024,
    #     sheets=[sheet_config],
    # )
    #
    # height_list = [box.height for box in original_coordinates]
    # group = Grouper(tolerance=0.1).simple_group(height_list, lambda x: x)
    # grouped_height = [[height_list[i] for i in g] for g in group]
    #
    print(result.model_dump_json())
