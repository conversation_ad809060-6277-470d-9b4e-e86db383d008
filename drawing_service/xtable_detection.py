import re
from pathlib import Path
from typing import Union, Any, List, Optional

import cv2
import numpy as np
from loguru import logger
from rapidocr import RapidOCR
from wired_table_rec.main import WiredTableInput, WiredTableRecognition

from drawing_service.base import DetectorHelper, VisionComputer
from models.base import VisionDetectedResult
from models.misaligned_table_models import DetectedTableResult, MisalignedTableResult, TableInfo
from models.pdf_info_models import PdfPageType
from models.sheet_models import ReportConfig, SheetConfig
from utils.sheet import SheetConfigConverter
from vision.core import ImageHandler, DetectionResult
from vision.core.data_types import BoundingBox
from vision.obj_det import YoloModelType
from vision.obj_det.yolo_holder import XTableElemType
from vision.ocr_engine.engine import OcrModelType, OcrEngine


class XTableVisionComputer(VisionComputer, DetectorHelper):
    """错位表格检测器"""

    def __init__(
            self,
            ocr_model: OcrModelType = OcrModelType.RAPID,
            confidence_threshold: float = 0.5,
            iou_threshold: float = 0.45,
            use_pdf_metadata: bool = False,
    ):
        super().__init__(PdfPageType.MISALIGNED_TABLE, ocr_model, use_pdf_metadata, confidence_threshold, iou_threshold)

    def handle_vision_detection(self, image):
        self.add_image_predict(YoloModelType.XTABLE_DETECTION, image)
        self.add_ocr_recognize(image)

    def handle_logic_compute(self, image, vision_result: VisionDetectedResult) -> ReportConfig:
        if not self.detections:
            return ReportConfig(
                output_filename=vision_result.file_name,
                sheets=[],
            )

        logger.info(f"共检测到 {len(self.detections)} 个表格区域")
        return ReportConfig(
            output_filename=vision_result.file_name,
            sheets=self._find_sheets(image)
        )

    def _find_sheets(self, image) -> List[SheetConfig]:
        sheets: List[SheetConfig] = []
        for idx, detection in enumerate(self.detections):
            # 提取表格区域
            x1, y1, x2, y2 = detection.xyxy
            sheet_image = image[y1:y2, x1:x2]

            cell_images, logic_points = XTableVisionComputer.detect_table_cell(sheet_image)

            logger.info(f"完成表格 {idx+1} 单元格布局检测")

            if not cell_images:
                logger.warning(f"未在区域{idx+1}检测到表格单元格")
                continue

            texts = []
            for cell_image in cell_images:
                ocr_result = self.ocr_engine.recognize(cell_image)
                texts.append("\n".join(ocr_result.texts))

            sheet_config = SheetConfigConverter.create_sheet_config(logic_points, texts,
                                                                    sheet_name=f"Sheet_{idx + 1}")
            sheets.append(sheet_config)
        return sheets

    @staticmethod
    def detect_table_cell(image: np.array, margin: int = 5):
        # 使用RapidOCR输入
        ocr_engine = RapidOCR()
        rapid_ocr_output = ocr_engine(image, return_word_box=True)
        ocr_result = list(
            zip(rapid_ocr_output.boxes, rapid_ocr_output.txts, rapid_ocr_output.scores)
        )

        # 初始化表格检测模型
        wired_input = WiredTableInput()
        table_engine = WiredTableRecognition(wired_input)
        table_results = table_engine(image, ocr_result=ocr_result)

        # 预览各个单元格
        img_height, img_width = image.shape[:2]
        cell_images = []
        for idx, bboxes in enumerate(table_results.cell_bboxes, start=1):
            p1, p2, p3, p4 = bboxes.reshape(4, 2)
            x = int(min(p1[0], p2[0], p3[0], p4[0]))
            y = int(min(p1[1], p2[1], p3[1], p4[1]))
            w = int(max(p1[0], p2[0], p3[0], p4[0]) - x)
            h = int(max(p1[1], p2[1], p3[1], p4[1]) - y)

            # 提取单元格
            x_with_margin = int(max(0, x - margin))
            y_with_margin = int(max(0, y - margin))
            w_with_margin = int(min(img_width - x_with_margin, w + 2 * margin))
            h_with_margin = int(min(img_height - y_with_margin, h + 2 * margin))
            cell_img = image[y_with_margin:y_with_margin + h_with_margin,
                       x_with_margin:x_with_margin + w_with_margin]

            # 将长方形填充为正方形
            h, w = cell_img.shape[:2]
            max_size = max(h, w)
            square_img = np.full((max_size, max_size, 3), 255, dtype=np.uint8)  # 创建白色背景
            y_offset = (max_size - h) // 2
            x_offset = (max_size - w) // 2
            square_img[y_offset:y_offset + h, x_offset:x_offset + w] = cell_img
            cell_img = square_img
            # 保存单元格图像
            cell_images.append(cell_img)

        logic_points = table_results.logic_points
        return cell_images, logic_points.tolist()


class MisalignedTableVisionComputer(VisionComputer, DetectorHelper):
    """错位表格检测器"""

    def __init__(
            self,
            ocr_model: OcrModelType = OcrModelType.RAPID,
            confidence_threshold: float = 0.5,
            iou_threshold: float = 0.45
    ):
        super().__init__(PdfPageType.MISALIGNED_TABLE, ocr_model, confidence_threshold, iou_threshold)

    def handle_vision_detection(self, image: np.ndarray):
        self.add_image_predict(YoloModelType.XTABLE_DETECTION, image)
        logger.info(f"共检测到 {len(self.detections)} 个表格区域")

        table_details = []
        for idx, detection in enumerate(self.detections):
            table_image = ImageHandler.crop_bbox(image, detection)
            logger.info(f"正在处理第 {idx+1} 个表格区域，区域坐标: {detection.xyxy}")
            table_detail = self.yolo_manager.load_model(YoloModelType.XTABLE_DETAIL_DETECTION).predict_single(table_image, target_size=self.tile_size)
            table_details.extend(self._handled_table_details(detection, table_detail))

        self._detections.extend(table_details)

    def handle_ocr_detection(self, image: np.ndarray):
        # ocr 数字
        self.merge_ocr(self.batch_ocr_processor.process(
            self._filter_in_class(
                self.detections,
                XTableElemType.TABLE_INT.value, XTableElemType.TABLE_NUMBER.value
            ), image, self.ocr_engine.recognize
        ))

        # ocr 文本信息
        self.merge_ocr(self.batch_ocr_processor.process(
            self._filter_in_class(
                self.detections,
                XTableElemType.TABLE_INFO.value
            ), image, self.ocr_engine.recognize
        ))


    def handle_logic_compute(self, image, vision_result: VisionDetectedResult) -> MisalignedTableResult:
        misaligned_table_result = MisalignedTableResult(
            file_name=vision_result.file_name,
            detect_type=self.page_type.desc,
            image_width=vision_result.image_width,
            image_height=vision_result.image_height,
            detected_tables_result=[]
        )

        detected_tables = self._filter_by_class(self.detections, XTableElemType.TABLE)

        if not detected_tables:
            return misaligned_table_result

        detected_tables = BoundingBox.sort_bboxes(detected_tables)
        for idx, detected_table in enumerate(detected_tables):
            logger.info(f"正在处理第 {idx+1} 个表格区域")
            detected_table_result = DetectedTableResult(
                table_info=self._find_table_info(image, detected_table),
                scales=[],
                number_data=[]
            )
            misaligned_table_result.detected_tables_result.append(detected_table_result)

            int_detections = self._find_class_in_bbox(detected_table, self.detections, XTableElemType.TABLE_INT)
            int_detections = sorted(int_detections, key=lambda i: i.y_min)

            # 找到 int 为 100 和 200 的检测
            match_int_detection = []
            for detection in int_detections:
                ocr_text = OcrEngine.find_best_text(detection, self.roi_ocr_result)
                if ocr_text in ["100", "200"]:
                    match_int_detection.append(detection)

                    detected_table_result.scales.append(DetectionResult(**detection.model_dump()).model_copy(update=dict(
                        original_text=ocr_text
                    )))

            if len(match_int_detection) != 2:
                logger.warning(f"找到 {len(match_int_detection)} 个 int 为 100 和 200 的检测，无法得到错位表格检测结果")
                continue

            # 找到 100 下边界 200 上边界之间的 number
            inner_lower = min(match_int_detection[0].y2, match_int_detection[1].y2)
            inner_upper = max(match_int_detection[0].y1, match_int_detection[1].y1)

            number_detections = self._find_class_in_bbox(detected_table, self.detections, XTableElemType.TABLE_NUMBER)
            match_numbers = self._find_class_center_in_y_range(inner_lower, inner_upper, number_detections, XTableElemType.TABLE_NUMBER)

            if len(match_numbers) != 2:
                logger.warning(f"未能找到两个 number 的检测，无法得到错位表格检测结果")
                continue

            # 识别结果
            match_numbers = sorted(match_numbers, key=lambda det: det.x1)
            for detection in match_numbers:
                ocr_text = OcrEngine.find_best_text(detection, self.roi_ocr_result)
                ocr_text = OcrEngine.fix_ocr_float_separation(ocr_text)

                detected_table_result.number_data.append(DetectionResult(**detection.model_dump()).model_copy(update=dict(
                    original_text=ocr_text
                )))

        return misaligned_table_result

    def _handled_table_details(self, parent_detection: DetectionResult, details: List[DetectionResult]) -> List[DetectionResult]:
        filtered_details = []
        for detail in details:
            if detail.class_name == XTableElemType.TABLE_INFO:
                filtered_details.append(detail.map_to_original(parent_detection))
            elif detail.class_name == XTableElemType.TABLE_INT:
                filtered_details.append(detail.map_to_original(parent_detection))
            elif detail.class_name == XTableElemType.TABLE_NUMBER:
                filtered_details.append(detail.map_to_original(parent_detection))

        return filtered_details

    @staticmethod
    def parse_table_info(bbox, text: str) -> "TableInfo":
        clean_text = text.replace(" ", "").replace("\r", "")

        # 孔号
        hole_number = re.search(r"(孔号)[:：]?([A-Za-z0-9\-]+)", clean_text)
        hole_number = hole_number.group(2) if hole_number else ""

        # 土样号
        soil_number = re.search(r"(土样号|土样编号)[:：]?(\d+)", clean_text)
        soil_number = soil_number.group(2) if soil_number else ""

        # 深度（支持 m 或 cm）
        depth_match = re.search(r"(深度|垂直.*?)[：:]?([\d.]+)\s*(m|cm)?", clean_text)
        soil_depth = 0.0
        if depth_match:
            value = float(depth_match.group(2))
            unit = depth_match.group(3)
            soil_depth = value / 100 if unit == "cm" else value

        # 土层名称/类型
        soil_type_match = re.search(r"(土层名称|土样类型)[:：]?([^\n]+)", text)
        soil_type = soil_type_match.group(2).strip() if soil_type_match else ""

        # 土层层号
        soil_layer_match = re.search(r"(层号|土层层号)[:：]?([A-Za-z0-9\-\u4e00-\u9fa5]+)", clean_text)
        soil_layer = soil_layer_match.group(2) if soil_layer_match else ""

        # 土样数
        total_sample_match = re.search(r"(土样数|样本数)[:：]?(\d+)", clean_text)
        total_soil_sampled = int(total_sample_match.group(2)) if total_sample_match else -1

        return TableInfo(
            class_name=XTableElemType.TABLE_INFO,
            original_text=text,
            soil_layer=soil_layer,
            soil_type=soil_type,
            soil_number=soil_number,
            soil_depth=soil_depth,
            hole_number=hole_number,
            total_soil_sampled=total_soil_sampled
        )

    def _find_table_info(self, image, detected_table) -> Optional[TableInfo]:

        info_detection = self._find_class_in_bbox(detected_table, self.detections, XTableElemType.TABLE_INFO)

        if len(info_detection) == 0:
            logger.warning(f"未能找到 table_info 的检测")
            return None

        if len(info_detection) > 1:
            logger.warning(f"检测到多个 table_info 的检测，默认使用第一个")
        info_detection = info_detection[0]

        text_items = OcrEngine.find_inner_item(info_detection, self.roi_ocr_result)
        text_items = BoundingBox.sort_bboxes(text_items)
        text = "\n".join([item.text for item in text_items])

        # 用解析器提取字段
        table_info = self.parse_table_info(info_detection.bbox, text)

        # 补充检测的 meta 信息
        table_info.class_name = XTableElemType.TABLE_INFO
        table_info.confidence = info_detection.confidence

        return table_info


if __name__ == '__main__':
    image_path = "/Users/<USER>/CodeRepo/08-CADWithAi/data/images/drawing_classify/train/错位表格成果表/0911-shang_page_110.png"
    report_config = XTableVisionComputer(confidence_threshold=0.4).detect("test.xlsx", image_path)

    print(report_config)
