import cv2
import numpy as np
from typing import List, Tuple, Optional, Dict
import logging
from collections import Counter
from sklearn.cluster import DBSCAN
import warnings

warnings.filterwarnings('ignore')


class EnhancedTableLineExtractor:
    def __init__(self):
        """增强的表格竖线提取器 - 减少遗漏，提高召回率"""
        self.debug_mode = False

    def extract_vertical_lines(self, image_path: str, height_threshold: float = 0.65,
                               use_left_half: bool = True, sensitivity: str = 'medium') -> List[
        Tuple[int, int, int, int]]:
        """
        增强的竖线提取方法 - 多轮检测，减少遗漏

        Args:
            image_path (str): 图片路径
            height_threshold (float): 高度阈值
            use_left_half (bool): 是否只使用左半部分
            sensitivity (str): 敏感度 'low', 'medium', 'high'

        Returns:
            List[Tuple[int, int, int, int]]: 增强检测的竖线坐标列表
        """
        try:
            # 加载和预处理图片
            image = cv2.imread(image_path)
            if image is None:
                raise FileNotFoundError(f"无法找到图片文件: {image_path}")

            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            print(f"原始图片尺寸: {gray.shape[1]}x{gray.shape[0]}")

            # 预处理
            processed_gray = self._enhanced_preprocess(gray, use_left_half)
            print(f"处理后尺寸: {processed_gray.shape[1]}x{processed_gray.shape[0]}")

            # 根据敏感度设置参数
            params = self._get_enhanced_params(sensitivity)

            # 计算参数
            table_height, table_width = processed_gray.shape
            min_line_length = int(height_threshold * table_height)
            print(f"最小线条长度要求: {min_line_length}px")

            # === 第一轮：基础检测 ===
            print(f"\n=== 第1轮：基础检测 ===")
            base_lines = self._comprehensive_base_detection(processed_gray, min_line_length, params)
            print(f"基础检测: {len(base_lines)} 条线")

            if len(base_lines) == 0:
                print("基础检测未找到线条，尝试降低标准")
                relaxed_params = self._get_enhanced_params('high')
                base_lines = self._comprehensive_base_detection(processed_gray, int(min_line_length * 0.7),
                                                                relaxed_params)
                print(f"降低标准检测: {len(base_lines)} 条线")

            if len(base_lines) == 0:
                return []

            # 验证基础线条
            verified_base = self._enhanced_verification(base_lines, processed_gray, params)
            print(f"基础验证后: {len(verified_base)} 条线")

            # === 第二轮：模式推理检测 ===
            if len(verified_base) >= 2:
                print(f"\n=== 第2轮：模式推理检测 ===")
                pattern_lines = self._pattern_inference_detection(processed_gray, verified_base, min_line_length,
                                                                  params)
                print(f"模式推理检测: {len(pattern_lines)} 条新线")

                # 合并并验证
                all_candidates = verified_base + pattern_lines
                round2_verified = self._enhanced_verification(all_candidates, processed_gray, params)
                print(f"第2轮验证后: {len(round2_verified)} 条线")
            else:
                round2_verified = verified_base

            # === 第三轮：弱信号增强检测 ===
            if len(round2_verified) >= 2:
                print(f"\n=== 第3轮：弱信号增强检测 ===")
                weak_lines = self._weak_signal_enhancement(processed_gray, round2_verified, min_line_length, params)
                print(f"弱信号检测: {len(weak_lines)} 条新线")

                # 最终合并和验证
                all_final = round2_verified + weak_lines
                final_verified = self._enhanced_verification(all_final, processed_gray, params)
                print(f"第3轮验证后: {len(final_verified)} 条线")
            else:
                final_verified = round2_verified

            # === 第四轮：智能补全检测 ===
            if len(final_verified) >= 3:
                print(f"\n=== 第4轮：智能补全检测 ===")
                completion_lines = self._intelligent_completion(processed_gray, final_verified, min_line_length, params)
                print(f"智能补全检测: {len(completion_lines)} 条新线")

                if completion_lines:
                    all_completed = final_verified + completion_lines
                    completed_verified = self._enhanced_verification(all_completed, processed_gray, params)
                    print(f"补全验证后: {len(completed_verified)} 条线")
                    final_verified = completed_verified

            # 坐标还原
            final_lines = self._scale_back_coordinates(final_verified, gray, processed_gray, use_left_half)

            print(f"\n最终结果: {len(final_lines)} 条增强检测竖线")
            return final_lines

        except Exception as e:
            print(f"检测过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _get_enhanced_params(self, sensitivity: str) -> Dict:
        """获取增强参数"""
        if sensitivity == 'low':
            return {
                'projection_threshold_factor': 1.0,
                'verification_threshold': 0.45,
                'pattern_similarity_threshold': 0.6,
                'weak_signal_threshold': 0.35,
                'completion_threshold': 0.4,
                'min_peak_distance': 20,
                'hough_threshold': 65,
                'morphology_density': 0.35,
                'adaptive_factor': 1.2
            }
        elif sensitivity == 'medium':
            return {
                'projection_threshold_factor': 0.7,
                'verification_threshold': 0.35,
                'pattern_similarity_threshold': 0.5,
                'weak_signal_threshold': 0.25,
                'completion_threshold': 0.3,
                'min_peak_distance': 18,
                'hough_threshold': 55,
                'morphology_density': 0.25,
                'adaptive_factor': 1.0
            }
        else:  # high
            return {
                'projection_threshold_factor': 0.4,
                'verification_threshold': 0.25,
                'pattern_similarity_threshold': 0.4,
                'weak_signal_threshold': 0.2,
                'completion_threshold': 0.25,
                'min_peak_distance': 15,
                'hough_threshold': 40,
                'morphology_density': 0.2,
                'adaptive_factor': 0.8
            }

    def _enhanced_preprocess(self, gray: np.ndarray, use_left_half: bool) -> np.ndarray:
        """增强预处理"""
        # 裁剪左半部分
        if use_left_half:
            width = gray.shape[1]
            crop_width = int(width * 0.55)
            gray = gray[:, :crop_width]
            print(f"裁剪为左半部分: {gray.shape[1]}x{gray.shape[0]}")

        # 保守缩放
        if gray.shape[0] > 2500:
            scale_factor = 0.75  # 更保守的缩放
            new_height = int(gray.shape[0] * scale_factor)
            new_width = int(gray.shape[1] * scale_factor)
            gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_AREA)
            print(f"缩放到: {gray.shape[1]}x{gray.shape[0]}")

        # 多级预处理
        # 1. 保边缘去噪
        denoised = cv2.bilateralFilter(gray, 5, 60, 60)

        # 2. 增强对比度
        clahe = cv2.createCLAHE(clipLimit=2.5, tileGridSize=(8, 8))
        enhanced = clahe.apply(denoised)

        # 3. 轻微锐化
        kernel = np.array([[-0.5, -1, -0.5], [-1, 7, -1], [-0.5, -1, -0.5]])
        sharpened = cv2.filter2D(enhanced, -1, kernel)

        return sharpened

    def _comprehensive_base_detection(self, gray: np.ndarray, min_line_length: int, params: Dict) -> List[
        Tuple[int, int, int, int]]:
        """综合基础检测"""
        all_lines = []

        # 方法1：增强投影分析
        proj_lines = self._enhanced_projection_analysis(gray, min_line_length, params)
        all_lines.extend(proj_lines)
        print(f"  增强投影分析: {len(proj_lines)} 条线")

        # 方法2：多参数霍夫变换
        hough_lines = self._multi_parameter_hough(gray, min_line_length, params)
        all_lines.extend(hough_lines)
        print(f"  多参数霍夫: {len(hough_lines)} 条线")

        # 方法3：增强形态学
        morph_lines = self._enhanced_morphology(gray, min_line_length, params)
        all_lines.extend(morph_lines)
        print(f"  增强形态学: {len(morph_lines)} 条线")

        # 方法4：轮廓分析
        contour_lines = self._contour_based_detection(gray, min_line_length, params)
        all_lines.extend(contour_lines)
        print(f"  轮廓分析: {len(contour_lines)} 条线")

        # 智能合并
        if all_lines:
            merged = self._intelligent_merge_v2(all_lines, min_spacing=10)
            print(f"  智能合并: {len(merged)} 条线")
            return merged

        return all_lines

    def _enhanced_projection_analysis(self, gray: np.ndarray, min_line_length: int, params: Dict) -> List[
        Tuple[int, int, int, int]]:
        """增强投影分析"""
        lines = []
        height, width = gray.shape

        # 多种二值化组合
        binaries = []

        # OTSU二值化
        _, otsu = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        binaries.append(('OTSU', otsu))

        # 自适应二值化
        adaptive = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 15, 3)
        binaries.append(('Adaptive', adaptive))

        # 基于梯度的二值化
        sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        sobel_binary = np.zeros_like(gray)
        sobel_binary[np.abs(sobelx) > np.mean(np.abs(sobelx)) + 0.5 * np.std(np.abs(sobelx))] = 255
        binaries.append(('Sobel', sobel_binary.astype(np.uint8)))

        for name, binary in binaries:
            # 去除水平线
            h_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (width // 50, 1))
            h_lines = cv2.morphologyEx(binary, cv2.MORPH_OPEN, h_kernel)
            clean_binary = cv2.subtract(binary, h_lines)

            # 轻微去噪
            clean_binary = cv2.morphologyEx(clean_binary, cv2.MORPH_OPEN, np.ones((2, 2), np.uint8))

            # 投影分析
            projection = np.sum(clean_binary, axis=0)

            # 动态阈值
            mean_proj = np.mean(projection)
            std_proj = np.std(projection)
            threshold = mean_proj + params['projection_threshold_factor'] * params['adaptive_factor'] * std_proj

            # 多尺度平滑
            for window in [3, 5, 7]:
                smoothed = self._smooth_projection(projection, window)
                peaks = self._find_enhanced_peaks(smoothed, threshold, params['min_peak_distance'])

                for x in peaks:
                    extracted = self._extract_enhanced_line(clean_binary, x, min_line_length, gray)
                    lines.extend(extracted)

        return lines

    def _find_enhanced_peaks(self, smoothed: np.ndarray, threshold: float, min_distance: int) -> List[int]:
        """增强峰值检测"""
        peaks = []

        for i in range(3, len(smoothed) - 3):
            # 多点比较确保是峰值
            if (smoothed[i] > smoothed[i - 1] and smoothed[i] > smoothed[i + 1] and
                    smoothed[i] > smoothed[i - 2] and smoothed[i] > smoothed[i + 2] and
                    smoothed[i] > smoothed[i - 3] and smoothed[i] > smoothed[i + 3] and
                    smoothed[i] > threshold):

                # 检查距离
                if not peaks or min([abs(i - p) for p in peaks]) >= min_distance:
                    peaks.append(i)

        return peaks

    def _extract_enhanced_line(self, binary: np.ndarray, x: int, min_line_length: int, original_gray: np.ndarray) -> \
    List[Tuple[int, int, int, int]]:
        """增强线条提取"""
        lines = []
        height, width = binary.shape

        if x < 2 or x >= width - 2:
            return lines

        best_line = None
        best_score = 0

        # 扩大搜索范围
        for dx in range(-3, 4):
            search_x = x + dx
            if 0 <= search_x < width:
                column = binary[:, search_x]

                # 查找连续段
                segments = self._find_enhanced_segments(column, min_gap=8, min_length=min_line_length)

                for start_y, end_y in segments:
                    segment_length = end_y - start_y + 1

                    # 增强评分
                    segment_pixels = column[start_y:end_y + 1]
                    continuity = np.count_nonzero(segment_pixels) / len(segment_pixels)
                    length_ratio = segment_length / height

                    # 考虑原始灰度值
                    if 0 <= search_x < original_gray.shape[1]:
                        orig_column = original_gray[start_y:end_y + 1, search_x]
                        gray_contrast = np.std(orig_column) / 255.0
                        gray_score = 1.0 - np.mean(orig_column) / 255.0  # 暗线条得分更高
                    else:
                        gray_contrast = 0.5
                        gray_score = 0.5

                    # 综合评分
                    score = (segment_length * 0.4 +
                             continuity * segment_length * 0.3 +
                             length_ratio * height * 0.2 +
                             gray_contrast * segment_length * 0.05 +
                             gray_score * segment_length * 0.05)

                    if score > best_score:
                        best_score = score
                        best_line = (search_x, start_y, search_x, end_y)

        # 降低阈值要求
        if best_line and best_score > min_line_length * 0.3:
            lines.append(best_line)

        return lines

    def _find_enhanced_segments(self, column: np.ndarray, min_gap: int, min_length: int) -> List[Tuple[int, int]]:
        """增强段查找"""
        segments = []
        nonzero_indices = np.where(column > 0)[0]

        if len(nonzero_indices) == 0:
            return segments

        # 更智能的连接算法
        current_segment = [nonzero_indices[0]]

        for i in range(1, len(nonzero_indices)):
            gap = nonzero_indices[i] - nonzero_indices[i - 1]

            # 动态调整连接距离
            adaptive_gap = min_gap
            if len(current_segment) > min_length // 2:  # 如果已有较长段，允许更大的间隙
                adaptive_gap = min_gap * 1.5

            if gap <= adaptive_gap:
                current_segment.append(nonzero_indices[i])
            else:
                # 结束当前段
                if len(current_segment) > 0:
                    start_y = min(current_segment)
                    end_y = max(current_segment)
                    if end_y - start_y + 1 >= min_length * 0.8:  # 稍微降低要求
                        segments.append((start_y, end_y))
                current_segment = [nonzero_indices[i]]

        # 处理最后一段
        if len(current_segment) > 0:
            start_y = min(current_segment)
            end_y = max(current_segment)
            if end_y - start_y + 1 >= min_length * 0.8:
                segments.append((start_y, end_y))

        return segments

    def _multi_parameter_hough(self, gray: np.ndarray, min_line_length: int, params: Dict) -> List[
        Tuple[int, int, int, int]]:
        """多参数霍夫变换"""
        lines = []

        # 多种边缘检测
        edge_methods = [
            ('Canny1', cv2.Canny(gray, 30, 100)),
            ('Canny2', cv2.Canny(gray, 40, 120)),
            ('Canny3', cv2.Canny(gray, 50, 140))
        ]

        # 多种霍夫参数
        hough_params = [
            {'threshold': params['hough_threshold'], 'minLineLength': int(min_line_length * 0.9), 'maxLineGap': 15},
            {'threshold': params['hough_threshold'] - 10, 'minLineLength': int(min_line_length * 0.8),
             'maxLineGap': 20},
            {'threshold': params['hough_threshold'] - 20, 'minLineLength': int(min_line_length * 0.7), 'maxLineGap': 25}
        ]

        for edge_name, edges in edge_methods:
            for hp in hough_params:
                hough_lines = cv2.HoughLinesP(edges, rho=1, theta=np.pi / 180, **hp)

                if hough_lines is not None:
                    for line in hough_lines:
                        x1, y1, x2, y2 = line[0]

                        # 角度检查
                        angle = self._calculate_angle(x1, y1, x2, y2)
                        if abs(angle - 90) < 20:  # 放宽角度要求
                            line_length = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
                            if line_length >= min_line_length * 0.7:
                                if y1 > y2:
                                    x1, y1, x2, y2 = x2, y2, x1, y1
                                lines.append((x1, y1, x2, y2))

        return lines

    def _enhanced_morphology(self, gray: np.ndarray, min_line_length: int, params: Dict) -> List[
        Tuple[int, int, int, int]]:
        """增强形态学检测"""
        lines = []

        # 多种二值化
        binaries = []
        _, otsu = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        binaries.append(otsu)

        # 自定义阈值
        mean_val = np.mean(gray)
        _, custom = cv2.threshold(gray, mean_val - 15, 255, cv2.THRESH_BINARY_INV)
        binaries.append(custom)

        for binary in binaries:
            # 多种垂直核
            kernel_heights = [min_line_length // 8, min_line_length // 6, min_line_length // 4, min_line_length // 3]

            for kh in kernel_heights:
                if kh < 8:
                    continue

                v_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, kh))
                morphed = cv2.morphologyEx(binary, cv2.MORPH_OPEN, v_kernel)

                # 连接组件分析
                contours, _ = cv2.findContours(morphed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                for contour in contours:
                    x, y, w, h = cv2.boundingRect(contour)

                    # 更宽松的筛选
                    if (h >= int(min_line_length * 0.7) and
                            h > w * 3 and
                            w <= 10 and
                            h <= gray.shape[0] * 0.95):

                        # 验证质量
                        mask = np.zeros(gray.shape, dtype=np.uint8)
                        cv2.fillPoly(mask, [contour], 255)
                        contour_pixels = cv2.bitwise_and(binary, mask)
                        density = np.count_nonzero(contour_pixels) / (w * h)

                        if density > params['morphology_density']:
                            center_x = x + w // 2
                            lines.append((center_x, y, center_x, y + h))

        return lines

    def _contour_based_detection(self, gray: np.ndarray, min_line_length: int, params: Dict) -> List[
        Tuple[int, int, int, int]]:
        """基于轮廓的检测"""
        lines = []

        # 边缘检测
        edges = cv2.Canny(gray, 40, 120)

        # 形态学处理连接断裂的边缘
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 5))
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

        # 寻找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        for contour in contours:
            # 拟合直线
            if len(contour) >= 20:  # 需要足够的点
                [vx, vy, x, y] = cv2.fitLine(contour, cv2.DIST_L2, 0, 0.01, 0.01)

                # 检查是否接近垂直
                if abs(vx) < 0.3:  # 接近垂直
                    # 计算轮廓的边界
                    pts = contour.reshape(-1, 2)
                    y_min, y_max = np.min(pts[:, 1]), np.max(pts[:, 1])
                    x_mean = np.mean(pts[:, 0])

                    line_length = y_max - y_min
                    if line_length >= min_line_length * 0.6:
                        lines.append((int(x_mean), int(y_min), int(x_mean), int(y_max)))

        return lines

    def _pattern_inference_detection(self, gray: np.ndarray, known_lines: List[Tuple[int, int, int, int]],
                                     min_line_length: int, params: Dict) -> List[Tuple[int, int, int, int]]:
        """模式推理检测"""
        if len(known_lines) < 2:
            return []

        lines = []
        height, width = gray.shape

        # 分析已知线条模式
        x_positions = sorted([line[0] for line in known_lines])

        # 计算间距统计
        spacings = [x_positions[i + 1] - x_positions[i] for i in range(len(x_positions) - 1)]

        if not spacings:
            return []

        # 使用聚类分析间距模式
        spacings_array = np.array(spacings).reshape(-1, 1)

        # 如果间距变化不大，使用均值；否则使用聚类
        if np.std(spacings) < np.mean(spacings) * 0.4:
            # 规律间距
            median_spacing = np.median(spacings)
            candidate_positions = []

            # 在间隙中寻找
            for i in range(len(x_positions) - 1):
                left_x = x_positions[i]
                right_x = x_positions[i + 1]
                gap = right_x - left_x

                if gap > median_spacing * 1.2:  # 可能缺失竖线的间隙
                    # 计算可能的竖线位置
                    n_missing = int(gap / median_spacing) - 1
                    for j in range(1, n_missing + 1):
                        candidate_x = left_x + j * gap // (n_missing + 1)
                        candidate_positions.append(candidate_x)

            # 向两端扩展
            if len(x_positions) >= 3:
                # 左端扩展
                leftmost = min(x_positions)
                for i in range(1, 3):  # 最多扩展2个位置
                    x = leftmost - i * median_spacing
                    if x > median_spacing // 2:
                        candidate_positions.append(int(x))

                # 右端扩展
                rightmost = max(x_positions)
                for i in range(1, 3):
                    x = rightmost + i * median_spacing
                    if x < width - median_spacing // 2:
                        candidate_positions.append(int(x))

            print(f"    规律间距模式，找到 {len(candidate_positions)} 个候选位置")

        else:
            # 不规律间距，使用聚类
            if len(spacings) >= 3:
                try:
                    clustering = DBSCAN(eps=max(5, np.std(spacings) * 0.5), min_samples=1).fit(spacings_array)
                    labels = clustering.labels_

                    # 找到最大的聚类
                    unique_labels, counts = np.unique(labels, return_counts=True)
                    main_cluster = unique_labels[np.argmax(counts)]
                    main_spacing = np.mean([spacings[i] for i in range(len(spacings)) if labels[i] == main_cluster])

                    candidate_positions = []
                    # 基于主要间距模式寻找候选位置
                    for i in range(len(x_positions) - 1):
                        left_x = x_positions[i]
                        right_x = x_positions[i + 1]
                        gap = right_x - left_x

                        if gap > main_spacing * 1.3:
                            candidate_x = left_x + gap // 2
                            candidate_positions.append(candidate_x)

                    print(f"    聚类间距模式，找到 {len(candidate_positions)} 个候选位置")
                except:
                    candidate_positions = []
                    print(f"    聚类失败，使用简单模式")
            else:
                candidate_positions = []

        # 在候选位置检测竖线
        for expected_x in candidate_positions:
            search_range = max(10, int(np.std(spacings)) if spacings else 15)

            best_line = None
            best_score = 0

            for dx in range(-search_range, search_range + 1, 2):
                search_x = expected_x + dx

                if 5 < search_x < width - 5:
                    candidate_line = self._pattern_based_detection(gray, search_x, min_line_length, known_lines)

                    if candidate_line:
                        similarity = self._calculate_pattern_similarity(candidate_line, known_lines, gray, params)

                        if similarity > best_score:
                            best_score = similarity
                            best_line = candidate_line

            if best_line and best_score > params['pattern_similarity_threshold']:
                lines.append(best_line)
                print(f"      在 x={best_line[0]} 找到模式推理线条，评分: {best_score:.2f}")

        return lines

    def _pattern_based_detection(self, gray: np.ndarray, x: int, min_line_length: int,
                                 known_lines: List[Tuple[int, int, int, int]]) -> Optional[Tuple[int, int, int, int]]:
        """基于模式的检测"""
        if x < 0 or x >= gray.shape[1]:
            return None

        column = gray[:, x]

        # 基于已知线条创建自适应阈值
        known_intensities = []
        for kx, ky1, _, ky2 in known_lines[:5]:  # 使用前5条线的统计
            if 0 <= kx < gray.shape[1] and ky1 < ky2:
                try:
                    known_col = gray[ky1:ky2 + 1, kx]
                    known_intensities.extend(known_col.flatten())
                except:
                    pass

        if not known_intensities:
            return None

        known_mean = np.mean(known_intensities)
        known_std = np.std(known_intensities)

        # 多种阈值策略
        thresholds = [
            known_mean - 0.5 * known_std,
            known_mean - 0.3 * known_std,
            known_mean,
            np.percentile(known_intensities, 60)
        ]

        best_line = None
        best_score = 0

        for thresh in thresholds:
            if thresh > 0:
                _, binary_col = cv2.threshold(column, thresh, 255, cv2.THRESH_BINARY_INV)
                segments = self._find_enhanced_segments(binary_col, min_gap=10, min_length=int(min_line_length * 0.7))

                for start_y, end_y in segments:
                    segment_length = end_y - start_y + 1
                    continuity = np.count_nonzero(binary_col[start_y:end_y + 1]) / segment_length

                    # 与已知线条长度的相似性
                    known_lengths = [abs(kline[3] - kline[1]) for kline in known_lines]
                    avg_known_length = np.mean(known_lengths)
                    length_similarity = max(0, 1.0 - abs(segment_length - avg_known_length) / (avg_known_length + 1))

                    score = segment_length * continuity * 0.7 + length_similarity * segment_length * 0.3

                    if score > best_score:
                        best_score = score
                        best_line = (x, start_y, x, end_y)

        return best_line if best_score > min_line_length * 0.4 else None

    def _calculate_pattern_similarity(self, line: Tuple[int, int, int, int],
                                      known_lines: List[Tuple[int, int, int, int]],
                                      gray: np.ndarray, params: Dict) -> float:
        """计算模式相似度"""
        x, y1, _, y2 = line
        line_length = y2 - y1

        # 长度相似度
        known_lengths = [abs(kline[3] - kline[1]) for kline in known_lines]
        avg_length = np.mean(known_lengths)
        std_length = np.std(known_lengths)

        length_similarity = max(0, 1.0 - abs(line_length - avg_length) / (avg_length + 1))

        # 强度模式相似度
        try:
            line_column = gray[y1:y2 + 1, x]
            line_mean = np.mean(line_column)
            line_std = np.std(line_column)

            # 与已知线条的强度比较
            known_means = []
            known_stds = []

            for kx, ky1, _, ky2 in known_lines[:3]:
                if 0 <= kx < gray.shape[1] and ky1 < ky2:
                    try:
                        k_col = gray[ky1:ky2 + 1, kx]
                        known_means.append(np.mean(k_col))
                        known_stds.append(np.std(k_col))
                    except:
                        pass

            if known_means:
                avg_known_mean = np.mean(known_means)
                intensity_similarity = max(0, 1.0 - abs(line_mean - avg_known_mean) / 255.0)
            else:
                intensity_similarity = 0.5

        except:
            intensity_similarity = 0.3

        # 位置合理性（是否在预期位置附近）
        x_positions = sorted([kline[0] for kline in known_lines])
        position_score = 0.8  # 基础分数

        # 综合相似度
        total_similarity = (length_similarity * 0.4 +
                            intensity_similarity * 0.4 +
                            position_score * 0.2)

        return max(0, total_similarity)

    def _weak_signal_enhancement(self, gray: np.ndarray, known_lines: List[Tuple[int, int, int, int]],
                                 min_line_length: int, params: Dict) -> List[Tuple[int, int, int, int]]:
        """弱信号增强检测"""
        lines = []
        height, width = gray.shape

        # 基于已知线条创建强度模板
        intensity_template = self._create_intensity_template(gray, known_lines)
        if intensity_template is None:
            return []

        # 在已知线条之间的区域搜索弱信号
        x_positions = sorted([line[0] for line in known_lines])

        search_regions = []
        for i in range(len(x_positions) - 1):
            left_x = x_positions[i]
            right_x = x_positions[i + 1]
            gap = right_x - left_x

            if gap > 30:  # 足够大的间隙才搜索
                # 在间隙中搜索
                step = max(5, gap // 10)
                for search_x in range(left_x + step, right_x, step):
                    search_regions.append(search_x)

        print(f"    弱信号搜索区域: {len(search_regions)} 个位置")

        for search_x in search_regions:
            if 0 <= search_x < width:
                weak_line = self._detect_weak_signal(gray, search_x, min_line_length, intensity_template, params)
                if weak_line:
                    # 验证弱信号的质量
                    quality = self._evaluate_weak_signal_quality(weak_line, gray, known_lines)
                    if quality > params['weak_signal_threshold']:
                        lines.append(weak_line)
                        print(f"      在 x={weak_line[0]} 找到弱信号线条，质量: {quality:.2f}")

        return lines

    def _create_intensity_template(self, gray: np.ndarray, known_lines: List[Tuple[int, int, int, int]]) -> Optional[
        Dict]:
        """创建强度模板"""
        if len(known_lines) < 2:
            return None

        intensities = []
        gradients = []

        for x, y1, _, y2 in known_lines[:3]:  # 使用前3条线
            if 0 <= x < gray.shape[1] and y1 < y2:
                try:
                    column = gray[y1:y2 + 1, x]
                    intensities.extend(column.flatten())

                    # 计算梯度
                    if len(column) > 2:
                        grad = np.gradient(column.astype(float))
                        gradients.extend(grad)
                except:
                    pass

        if not intensities:
            return None

        return {
            'mean_intensity': np.mean(intensities),
            'std_intensity': np.std(intensities),
            'percentiles': np.percentile(intensities, [10, 25, 50, 75, 90]),
            'mean_gradient': np.mean(np.abs(gradients)) if gradients else 0,
            'std_gradient': np.std(gradients) if gradients else 0
        }

    def _detect_weak_signal(self, gray: np.ndarray, x: int, min_line_length: int,
                            template: Dict, params: Dict) -> Optional[Tuple[int, int, int, int]]:
        """检测弱信号"""
        if x < 0 or x >= gray.shape[1]:
            return None

        column = gray[:, x]

        # 基于模板的多阈值检测
        mean_int = template['mean_intensity']
        std_int = template['std_intensity']

        # 更宽松的阈值
        thresholds = [
            mean_int + 0.5 * std_int,  # 更宽松
            mean_int,
            template['percentiles'][3],  # 75分位数
            template['percentiles'][2],  # 中位数
        ]

        best_line = None
        best_score = 0

        for thresh in thresholds:
            if thresh > 0:
                # 使用软阈值
                soft_binary = np.zeros_like(column)
                mask = column <= thresh
                soft_binary[mask] = 255

                # 形态学处理连接断裂
                kernel = np.ones(3, np.uint8)
                soft_binary = cv2.morphologyEx(soft_binary, cv2.MORPH_CLOSE, kernel)

                segments = self._find_enhanced_segments(soft_binary, min_gap=15, min_length=int(min_line_length * 0.6))

                for start_y, end_y in segments:
                    segment_length = end_y - start_y + 1

                    # 弱信号特殊评分
                    segment_pixels = soft_binary[start_y:end_y + 1]
                    continuity = np.count_nonzero(segment_pixels) / len(segment_pixels)

                    # 强度一致性
                    segment_column = column[start_y:end_y + 1]
                    intensity_consistency = 1.0 - np.std(segment_column) / (np.mean(segment_column) + 1)

                    score = (segment_length * 0.5 +
                             continuity * segment_length * 0.3 +
                             intensity_consistency * segment_length * 0.2)

                    if score > best_score:
                        best_score = score
                        best_line = (x, start_y, x, end_y)

        return best_line if best_score > min_line_length * 0.25 else None

    def _evaluate_weak_signal_quality(self, line: Tuple[int, int, int, int], gray: np.ndarray,
                                      known_lines: List[Tuple[int, int, int, int]]) -> float:
        """评估弱信号质量"""
        x, y1, _, y2 = line
        line_length = y2 - y1

        # 长度评分
        known_lengths = [abs(kline[3] - kline[1]) for kline in known_lines]
        avg_length = np.mean(known_lengths)
        length_score = max(0, 1.0 - abs(line_length - avg_length) / (avg_length + 1))

        # 位置合理性评分
        x_positions = sorted([kline[0] for kline in known_lines])
        position_score = 0.5  # 基础分

        # 检查是否在合理的间距范围内
        for kx in x_positions:
            distance = abs(x - kx)
            if 20 <= distance <= 200:  # 合理的距离范围
                position_score = 0.8
                break

        # 强度一致性评分
        try:
            column = gray[y1:y2 + 1, x]
            intensity_score = 1.0 - np.std(column) / (np.mean(column) + 1)
        except:
            intensity_score = 0.3

        return length_score * 0.4 + position_score * 0.3 + intensity_score * 0.3

    def _intelligent_completion(self, gray: np.ndarray, known_lines: List[Tuple[int, int, int, int]],
                                min_line_length: int, params: Dict) -> List[Tuple[int, int, int, int]]:
        """智能补全检测"""
        lines = []

        if len(known_lines) < 3:
            return []

        # 深度间距分析
        x_positions = sorted([line[0] for line in known_lines])
        spacings = [x_positions[i + 1] - x_positions[i] for i in range(len(x_positions) - 1)]

        # 统计分析
        spacing_counter = Counter(spacings)
        most_common_spacings = dict(spacing_counter.most_common(3))

        print(f"    间距统计: {most_common_spacings}")

        # 寻找异常大的间隙
        median_spacing = np.median(spacings)
        mean_spacing = np.mean(spacings)

        completion_candidates = []

        for i in range(len(x_positions) - 1):
            left_x = x_positions[i]
            right_x = x_positions[i + 1]
            gap = right_x - left_x

            # 如果间隙明显大于平均值，可能缺失竖线
            if gap > median_spacing * 1.6:
                # 估算可能的竖线数量
                estimated_lines = round(gap / median_spacing) - 1
                estimated_lines = min(estimated_lines, 3)  # 最多补3条

                if estimated_lines > 0:
                    for j in range(1, estimated_lines + 1):
                        candidate_x = left_x + j * gap // (estimated_lines + 1)
                        completion_candidates.append(candidate_x)
                        print(f"      补全候选位置: x={candidate_x} (间隙={gap}, 估算={estimated_lines}条)")

        # 在候选位置进行精细检测
        for candidate_x in completion_candidates:
            search_range = min(15, int(median_spacing * 0.2))

            best_completion = None
            best_score = 0

            for dx in range(-search_range, search_range + 1, 3):
                search_x = candidate_x + dx

                if 5 < search_x < gray.shape[1] - 5:
                    completion_line = self._detect_completion_line(gray, search_x, min_line_length, known_lines, params)

                    if completion_line:
                        score = self._evaluate_completion_quality(completion_line, gray, known_lines, median_spacing)

                        if score > best_score:
                            best_score = score
                            best_completion = completion_line

            if best_completion and best_score > params['completion_threshold']:
                lines.append(best_completion)
                print(f"      补全线条: x={best_completion[0]}, 评分: {best_score:.2f}")

        return lines

    def _detect_completion_line(self, gray: np.ndarray, x: int, min_line_length: int,
                                known_lines: List[Tuple[int, int, int, int]], params: Dict) -> Optional[
        Tuple[int, int, int, int]]:
        """检测补全线条"""
        if x < 0 or x >= gray.shape[1]:
            return None

        column = gray[:, x]

        # 基于已知线条的统计信息
        known_stats = self._analyze_known_lines_stats(gray, known_lines)

        # 使用统计信息指导检测
        mean_intensity = known_stats.get('mean_intensity', np.mean(column))
        std_intensity = known_stats.get('std_intensity', np.std(column))

        # 自适应阈值
        adaptive_thresholds = [
            mean_intensity - 0.3 * std_intensity,
            mean_intensity,
            mean_intensity + 0.3 * std_intensity,
            np.percentile(column, 40),
            np.percentile(column, 50),
            np.percentile(column, 60)
        ]

        best_line = None
        best_score = 0

        for thresh in adaptive_thresholds:
            if thresh > 0:
                _, binary_col = cv2.threshold(column, thresh, 255, cv2.THRESH_BINARY_INV)

                # 轻微的形态学处理
                kernel = np.ones(3, np.uint8)
                binary_col = cv2.morphologyEx(binary_col, cv2.MORPH_CLOSE, kernel)

                segments = self._find_enhanced_segments(binary_col, min_gap=12, min_length=int(min_line_length * 0.65))

                for start_y, end_y in segments:
                    segment_length = end_y - start_y + 1

                    # 补全线条的特殊评分
                    segment_pixels = binary_col[start_y:end_y + 1]
                    continuity = np.count_nonzero(segment_pixels) / len(segment_pixels)

                    # 与已知线条的长度相似性
                    known_lengths = [abs(kline[3] - kline[1]) for kline in known_lines]
                    avg_known_length = np.mean(known_lengths)
                    length_similarity = max(0, 1.0 - abs(segment_length - avg_known_length) / (avg_known_length + 1))

                    # 强度相似性
                    segment_column = column[start_y:end_y + 1]
                    intensity_similarity = max(0, 1.0 - abs(np.mean(segment_column) - mean_intensity) / 255.0)

                    score = (segment_length * 0.3 +
                             continuity * segment_length * 0.2 +
                             length_similarity * segment_length * 0.3 +
                             intensity_similarity * segment_length * 0.2)

                    if score > best_score:
                        best_score = score
                        best_line = (x, start_y, x, end_y)

        return best_line if best_score > min_line_length * 0.3 else None

    def _analyze_known_lines_stats(self, gray: np.ndarray, known_lines: List[Tuple[int, int, int, int]]) -> Dict:
        """分析已知线条统计信息"""
        all_intensities = []
        all_lengths = []

        for x, y1, _, y2 in known_lines:
            if 0 <= x < gray.shape[1] and y1 < y2:
                try:
                    column = gray[y1:y2 + 1, x]
                    all_intensities.extend(column.flatten())
                    all_lengths.append(y2 - y1)
                except:
                    pass

        if not all_intensities:
            return {}

        return {
            'mean_intensity': np.mean(all_intensities),
            'std_intensity': np.std(all_intensities),
            'mean_length': np.mean(all_lengths) if all_lengths else 0,
            'std_length': np.std(all_lengths) if all_lengths else 0,
            'intensity_percentiles': np.percentile(all_intensities, [25, 50, 75])
        }

    def _evaluate_completion_quality(self, line: Tuple[int, int, int, int], gray: np.ndarray,
                                     known_lines: List[Tuple[int, int, int, int]], median_spacing: float) -> float:
        """评估补全质量"""
        x, y1, _, y2 = line
        line_length = y2 - y1

        # 长度一致性
        known_lengths = [abs(kline[3] - kline[1]) for kline in known_lines]
        avg_length = np.mean(known_lengths)
        length_consistency = max(0, 1.0 - abs(line_length - avg_length) / (avg_length + 1))

        # 间距合理性
        x_positions = [kline[0] for kline in known_lines]
        distances = [abs(x - kx) for kx in x_positions]
        min_distance = min(distances)

        # 检查间距是否合理
        spacing_score = 0.5
        if median_spacing * 0.5 <= min_distance <= median_spacing * 2.0:
            spacing_score = 0.9
        elif min_distance > median_spacing * 2.0:
            spacing_score = 0.3

        # 强度一致性
        try:
            column = gray[y1:y2 + 1, x]
            column_mean = np.mean(column)
            column_std = np.std(column)

            # 与已知线条的强度比较
            known_means = []
            for kx, ky1, _, ky2 in known_lines[:3]:
                if 0 <= kx < gray.shape[1] and ky1 < ky2:
                    try:
                        k_col = gray[ky1:ky2 + 1, kx]
                        known_means.append(np.mean(k_col))
                    except:
                        pass

            if known_means:
                avg_known_mean = np.mean(known_means)
                intensity_consistency = max(0, 1.0 - abs(column_mean - avg_known_mean) / 255.0)
            else:
                intensity_consistency = 0.5

        except:
            intensity_consistency = 0.3

        return (length_consistency * 0.4 +
                spacing_score * 0.4 +
                intensity_consistency * 0.2)

    def _enhanced_verification(self, lines: List[Tuple[int, int, int, int]], gray: np.ndarray, params: Dict) -> List[
        Tuple[int, int, int, int]]:
        """增强验证"""
        if not lines:
            return lines

        verified_lines = []
        height, width = gray.shape

        print(f"  开始增强验证 {len(lines)} 条候选线")

        for i, (x, y1, _, y2) in enumerate(lines):
            if x < 0 or x >= width or y1 >= y2:
                continue

            line_length = y2 - y1

            # 多维度验证
            existence_score = self._calculate_enhanced_existence_score(gray, x, y1, y2)
            environment_score = self._calculate_enhanced_environment_score(gray, x, y1, y2)
            straightness_score = self._calculate_enhanced_straightness_score(gray, x, y1, y2)
            consistency_score = self._calculate_consistency_score(gray, x, y1, y2)

            # 加权综合评分
            total_score = (existence_score * 0.3 +
                           environment_score * 0.25 +
                           straightness_score * 0.25 +
                           consistency_score * 0.2)

            print(f"    线条{i + 1} x={x}: 存在={existence_score:.2f}, 环境={environment_score:.2f}, "
                  f"直线性={straightness_score:.2f}, 一致性={consistency_score:.2f}, 总分={total_score:.2f}")

            if total_score > params['verification_threshold']:
                verified_lines.append((x, y1, x, y2))
            else:
                print(f"      -> 未通过验证（评分: {total_score:.2f} < {params['verification_threshold']:.2f}）")

        return verified_lines

    def _calculate_enhanced_existence_score(self, gray: np.ndarray, x: int, y1: int, y2: int) -> float:
        """增强存在性评分"""
        if x < 0 or x >= gray.shape[1] or y1 >= y2:
            return 0.0

        column = gray[y1:y2 + 1, x]

        scores = []

        # 1. 多种二值化方法
        try:
            _, otsu = cv2.threshold(column, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            otsu_density = np.count_nonzero(otsu) / len(otsu)
            scores.append(otsu_density)
        except:
            pass

        # 2. 基于统计的阈值
        mean_val = np.mean(column)
        std_val = np.std(column)
        if std_val > 2:
            for factor in [0.2, 0.5, 0.8]:
                thresh_val = mean_val - factor * std_val
                if thresh_val > 0:
                    simple_binary = (column < thresh_val).astype(np.uint8) * 255
                    simple_density = np.count_nonzero(simple_binary) / len(simple_binary)
                    scores.append(simple_density)

        # 3. 基于梯度的检测
        if len(column) > 5:
            grad = np.abs(np.gradient(column.astype(float)))
            grad_threshold = np.mean(grad) + 0.5 * np.std(grad)
            grad_binary = (grad > grad_threshold).astype(np.uint8) * 255
            grad_density = np.count_nonzero(grad_binary) / len(grad_binary)
            scores.append(grad_density * 0.7)  # 梯度方法权重稍低

        return max(scores) if scores else 0.0

    def _calculate_enhanced_environment_score(self, gray: np.ndarray, x: int, y1: int, y2: int) -> float:
        """增强环境评分"""
        height, width = gray.shape

        # 扩大检查范围
        left_x = max(0, x - 8)
        right_x = min(width - 1, x + 8)

        if right_x - left_x < 5:
            return 0.6

        try:
            region = gray[y1:y2 + 1, left_x:right_x + 1]
            center_col_idx = x - left_x

            if center_col_idx >= region.shape[1]:
                return 0.5

            center_column = region[:, center_col_idx]

            # 多种环境分析
            scores = []

            # 1. 与左右邻近列的对比
            if center_col_idx > 0:
                left_col = region[:, center_col_idx - 1]
                left_contrast = np.mean(np.abs(center_column.astype(float) - left_col.astype(float))) / 255.0
                scores.append(min(1.0, left_contrast * 1.5))

            if center_col_idx < region.shape[1] - 1:
                right_col = region[:, center_col_idx + 1]
                right_contrast = np.mean(np.abs(center_column.astype(float) - right_col.astype(float))) / 255.0
                scores