import argparse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from loguru import logger

from aikit.providers.auto_discovery import auto_register_ai_models
from api.app_server import create_app


def parse_opt():
    parser = argparse.ArgumentParser()
    parser.add_argument('--host', type=str, default='0.0.0.0')
    parser.add_argument('--port', type=int, default=8000)
    return parser.parse_args()


def run_server():
    args = parse_opt()
    app = create_app()

    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 生产环境中应该设置具体的域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    auto_register_ai_models()
    logger.info("启动计算机视觉检测API服务...")
    uvicorn.run(app, host=args.host, port=args.port)


if __name__ == '__main__':
    run_server()
