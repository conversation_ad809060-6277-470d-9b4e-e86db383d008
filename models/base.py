from typing import Optional, List
from pydantic import Field

from utils.pdf import PdfTextItem
from vision.core import DetectionResult
from vision.core.data_types import CamelCaseModel, Point
from vision.ocr_engine.base import OcrResult


TABLE_ELEM_LABEL = "table"
TABLE_CELL = "my_table_cell"


class LeaderLine(CamelCaseModel):
    page_id: int
    bbox_id: str
    point: Point


class DetectedRespResult(DetectionResult):
    file_name: str = Field(..., description="文件名")
    detect_type: str = Field(..., description="检测类型")
    image_width: int = Field(..., description="原始图片宽度")
    image_height: int = Field(..., description="原始图片高度")


class VisionDetectedResult(DetectedRespResult):
    """检测的基础数据"""
    detected_results: Optional[list[DetectionResult]] = Field(default_factory=list, description="检测到的结果列表")
    ocr_result: Optional[OcrResult] = Field(..., description="OCR 识别结果")
    roi_ocr_result: Optional[OcrResult] = Field(default=None, description="ROI 区域 OCR 识别结果")
    leader_line: Optional[list[LeaderLine]] = Field(default_factory=list, description="引线信息")
    pdf_texts: Optional[List[PdfTextItem]] = Field(default_factory=list, description="PDF 元数据信息")
