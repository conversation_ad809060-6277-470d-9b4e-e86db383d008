"""
分类器相关数据模型
包含图像分类等相关的数据模型
"""

from pydantic import Field
from typing import List, Dict, Any, Optional

from models.base import CamelCaseModel


class ClassificationResult(CamelCaseModel):
    """图像分类结果模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="处理消息")
    predicted_class: str = Field(..., description="预测类别")
    confidence: float = Field(..., description="置信度")
    all_predictions: List[Dict[str, Any]] = Field(default=[], description="所有预测结果")
    file_id: str = Field(..., description="文件ID")
    filename: str = Field(..., description="文件名")
    timestamp: str = Field(..., description="处理时间戳")
    result_image_base64: Optional[str] = Field(None, description="结果图片的base64编码")
