from typing import Optional, List, Annotated, Union

from pydantic import Field

from models.legend_models import DetectedLegendResult
from models.misaligned_table_models import MisalignedTableResult
from models.plane_models import DetectedPlainResult
from models.section_models import DetectedSectionResult
from models.sheet_models import DetectedTableResult


LogicResult = Annotated[
    Union[
        DetectedLegendResult,
        MisalignedTableResult,
        DetectedPlainResult,
        DetectedSectionResult,
        DetectedTableResult,
    ],
    <PERSON>(discriminator="detect_type"),  # 关键！
]
