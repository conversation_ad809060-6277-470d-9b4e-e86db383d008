from typing import List, Literal
from pydantic import Field
from models.base import DetectedRespResult
from models.pdf_info_models import PdfPageType
from vision.core import DetectionResult


class LegendDetail(DetectionResult):
    legend_area: DetectionResult
    legend_text: DetectionResult


class DetectedLegendResult(DetectedRespResult):
    """检测的剖面数据"""
    detect_type: Literal["图例"] = PdfPageType.LEGEND.desc
    legends: List[LegendDetail] = Field(default_factory=list, description="图例列表")
