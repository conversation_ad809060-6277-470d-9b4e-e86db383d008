from typing import Optional, List, Literal

from pydantic import Field

from models.base import DetectedRespResult
from models.pdf_info_models import PdfPageType
from vision.core.data_types import CamelCaseModel, DetectionResult


class TableInfo(DetectionResult):
    """错位表格信息模型"""
    soil_layer: Optional[str] = Field("", description="土层")
    soil_type: Optional[str] = Field("", description="土样类型")
    soil_number: Optional[str] = Field("", description="土样编号")
    soil_depth: Optional[float] = Field(0, description="土样深度(m)")
    hole_number: Optional[str] = Field("", description="孔号")
    total_soil_sampled: Optional[int] = Field(-1, description="总取土样数")


class DetectedTableResult(CamelCaseModel):
    """检测的剖面数据"""
    table_info: Optional[TableInfo] = Field(None, description="表格信息")
    scales: Optional[List[DetectionResult]] = Field(default_factory=list, description="标尺对应的检测框")
    number_data: Optional[List[DetectionResult]] = Field(default_factory=list, description="数据的检测框位置")


class MisalignedTableResult(DetectedRespResult):
    """错位表格检测结果"""
    detect_type: Literal["错位表格成果表"] = PdfPageType.MISALIGNED_TABLE.desc
    detected_tables_result: Optional[List[DetectedTableResult]] = Field(None, description="错位表格检测结果")
