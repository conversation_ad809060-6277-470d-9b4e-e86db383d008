from enum import Enum


class PdfPageType(Enum):
     OTHER_RESULT = (0, "其他成果表")
     SECTION = (1, "剖面图")
     LEGEND = (2, "图例")
     RICH_TEXT = (3, "富文本")
     FLOOR_PLAN = (4, "平面图")
     TEXT = (5, "纯文本")
     TABLE = (6, "表格为主")
     DRILL_BAR_CHART = (7, "钻孔柱状图")
     MISALIGNED_TABLE = (8, "错位表格成果表")
     STATIC_PENETRATION = (9, "静力触探成果表")

     def __init__(self, code, desc):
          self.code = code
          self.desc = desc


class DataSourceType(str, Enum):
    PLANE = "勘探孔平面布置图"
    SECTION = "工程地质剖面图"
    DRILL_BAR_CHART = "钻孔柱状图"
    STATIC_PENETRATION_LAYER = "静力触探分层参数"
    STATIC_PENETRATION_TEST = "静力触探测试成果"
    UNDEFINED = "未定义"
