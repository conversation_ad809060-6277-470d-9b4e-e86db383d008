"""
报告相关数据模型
包含报告表头、结果表等数据模型
"""
from enum import Enum
from typing import Optional, List

from models.pdf_info_models import DataSourceType
from vision.core.data_types import CamelCaseModel
from pydantic import Field


class CompareDataType(str, Enum):
    HOLE_INFO = "勘探孔基本信息"
    HOLE_SPACE = "勘探孔的间距"
    LAYER_INFO = "层级信息"


class CheckingReport(CamelCaseModel):
    """审查报告数据模型"""
    name: str = Field(..., description="数据名称")
    result: Optional[str] = Field(..., description="审查结果")
    is_valid: Optional[bool] = Field(..., description="数据是否正确")
    source: Optional[str] = Field(..., description="数据来源")
    source_page: Optional[str] = Field(..., description="数据来源页码")
    reference: Optional[str] = Field(..., description="数据比对")
    reference_page: Optional[str] = Field(..., description="数据比对页码")
    usage: Optional[str] = Field(..., description="数据最终用途")
    original_value: Optional[str] = Field(default=None, description="源数据")
    compared_value: Optional[str] = Field(default=None, description="对照数据")
    comments: Optional[str] = Field(..., description="备注")


class CheckingReportEntity(CamelCaseModel):
    """审查报告实体数据模型"""
    checking_reports: Optional[List[CheckingReport]] = Field(..., description="审查报告")


class DataInfo(CamelCaseModel):
    """数据信息数据模型"""
    data_type: Optional[DataSourceType] = Field(default=None, description="数据类型")
    page_info: Optional[str] = Field(default=None, description="数据来源类型")


class DrillHoleInfo(DataInfo):
    """钻孔信息数据模型"""
    number: Optional[str] = Field(default=None, description="钻孔编号")
    hole_type: Optional[str] = Field(default=None, description="钻孔类型")
    surface_elevation: Optional[float] = Field(default=None, description="孔口标高(m)")
    depth: Optional[float] = Field(default=None, description="钻孔深度(m)")
    x_coordinate: Optional[float] = Field(default=None, description="钻孔X坐标")
    y_coordinate: Optional[float] = Field(default=None, description="钻孔Y坐标")


class LayerInfoData(DataInfo):
    """分层信息数据模型"""
    drill_id: str = Field(..., description="钻孔编号")
    depth: Optional[float] = Field(default=None, description="层深度(m)")
    elevation: Optional[float] = Field(default=None, description="层标高(m)")

