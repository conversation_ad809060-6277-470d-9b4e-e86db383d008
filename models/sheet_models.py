from typing import Optional, Union, Dict, List, Any, Literal
from pydantic import Field, model_validator, ConfigDict
from models.base import CamelCaseModel, DetectedRespResult
from models.pdf_info_models import PdfPageType
from models.report_models import DrillHoleInfo
from vision.core import DetectionResult
from vision.ocr_engine.base import OcrResult


class CellStyle(CamelCaseModel):
    font_name: Optional[str] = Field("Arial")
    font_size: Optional[int] = Field(11)
    font_bold: Optional[bool] = Field(False)
    font_italic: Optional[bool] = Field(False)
    font_color: Optional[str] = Field("#000000")
    background_color: Optional[str] = Field(None)
    border: Optional[bool] = False
    border_color: Optional[str] = Field("#000000")
    horizontal_align: Optional[str] = Field("left")  # left, center, right
    vertical_align: Optional[str] = Field("top")  # top, center, bottom
    number_format: Optional[str] = Field("General")


class MergeCell(CamelCaseModel):
    range: str = Field(..., description="合并单元格范围，如'A1:C1'")
    value: Optional[str] = None
    style: Optional[CellStyle] = None


class SheetConfig(CamelCaseModel):
    name: str = Field(..., description="工作表名称")

    # 支持多种数据格式
    data: Union[
        List[Dict[str, Any]],  # 原有格式：字典列表
        List[List[Any]],  # 新格式：二维数组
        List[Any]  # 一维数组
    ] = Field(..., description="数据，支持多种格式")

    # 表头配置
    headers: Optional[List[str]] = Field(default=None, description="表头列表，支持重复值")
    has_headers: bool = Field(True, description="数据是否包含表头")

    # 数据格式类型
    data_format: Optional[str] = Field(default="dict", description="数据格式：dict/array/mixed")

    merge_cells: Optional[List[MergeCell]] = Field(default_factory=list)
    freeze_panes: Optional[str] = Field(default=None)
    column_widths: Optional[Dict[str, float]] = Field(default_factory=dict)
    row_heights: Optional[Dict[int, float]] = Field(default_factory=dict)
    styles: Optional[Dict[str, CellStyle]] = Field(default_factory=dict, description="key 表示 Excel 范围（如 \"A1:D2\"），value 是 CellStyle")
    start_row: int = Field(default=1, description="数据起始行，1表示第一行")
    start_col: int = Field(default=1, description="数据起始列，1表示A列")

    @model_validator(mode="after")
    def set_data_format(self):
        data = self.data or []
        if not data:
            self.data_format = "dict"
        else:
            first_item = data[0]
            if isinstance(first_item, dict):
                self.data_format = "dict"
            elif isinstance(first_item, (list, tuple)):
                self.data_format = "array"
            else:
                self.data_format = "mixed"
        return self


class ReportConfig(CamelCaseModel):
    output_filename: str = Field(..., description="输出文件名")
    sheets: List[SheetConfig] = Field(..., description="工作表配置列表")


class DetectedTableResult(DetectedRespResult):
    """检测的表格数据"""
    detect_type: Literal["表格为主"] = PdfPageType.TABLE.desc
    output_filename: Optional[str] = Field(default="data.xlsx", description="输出文件名")
    table_type: Optional[str] = Field(default=None, description="表格类型")
    sheets: List[SheetConfig] = Field(default_factory=list)
    vision_results: List[DetectionResult] = Field(default_factory=list)
    ocr_result: Optional[OcrResult] = Field(default_factory=OcrResult)
    roi_ocr_result: Optional[OcrResult] = Field(default_factory=OcrResult)
    drill_hole_infos: Optional[List[DrillHoleInfo]] = Field(default_factory=list)