[project]
name = "ai-api"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "cnocr[ort-cpu]>=*******",
    "fastapi>=0.115.14",
    "fastapi-offline>=1.7.3",
    "loguru>=0.7.3",
    "numpy>=2.2.6",
    "onnxruntime>=1.22.0",
    "opencv-python>=*********",
    "paddleocr>=3.1.0",
    "paddlepaddle>=3.1.0",
    "pillow>=11.3.0",
    "python-multipart>=0.0.20",
    "rapidocr-onnxruntime>=1.4.4",
    "setuptools>=80.9.0",
    "uvicorn>=0.35.0",
    "pandas>=2.0.0",
    "openpyxl>=3.1.0",
    "rapidocr-onnxruntime>=1.4.4",
    "wired-table-rec>=1.2.0",
    "pytesseract>=0.3.13",
    "pymupdf>=1.26.4",
    "matplotlib>=3.10.3",
    "openai>=1.97.1",
    "pefile>=2024.8.26",
]

[[tool.uv.index]]
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
default = true
