#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import yaml
from pathlib import Path

# 项目根目录
ROOT = Path(__file__).resolve().parent

# 输出目录
OUTPUT = ROOT / "web_data/outputs"

# 确保输出目录存在
if not OUTPUT.exists():
    OUTPUT.mkdir(parents=True, exist_ok=True)


def load_yaml(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)


MODEL_CONFIG = load_yaml(ROOT / "config/model.yaml")
