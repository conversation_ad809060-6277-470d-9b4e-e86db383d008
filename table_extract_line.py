import cv2
import numpy as np
from typing import List, Tuple, Optional, Dict
import logging
from collections import Counter


class BalancedTableLineExtractor:
    def __init__(self):
        """平衡的表格竖线提取器 - 平衡精度与召回率"""
        self.debug_mode = False

    def extract_vertical_lines(self, image_path: str, height_threshold: float = 0.65,
                               use_left_half: bool = True, sensitivity: str = 'medium') -> List[
        Tuple[int, int, int, int]]:
        """
        平衡的竖线提取方法 - 减少假阳性但保证检测真实竖线

        Args:
            image_path (str): 图片路径
            height_threshold (float): 高度阈值，降低到0.65
            use_left_half (bool): 是否只使用左半部分
            sensitivity (str): 敏感度 'low', 'medium', 'high'

        Returns:
            List[Tuple[int, int, int, int]]: 平衡检测的竖线坐标列表
        """
        try:
            # 加载和预处理图片
            image = cv2.imread(image_path)
            if image is None:
                raise FileNotFoundError(f"无法找到图片文件: {image_path}")

            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            print(f"原始图片尺寸: {gray.shape[1]}x{gray.shape[0]}")

            # 预处理
            processed_gray = self._balanced_preprocess(gray, use_left_half)
            print(f"处理后尺寸: {processed_gray.shape[1]}x{processed_gray.shape[0]}")

            # cv2.imwrite("debug_preprocess.png", processed_gray)

            # 根据敏感度设置参数
            params = self._get_sensitivity_params(sensitivity)

            # 计算参数
            table_height, table_width = processed_gray.shape
            min_line_length = int(height_threshold * table_height)
            print(f"最小线条长度要求: {min_line_length}px")
            print(f"敏感度设置: {sensitivity}")

            # 第一阶段：适度保守的检测
            print(f"\n=== 第1阶段：适度保守检测 ===")
            conservative_lines = self._moderate_conservative_detection(processed_gray, min_line_length, params)
            print(f"适度保守检测: {len(conservative_lines)} 条线")

            if len(conservative_lines) == 0:
                print("适度保守检测未找到线条，尝试降低标准")
                # 使用更宽松的参数重新检测
                relaxed_params = self._get_sensitivity_params('high')
                conservative_lines = self._moderate_conservative_detection(processed_gray, int(min_line_length * 0.8),
                                                                           relaxed_params)
                print(f"宽松检测: {len(conservative_lines)} 条线")

            if len(conservative_lines) == 0:
                return []

            # 平衡的验证
            verified_lines = self._balanced_verification(conservative_lines, processed_gray, params)
            print(f"平衡验证后: {len(verified_lines)} 条线")

            # 第二阶段：智能增强检测
            if len(verified_lines) >= 2:
                print(f"\n=== 第2阶段：智能增强检测 ===")
                enhanced_lines = self._intelligent_enhancement(processed_gray, verified_lines, min_line_length, params)
                print(f"智能增强检测: {len(enhanced_lines)} 条新线")

                # 合并并再次验证
                all_candidates = verified_lines + enhanced_lines
                final_verified = self._balanced_verification(all_candidates, processed_gray, params)
                print(f"最终验证后: {len(final_verified)} 条线")
            else:
                final_verified = verified_lines

            # 最终处理和坐标还原
            final_lines = self._scale_back_coordinates(final_verified, gray, processed_gray, use_left_half)

            print(f"\n最终结果: {len(final_lines)} 条平衡检测竖线")
            return final_lines

        except Exception as e:
            print(f"检测过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _get_sensitivity_params(self, sensitivity: str) -> Dict:
        """根据敏感度获取参数"""
        if sensitivity == 'low':
            return {
                'projection_threshold_factor': 1.2,
                'verification_threshold': 0.5,
                'environment_weight': 0.2,
                'straightness_weight': 0.3,
                'existence_weight': 0.5,
                'min_peak_distance': 25,
                'hough_threshold': 70,
                'morphology_density': 0.4
            }
        elif sensitivity == 'medium':
            return {
                'projection_threshold_factor': 0.8,
                'verification_threshold': 0.4,
                'environment_weight': 0.25,
                'straightness_weight': 0.35,
                'existence_weight': 0.4,
                'min_peak_distance': 20,
                'hough_threshold': 60,
                'morphology_density': 0.3
            }
        else:  # high
            return {
                'projection_threshold_factor': 0.5,
                'verification_threshold': 0.3,
                'environment_weight': 0.3,
                'straightness_weight': 0.3,
                'existence_weight': 0.4,
                'min_peak_distance': 15,
                'hough_threshold': 45,
                'morphology_density': 0.25
            }

    def _balanced_preprocess(self, gray: np.ndarray, use_left_half: bool) -> np.ndarray:
        """平衡的预处理"""
        # 裁剪左半部分
        if use_left_half:
            width = gray.shape[1]
            crop_width = int(width * 0.55)
            gray = gray[:, :crop_width]
            print(f"裁剪为左半部分: {gray.shape[1]}x{gray.shape[0]}")

        # 保守的缩放，保持足够分辨率
        if gray.shape[0] > 2500:  # 只对非常大的图像进行缩放
            scale_factor = 0.7  # 更保守的缩放因子
            new_height = int(gray.shape[0] * scale_factor)
            new_width = int(gray.shape[1] * scale_factor)
            gray = cv2.resize(gray, (new_width, new_height), interpolation=cv2.INTER_AREA)
            print(f"缩放到: {gray.shape[1]}x{gray.shape[0]}")

        # 温和的预处理
        # 1. 轻微去噪
        denoised = cv2.bilateralFilter(gray, 5, 50, 50)

        # 2. 温和的对比度增强
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(denoised)

        return enhanced

    def _moderate_conservative_detection(self, gray: np.ndarray, min_line_length: int,
                                         params: Dict) -> List[Tuple[int, int, int, int]]:
        """适度保守的检测"""
        lines = []
        height, width = gray.shape

        # 方法1：平衡的投影分析
        proj_lines = self._balanced_projection_analysis(gray, min_line_length, params)
        lines.extend(proj_lines)
        print(f"  平衡投影分析: {len(proj_lines)} 条线")

        # 方法2：适度的霍夫变换
        hough_lines = self._moderate_hough_detection(gray, min_line_length, params)
        lines.extend(hough_lines)
        print(f"  适度霍夫变换: {len(hough_lines)} 条线")

        # 方法3：平衡的形态学检测
        morph_lines = self._balanced_morphology(gray, min_line_length, params)
        lines.extend(morph_lines)
        print(f"  平衡形态学: {len(morph_lines)} 条线")

        # 初步去重
        if lines:
            merged_lines = self._intelligent_merge(lines, min_spacing=12)
            print(f"  智能合并: {len(merged_lines)} 条线")
            return merged_lines

        return lines

    def _balanced_projection_analysis(self, gray: np.ndarray, min_line_length: int,
                                      params: Dict) -> List[Tuple[int, int, int, int]]:
        """平衡的投影分析"""
        lines = []
        height, width = gray.shape

        # 创建平衡的二值图像
        # 使用多种二值化方法的组合
        _, otsu = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        adaptive = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                         cv2.THRESH_BINARY_INV, 11, 2)

        # 组合二值化结果
        combined = cv2.bitwise_or(otsu, adaptive)

        # 温和的去除水平线
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (width // 60, 1))
        horizontal_lines = cv2.morphologyEx(combined, cv2.MORPH_OPEN, horizontal_kernel)
        clean_binary = cv2.subtract(combined, horizontal_lines)

        # 轻微的噪点去除
        noise_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        clean_binary = cv2.morphologyEx(clean_binary, cv2.MORPH_OPEN, noise_kernel)

        # 计算垂直投影
        projection = np.sum(clean_binary, axis=0)

        # 平衡的峰值检测
        mean_proj = np.mean(projection)
        std_proj = np.std(projection)

        # 使用参数化的阈值
        threshold = mean_proj + params['projection_threshold_factor'] * std_proj

        # 平滑投影
        smoothed = self._smooth_projection(projection, window_size=5)

        # 峰值检测
        peaks = []
        min_distance = params['min_peak_distance']

        for i in range(2, len(smoothed) - 2):
            if (smoothed[i] > smoothed[i - 1] and smoothed[i] > smoothed[i + 1] and
                    smoothed[i] > smoothed[i - 2] and smoothed[i] > smoothed[i + 2] and
                    smoothed[i] > threshold):

                # 检查与已有峰值的距离
                if not peaks or min([abs(i - p) for p in peaks]) >= min_distance:
                    peaks.append(i)

        print(f"    找到 {len(peaks)} 个平衡投影峰值")

        # 在峰值位置提取竖线
        for x in peaks:
            extracted_lines = self._extract_balanced_line(clean_binary, x, min_line_length)
            lines.extend(extracted_lines)

        return lines

    def _extract_balanced_line(self, binary: np.ndarray, x: int, min_line_length: int) -> List[
        Tuple[int, int, int, int]]:
        """平衡的线条提取"""
        lines = []
        height, width = binary.shape

        if x < 1 or x >= width - 1:
            return lines

        # 在x位置及其邻近位置搜索
        best_line = None
        best_score = 0

        for dx in [-2, -1, 0, 1, 2]:  # 扩大搜索范围
            search_x = x + dx
            if 0 <= search_x < width:
                column = binary[:, search_x]

                # 查找连续段
                segments = self._find_balanced_segments(column, min_gap=10, min_length=min_line_length)

                for start_y, end_y in segments:
                    segment_length = end_y - start_y + 1

                    # 平衡的质量评分
                    segment_pixels = column[start_y:end_y + 1]
                    continuity = np.count_nonzero(segment_pixels) / len(segment_pixels)
                    length_ratio = segment_length / height

                    # 更宽松的评分公式
                    score = segment_length * (continuity * 0.6 + length_ratio * 0.4)

                    if score > best_score:
                        best_score = score
                        best_line = (search_x, start_y, search_x, end_y)

        # 降低评分要求
        if best_line and best_score > min_line_length * 0.4:  # 从0.6降到0.4
            lines.append(best_line)

        return lines

    def _find_balanced_segments(self, column: np.ndarray, min_gap: int, min_length: int) -> List[Tuple[int, int]]:
        """平衡的段查找"""
        segments = []
        nonzero_indices = np.where(column > 0)[0]

        if len(nonzero_indices) == 0:
            return segments

        # 更宽松的连接算法
        current_segment = [nonzero_indices[0]]

        for i in range(1, len(nonzero_indices)):
            gap = nonzero_indices[i] - nonzero_indices[i - 1]
            if gap <= min_gap:
                current_segment.append(nonzero_indices[i])
            else:
                # 结束当前段
                if len(current_segment) > 0:
                    start_y = min(current_segment)
                    end_y = max(current_segment)
                    if end_y - start_y + 1 >= min_length:
                        segments.append((start_y, end_y))
                current_segment = [nonzero_indices[i]]

        # 处理最后一段
        if len(current_segment) > 0:
            start_y = min(current_segment)
            end_y = max(current_segment)
            if end_y - start_y + 1 >= min_length:
                segments.append((start_y, end_y))

        return segments

    def _moderate_hough_detection(self, gray: np.ndarray, min_line_length: int,
                                  params: Dict) -> List[Tuple[int, int, int, int]]:
        """适度的霍夫变换检测"""
        lines = []

        # 平衡的边缘检测
        edges = cv2.Canny(gray, 40, 120, apertureSize=3)

        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 3))
        edges = cv2.dilate(edges, kernel, iterations=1)

        hough_lines = cv2.HoughLinesP(
            edges,
            rho=1,
            theta=np.pi / 180,
            threshold=40,  # 降低，保证弱线也能检测到
            minLineLength=20,  # 降低，允许短竖线
            maxLineGap=20  # 提高，能把断裂的竖线连起来
        )
        if hough_lines is not None:
            # 适度限制数量
            if len(hough_lines) > 30:
                # 按长度排序，取最长的30条
                line_lengths = []
                for line in hough_lines:
                    x1, y1, x2, y2 = line[0]
                    length = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
                    line_lengths.append((length, line))
                line_lengths.sort(key=lambda x: x[0], reverse=True)
                hough_lines = [line for _, line in line_lengths[:30]]

            for line in hough_lines:
                x1, y1, x2, y2 = line[0]

                # 适度的角度检查
                angle = self._calculate_angle(x1, y1, x2, y2)
                if abs(angle - 90) < 15:  # 放宽到15度
                    line_length = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
                    if line_length >= min_line_length * 0.8:  # 降低长度要求
                        # 统一坐标顺序
                        if y1 > y2:
                            x1, y1, x2, y2 = x2, y2, x1, y1
                        lines.append((x1, y1, x2, y2))

        return lines

    def _balanced_morphology(self, gray: np.ndarray, min_line_length: int,
                             params: Dict) -> List[Tuple[int, int, int, int]]:
        """平衡的形态学检测"""
        lines = []

        # 平衡的二值化
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # 多种垂直核
        kernel_heights = [min_line_length // 6, min_line_length // 4, min_line_length // 3]

        for kernel_height in kernel_heights:
            if kernel_height < 10:
                continue

            vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, kernel_height))

            # 形态学开运算
            morphed = cv2.morphologyEx(binary, cv2.MORPH_OPEN, vertical_kernel)

            # cv2.imwrite("debug_vertical_enhanced.png", morphed)

            # 寻找轮廓
            contours, _ = cv2.findContours(morphed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)

                # 平衡的筛选条件
                if (h >= int(min_line_length * 0.8) and  # 稍微降低高度要求
                        h > w * 2 and  # 降低宽高比要求
                        w <= 15 and  # 放宽宽度限制
                        h <= gray.shape[0] * 0.9):  # 放宽最大高度

                    # 验证轮廓质量
                    mask = np.zeros(gray.shape, dtype=np.uint8)
                    cv2.fillPoly(mask, [contour], 255)
                    contour_pixels = cv2.bitwise_and(binary, mask)
                    density = np.count_nonzero(contour_pixels) / (w * h)

                    if density > params['morphology_density']:  # 参数化密度要求
                        center_x = x + w // 2
                        lines.append((center_x, y, center_x, y + h))

        return lines

    def _balanced_verification(self, lines: List[Tuple[int, int, int, int]],
                               gray: np.ndarray, params: Dict) -> List[Tuple[int, int, int, int]]:
        """平衡的验证"""
        if not lines:
            return lines

        verified_lines = []
        height, width = gray.shape

        print(f"  开始平衡验证 {len(lines)} 条候选线")

        for i, (x, y1, _, y2) in enumerate(lines):
            # 基本检查
            if x < 0 or x >= width or y1 >= y2:
                continue

            line_length = y2 - y1

            # 验证1：存在性验证（权重降低）
            existence_score = self._calculate_existence_score(gray, x, y1, y2)

            # 验证2：环境验证（权重降低）
            environment_score = self._calculate_environment_score(gray, x, y1, y2)

            # 验证3：直线性验证（权重降低）
            straightness_score = self._calculate_straightness_score(gray, x, y1, y2)

            # 平衡的综合评分
            total_score = (existence_score * params['existence_weight'] +
                           environment_score * params['environment_weight'] +
                           straightness_score * params['straightness_weight'])

            print(f"    线条{i + 1} x={x}: 存在={existence_score:.2f}, 环境={environment_score:.2f}, "
                  f"直线性={straightness_score:.2f}, 总分={total_score:.2f}")

            # 使用参数化的验证阈值
            if total_score > params['verification_threshold']:
                verified_lines.append((x, y1, x, y2))
            else:
                print(f"      -> 未通过验证（评分: {total_score:.2f} < {params['verification_threshold']:.2f}）")

        return verified_lines

    def _calculate_existence_score(self, gray: np.ndarray, x: int, y1: int, y2: int) -> float:
        """计算存在性评分 - 更宽松"""
        if x < 0 or x >= gray.shape[1] or y1 >= y2:
            return 0.0

        column = gray[y1:y2 + 1, x]

        # 多种验证方法
        scores = []

        # OTSU二值化
        _, otsu_binary = cv2.threshold(column, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        otsu_density = np.count_nonzero(otsu_binary) / len(otsu_binary)
        scores.append(otsu_density)

        # 自适应阈值
        if len(column) >= 11:
            try:
                adaptive_binary = cv2.adaptiveThreshold(column, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                                        cv2.THRESH_BINARY_INV, 11, 2)
                adaptive_density = np.count_nonzero(adaptive_binary) / len(adaptive_binary)
                scores.append(adaptive_density)
            except:
                pass

        # 简单阈值
        mean_val = np.mean(column)
        std_val = np.std(column)
        if std_val > 5:  # 降低要求
            thresh_val = mean_val - 0.5 * std_val  # 更宽松的阈值
            if thresh_val > 0:
                _, simple_binary = cv2.threshold(column, thresh_val, 255, cv2.THRESH_BINARY_INV)
                simple_density = np.count_nonzero(simple_binary) / len(simple_binary)
                scores.append(simple_density)

        # 返回最高评分
        return float(np.mean(scores)) if scores else 0.0

    def _calculate_environment_score(self, gray: np.ndarray, x: int, y1: int, y2: int) -> float:
        """计算环境评分 - 更宽松"""
        height, width = gray.shape

        # 检查周围区域
        left_x = max(0, x - 5)
        right_x = min(width - 1, x + 5)

        if right_x - left_x < 3:
            return 0.6  # 给边界情况更高的评分

        # 提取周围区域
        region = gray[y1:y2 + 1, left_x:right_x + 1]

        # 更宽松的环境检查
        region_std = np.std(region)

        # 简单的对比度检查
        center_col_idx = x - left_x
        if center_col_idx < region.shape[1]:
            center_column = region[:, center_col_idx]
            center_mean = np.mean(center_column)

            # 与整个区域的平均值比较
            region_mean = np.mean(region)
            contrast = abs(center_mean - region_mean) / 255.0

            # 宽松的评分
            contrast_score = min(1.0, contrast * 2)  # 降低对比度要求
            variance_score = max(0.3, 1.0 - region_std / 60.0)  # 更宽松的方差要求

            return (contrast_score + variance_score) / 2

        return 0.6

    def _calculate_straightness_score(self, gray: np.ndarray, x: int, y1: int, y2: int) -> float:
        """计算直线性评分 - 更宽松"""
        if y2 - y1 < 15:  # 降低最小长度要求
            return 0.7  # 给短线条更高的基础评分

        width = gray.shape[1]
        search_range = min(3, width // 80)  # 扩大搜索范围

        best_score = 0

        for dx in range(-search_range, search_range + 1):
            search_x = x + dx
            if 0 <= search_x < width:
                column = gray[y1:y2 + 1, search_x]

                # 更宽松的二值化
                _, binary_col = cv2.threshold(column, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

                # 简单的连续性计算
                total_pixels = len(binary_col)
                nonzero_pixels = np.count_nonzero(binary_col)

                if total_pixels > 0:
                    density = nonzero_pixels / total_pixels

                    # 更宽松的连续性评估
                    # 计算连续段
                    segments = []
                    current_segment = 0
                    for pixel in binary_col:
                        if pixel > 0:
                            current_segment += 1
                        else:
                            if current_segment > 0:
                                segments.append(current_segment)
                            current_segment = 0

                    if current_segment > 0:
                        segments.append(current_segment)

                    if segments:
                        # 最长段的比例
                        max_segment = max(segments)
                        continuity_ratio = max_segment / total_pixels

                        # 段数量惩罚更宽松
                        segment_penalty = min(0.5, len(segments) / 8.0)  # 降低惩罚

                        score = density * 0.5 + continuity_ratio * 0.5 - segment_penalty * 0.2
                        best_score = max(best_score, score)

        return max(0.3, best_score)  # 设置最低评分

    def _intelligent_enhancement(self, gray: np.ndarray, known_lines: List[Tuple[int, int, int, int]],
                                 min_line_length: int, params: Dict) -> List[Tuple[int, int, int, int]]:
        """智能增强检测"""
        if len(known_lines) < 2:
            return []

        lines = []
        height, width = gray.shape

        # 分析已知线条
        x_positions = sorted([line[0] for line in known_lines])

        if len(x_positions) < 2:
            return []

        spacings = [x_positions[i + 1] - x_positions[i] for i in range(len(x_positions) - 1)]
        median_spacing = np.median(spacings)
        mean_spacing = np.mean(spacings)
        std_spacing = np.std(spacings)

        print(f"    间距分析 - 中位数: {median_spacing:.1f}, 平均: {mean_spacing:.1f}, 标准差: {std_spacing:.1f}")

        # 宽松的模式检测条件
        if std_spacing < mean_spacing * 0.5:  # 放宽间距规律要求
            candidate_positions = []

            # 在间隙中寻找
            for i in range(len(x_positions) - 1):
                left_x = x_positions[i]
                right_x = x_positions[i + 1]
                gap = right_x - left_x

                if gap > median_spacing * 1.3:  # 降低间隙阈值
                    expected_x = left_x + gap // 2
                    candidate_positions.append(expected_x)

            # 向两端扩展（保守）
            if len(x_positions) >= 3:
                # 向左扩展
                leftmost = min(x_positions)
                x = leftmost - median_spacing
                if x > median_spacing:
                    candidate_positions.append(int(x))

                # 向右扩展
                rightmost = max(x_positions)
                x = rightmost + median_spacing
                if x < width - median_spacing:
                    candidate_positions.append(int(x))

            print(f"    找到 {len(candidate_positions)} 个增强候选位置")

            # 在候选位置检测
            for expected_x in candidate_positions:
                search_range = max(8, int(std_spacing / 2)) if std_spacing > 0 else 10

                best_line = None
                best_score = 0

                for dx in range(-search_range, search_range + 1, 3):  # 步长为3
                    search_x = expected_x + dx

                    if 5 < search_x < width - 5:
                        candidate_line = self._detect_enhancement_line(gray, search_x, min_line_length, known_lines)

                        if candidate_line:
                            similarity = self._calculate_enhancement_similarity(candidate_line, known_lines, gray)

                            if similarity > best_score:
                                best_score = similarity
                                best_line = candidate_line

                # 使用更宽松的相似度阈值
                if best_line and best_score > 0.5:  # 从0.7降到0.5
                    lines.append(best_line)
                    print(f"      在 x={best_line[0]} 找到增强线条，评分: {best_score:.2f}")

        return lines

    def _detect_enhancement_line(self, gray: np.ndarray, x: int, min_line_length: int,
                                 known_lines: List[Tuple[int, int, int, int]]) -> Optional[Tuple[int, int, int, int]]:
        """检测增强线条 - 更宽松"""
        if x < 0 or x >= gray.shape[1]:
            return None

        column = gray[:, x]

        # 多种二值化方法
        binary_methods = []

        # OTSU
        _, otsu = cv2.threshold(column, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        binary_methods.append(otsu)

        # 自适应阈值
        if len(column) >= 11:
            try:
                adaptive = cv2.adaptiveThreshold(column, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                                 cv2.THRESH_BINARY_INV, 11, 3)
                binary_methods.append(adaptive)
            except:
                pass

        # 基于已知线条的统计
        if known_lines:
            known_intensities = []
            for kx, ky1, _, ky2 in known_lines[:3]:  # 只使用前3条线的统计
                if 0 <= kx < gray.shape[1] and ky1 < ky2:
                    try:
                        known_column = gray[ky1:ky2 + 1, kx]
                        known_intensities.extend(known_column.tolist())
                    except:
                        pass

            if known_intensities:
                known_mean = np.mean(known_intensities)
                known_std = np.std(known_intensities)
                thresh_value = known_mean + 0.2 * known_std  # 更宽松
                if thresh_value > 0:
                    _, pattern_binary = cv2.threshold(column, thresh_value, 255, cv2.THRESH_BINARY_INV)
                    binary_methods.append(pattern_binary)

        # 寻找最佳线条
        best_line = None
        best_score = 0

        for binary_column in binary_methods:
            segments = self._find_balanced_segments(binary_column, min_gap=12, min_length=int(min_line_length * 0.7))

            for start_y, end_y in segments:
                segment_length = end_y - start_y + 1
                continuity = np.count_nonzero(binary_column[start_y:end_y + 1]) / segment_length

                # 更宽松的评分
                score = segment_length * continuity * 0.8  # 降低要求

                if score > best_score:
                    best_score = score
                    best_line = (x, start_y, x, end_y)

        return best_line if best_score > min_line_length * 0.3 else None  # 降低阈值

    def _calculate_enhancement_similarity(self, line: Tuple[int, int, int, int],
                                          known_lines: List[Tuple[int, int, int, int]],
                                          gray: np.ndarray) -> float:
        """计算增强相似度 - 更宽松"""
        x, y1, _, y2 = line
        line_length = y2 - y1

        # 长度相似度
        known_lengths = [abs(kline[3] - kline[1]) for kline in known_lines]
        mean_known_length = np.mean(known_lengths)
        std_known_length = np.std(known_lengths)

        # 更宽松的长度检查
        if abs(line_length - mean_known_length) > 3 * std_known_length:
            return 0.0

        length_similarity = max(0, 1.0 - abs(line_length - mean_known_length) / (mean_known_length + 1))

        # 密度相似度（简化）
        if 0 <= x < gray.shape[1] and y1 < y2:
            try:
                column = gray[y1:y2 + 1, x]
                _, line_binary = cv2.threshold(column, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
                line_density = np.count_nonzero(line_binary) / len(line_binary)

                # 与已知线条密度比较（简化）
                density_similarity = min(1.0, line_density * 2)  # 简化的密度评分
            except:
                density_similarity = 0.5
        else:
            density_similarity = 0.3

        # 宽松的综合相似度
        total_similarity = length_similarity * 0.4 + density_similarity * 0.6
        return max(0, total_similarity)

    def _intelligent_merge(self, lines: List[Tuple[int, int, int, int]],
                           min_spacing: int) -> List[Tuple[int, int, int, int]]:
        """智能合并"""
        if len(lines) <= 1:
            return lines

        # 按x坐标排序
        sorted_lines = sorted(lines, key=lambda line: line[0])

        merged = []
        current_group = [sorted_lines[0]]

        for i in range(1, len(sorted_lines)):
            current_line = sorted_lines[i]
            last_line = current_group[-1]

            x_distance = abs(current_line[0] - last_line[0])

            if x_distance <= min_spacing:
                current_group.append(current_line)
            else:
                # 智能合并当前组
                merged_line = self._smart_merge_group(current_group)
                merged.append(merged_line)
                current_group = [current_line]

        # 处理最后一组
        if current_group:
            merged_line = self._smart_merge_group(current_group)
            merged.append(merged_line)

        return merged

    def _smart_merge_group(self, line_group: List[Tuple[int, int, int, int]]) -> Tuple[int, int, int, int]:
        """智能合并线条组"""
        if len(line_group) == 1:
            return line_group[0]

        # 选择最长且质量最好的线条
        best_line = None
        best_score = 0

        for line in line_group:
            x, y1, _, y2 = line
            length = abs(y2 - y1)

            # 综合评分：长度 + 位置权重
            center_weight = 1.0 - abs(len(line_group) // 2 - line_group.index(line)) / len(line_group)
            score = length * (0.8 + center_weight * 0.2)

            if score > best_score:
                best_score = score
                best_line = line

        return best_line or line_group[0]

    def _scale_back_coordinates(self, lines: List[Tuple[int, int, int, int]],
                                original_gray: np.ndarray, processed_gray: np.ndarray,
                                use_left_half: bool) -> List[Tuple[int, int, int, int]]:
        """缩放回原始坐标"""
        if not lines:
            return lines

        original_height, original_width = original_gray.shape
        processed_height, processed_width = processed_gray.shape

        # 计算缩放比例
        if use_left_half:
            crop_width = int(original_width * 0.55)
            x_scale = crop_width / processed_width if processed_width > 0 else 1.0
            y_scale = original_height / processed_height if processed_height > 0 else 1.0
        else:
            x_scale = original_width / processed_width if processed_width > 0 else 1.0
            y_scale = original_height / processed_height if processed_height > 0 else 1.0

        scaled_lines = []
        for x, y1, _, y2 in lines:
            # 缩放坐标
            original_x = int(x * x_scale)
            original_y1 = int(y1 * y_scale)
            original_y2 = int(y2 * y_scale)

            # 边界检查
            original_x = max(0, min(original_x, original_width - 1))
            original_y1 = max(0, min(original_y1, original_height - 1))
            original_y2 = max(0, min(original_y2, original_height - 1))

            if original_y2 > original_y1 and original_y2 - original_y1 > 30:  # 降低最小长度要求
                scaled_lines.append((original_x, original_y1, original_x, original_y2))

        return scaled_lines

    # 辅助方法
    def _smooth_projection(self, projection: np.ndarray, window_size: int) -> np.ndarray:
        """平滑投影"""
        if len(projection) < window_size:
            return projection
        kernel = np.ones(window_size) / window_size
        return np.convolve(projection, kernel, mode='same')

    def _calculate_angle(self, x1: int, y1: int, x2: int, y2: int) -> float:
        """计算线条角度"""
        if x2 == x1:
            return 90.0
        return np.arctan2(abs(y2 - y1), abs(x2 - x1)) * 180 / np.pi

    def visualize_final_result(self, image_path: str, lines: List[Tuple[int, int, int, int]],
                               output_path: str = None) -> np.ndarray:
        """可视化最终结果"""
        image = cv2.imread(image_path)
        if image is None:
            raise FileNotFoundError(f"无法找到图片文件: {image_path}")

        result_image = image.copy()

        # 绘制检测到的竖线
        colors = [
            (0, 255, 0),  # 绿色
            (255, 0, 0),  # 红色
            (0, 0, 255),  # 蓝色
            (255, 255, 0),  # 青色
            (255, 0, 255),  # 洋红
            (0, 255, 255),  # 黄色
            (128, 255, 0),  # 浅绿
            (255, 128, 0),  # 橙色
            (128, 128, 255),  # 淡紫色
            (255, 128, 128),  # 淡红色
            (128, 255, 128),  # 淡绿色
            (255, 255, 128),  # 淡黄色
        ]

        for i, (x1, y1, x2, y2) in enumerate(lines):
            color = colors[i % len(colors)]

            # 绘制竖线
            cv2.line(result_image, (x1, y1), (x2, y2), color, 6)

            # 添加线条编号
            mid_y = (y1 + y2) // 2
            cv2.circle(result_image, (x1, mid_y), 25, color, -1)
            cv2.putText(result_image, str(i + 1), (x1 - 18, mid_y + 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 3)

            # 显示详细信息
            length = abs(y2 - y1)
            cv2.putText(result_image, f'x:{x1}', (x1 + 30, mid_y - 20),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.9, color, 3)
            cv2.putText(result_image, f'L:{length}', (x1 + 30, mid_y + 20),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.9, color, 3)

        # 添加统计信息
        stats_text = f'平衡检测: {len(lines)} 条竖线'
        cv2.putText(result_image, stats_text, (40, 100),
                    cv2.FONT_HERSHEY_SIMPLEX, 3.0, (0, 255, 0), 6)

        # 保存结果
        if output_path:
            cv2.imwrite(output_path, result_image)
            print(f"结果图像已保存到: {output_path}")

        return result_image

    def get_line_summary(self, lines: List[Tuple[int, int, int, int]]) -> dict:
        """获取线条检测摘要"""
        if not lines:
            return {
                'count': 0,
                'avg_length': 0,
                'min_length': 0,
                'max_length': 0,
                'x_positions': [],
                'line_spacing': [],
                'avg_spacing': 0,
                'spacing_std': 0
            }

        lengths = [abs(line[3] - line[1]) for line in lines]
        x_positions = sorted([line[0] for line in lines])

        # 计算线条间距
        line_spacing = []
        if len(x_positions) > 1:
            for i in range(1, len(x_positions)):
                spacing = x_positions[i] - x_positions[i - 1]
                line_spacing.append(spacing)

        return {
            'count': len(lines),
            'avg_length': np.mean(lengths) if lengths else 0,
            'min_length': min(lengths) if lengths else 0,
            'max_length': max(lengths) if lengths else 0,
            'x_positions': x_positions,
            'line_spacing': line_spacing,
            'avg_spacing': np.mean(line_spacing) if line_spacing else 0,
            'spacing_std': np.std(line_spacing) if line_spacing else 0
        }


# 使用示例
def main():
    # 创建平衡的提取器实例
    extractor = BalancedTableLineExtractor()

    # 设置图片路径
    image_path = r"E:\CodeData\01-SHProject\drawing-classify\train\drill_bar\siqingfu_page_26.png"

    try:
        print("开始平衡竖线检测...")
        print("平衡精度与召回率，既减少假阳性又保证检测真实竖线")

        # 提取竖线 - 可以尝试不同的敏感度
        for sensitivity in ['medium', 'high']:
            print(f"\n{'=' * 50}")
            print(f"尝试敏感度: {sensitivity}")
            print(f"{'=' * 50}")

            vertical_lines = extractor.extract_vertical_lines(
                image_path,
                height_threshold=0.65,  # 平衡的高度阈值
                use_left_half=True,  # 只使用左半部分
                sensitivity=sensitivity  # 敏感度设置
            )

            if len(vertical_lines) > 0:
                # 输出结果摘要
                summary = extractor.get_line_summary(vertical_lines)
                print(f"\n=== 平衡检测摘要 ({sensitivity}敏感度) ===")
                print(f"- 检测到竖线数量: {summary['count']}")
                print(f"- 平均长度: {summary['avg_length']:.1f}px")
                print(f"- 长度范围: {summary['min_length']}-{summary['max_length']}px")
                print(f"- 平均间距: {summary['avg_spacing']:.1f}px")
                print(f"- 间距标准差: {summary['spacing_std']:.1f}px")

                # 详细输出
                print(f"\n=== 详细信息 ===")
                for i, (x1, y1, x2, y2) in enumerate(vertical_lines):
                    length = abs(y2 - y1)
                    print(f"竖线 {i + 1:2d}: x={x1:4d}, y范围={y1:4d}-{y2:4d}, 长度={length:4d}px")

                # 间距分析
                if len(summary['line_spacing']) > 0:
                    print(f"\n=== 间距分析 ===")
                    print(f"所有间距: {summary['line_spacing']}")
                    spacing_counter = Counter(summary['line_spacing'])
                    most_common = dict(spacing_counter.most_common(3))
                    print(f"最常见间距: {most_common}")

                # 可视化结果
                output_filename = f"balanced_result_{sensitivity}.jpg"
                result_image = extractor.visualize_final_result(
                    image_path,
                    vertical_lines,
                    output_filename
                )

                print(f"\n平衡检测完成！结果已保存到 '{output_filename}'")
                break  # 找到结果就退出
            else:
                print(f"敏感度 {sensitivity} 未检测到竖线，尝试更高敏感度...")

        if len(vertical_lines) == 0:
            print("\n所有敏感度都未检测到竖线")
            print("建议：")
            print("1. 检查图片质量和表格竖线的清晰度")
            print("2. 尝试调整 height_threshold 参数（降低到0.5-0.6）")
            print("3. 检查图片是否需要预处理（旋转、去噪等）")

        print("\n检测完成！")

    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()