import shutil
from typing import List, Optional

import cv2
import numpy as np
from paddleocr import TableCellsDetection

from settings import MODEL_CONFIG
from utils.pdf import PdfProcessor
from vision.classification import ClassifierModelManager, ClassifierModelType
from vision.core import <PERSON><PERSON>and<PERSON>
from vision.core.data_types import <PERSON>cr<PERSON><PERSON>, BoundingBox, DetectionResult
from vision.core.utils import Visualizer, save_bbox_crops
from vision.cv_det.opencv_detector import OpenCVDetector
from vision.obj_det import Y<PERSON>OModelManager, YoloModelType
from vision.ocr_engine.engine import Ocr<PERSON>ngine, OcrModelType
from vision.ocr_engine.tesseract_model import TesseractOcrModel
from vision.table_det.table_image_cutter import Table<PERSON>mage<PERSON>utter
from vision.utils.grid_image_builder import GridImageBuilder


def test_detect_image(image_path, model_type, show=False, save=False):
    detector = YOLOModelManager().load_model(model_type)
    detections = detector.predict_single(image_path)

    image, _ = ImageHandler.normalize_input(image_path)
    detect_result = Visualizer().draw_detections(image, detections)

    if detections:
        if show:
            cv2.imshow("detect_result", detect_result)
            cv2.waitKey(0)

        if save:
            cv2.imwrite("output_detected_image.png", detect_result)
        return detections

    return None


def test_detect_large_image(image_path, model_type, show=False, save=False):
    detector = YOLOModelManager().load_model(model_type)
    detections = detector.predict_large_image(image_path, (640, 640), overlap_ratio=0.4, merge_nms_threshold=0.1)

    image, _ = ImageHandler.normalize_input(image_path)
    detect_result = Visualizer().draw_detections(image, detections)

    if detections:
        if show:
            cv2.imshow("detect_result", detect_result)
            cv2.waitKey(0)

        if save:
            cv2.imwrite("output_detected_large_image.png", detect_result)
        return detections

    return None


def save_detect_large_image(image_path, model_type, output_folder="output"):

    detections = test_detect_large_image(image_path, model_type, show=False, save=True)
    if detections:
        save_bbox_crops(image_path, detections, folder_path="output", prefix="crop")


def vision_detection_items(image_path, detections: List[DetectionResult]):
    inp_image, _ = ImageHandler.normalize_input(image_path)

    grid_builder = GridImageBuilder()
    images = grid_builder.extract_regions(detections, inp_image)
    img, _, _ = grid_builder.build_grid_image(images)

    cv2.namedWindow("image", cv2.WINDOW_NORMAL)
    cv2.imshow("image", img)
    cv2.waitKey(0)


def ocr_test():
    # 方式1：使用预设配置
    # 罗马数字 + 短横线 + 分号
    roman_ocr = TesseractOcrModel.create_roman_with_symbols(min_confidence=70)

    # 数字 + 点 + 括号
    number_ocr = TesseractOcrModel.create_numbers_with_dots_brackets(min_confidence=75)

    # 字母数字 + 短横线
    alpha_ocr = TesseractOcrModel.create_alphanumeric_with_dash(min_confidence=65)

    # 执行识别
    print(roman_ocr.predict(r"F:\MyDownload\luoma.png"))
    print(number_ocr.predict(r"F:\MyDownload\shuzikuohao.png"))
    print(alpha_ocr.predict(r"F:\MyDownload\zimushuzi.png"))

    # 查看可用预设
    presets = TesseractOcrModel().get_available_presets()
    for name, config in presets.items():
        print(f"{name}: {config['description']}")

    # 查看当前配置
    print(roman_ocr.get_current_config())


def test_ocr():
    img_path = '/Users/<USER>/Desktop/ruler.png'
    result = OcrEngine.get_instance().recognize(img_path)
    print(result)


def test_pdf_metadata():
    pdf_path = '/Users/<USER>/CodeRepo/08-CADWithAi/Resources/地勘图纸/202单体扩建项目地勘.pdf'
    # 处理特定页面
    with PdfProcessor(pdf_path=pdf_path) as processor:
        # 只处理第1、3、5页
        items = processor.extract_text_with_dpi_conversion(page_numbers=[26], dpi=300)

        print(items)


def test_table_cutter():
    image_path = "/Users/<USER>/CodeRepo/08-CADWithAi/paddlex-api/output/table/0416-shang_page_237_0.png"
    image = cv2.imread(image_path)

    # 创建检测器实例
    detector = OpenCVDetector(image=image)

    # 获取图像信息
    info = detector.get_image_info()
    print("图像信息:", info)

    horizontal_lines = detector.detect_horizontal_lines(0)
    vertical_lines = detector.detect_vertical_lines(0)

    # 初始化切割器
    cutter = TableImageCutter(image, threshold_ratio=0.9, min_segment_ratio=0.02)

    # 设置线条
    cutter.set_lines(horizontal_lines, vertical_lines)

    # 生成切割预览
    cutter.visualize_cut_lines(target_width=1024, target_height=1024,
                               output_path="cut_preview.png")

    # 执行切割
    output_files = cutter.cut_image(target_width=1024, target_height=1024,
                                    output_dir="cut_segments")

    print(f"\n切割完成！共生成 {len(output_files)} 个图片片段:")
    for file_path in output_files:
        print(f"  - {file_path}")


def cell_detection():
    image_path = "/Users/<USER>/CodeRepo/08-CADWithAi/data/images/drawing_classify/train/表格为主/0416-shang_page_154.png"
    img = cv2.imread(image_path)
    # img = cv2.hconcat([img, img, img])
    # 转换到 YCrCb 色彩空间
    ycrcb = cv2.cvtColor(img, cv2.COLOR_BGR2YCrCb)

    # 拆分通道
    y, cr, cb = cv2.split(ycrcb)

    # 只对亮度通道 Y 做直方图均衡化
    y_eq = cv2.equalizeHist(y)

    # 合并回去
    ycrcb_eq = cv2.merge((y_eq, cr, cb))

    # 转回 BGR
    equalized = cv2.cvtColor(ycrcb_eq, cv2.COLOR_YCrCb2BGR)

    model = TableCellsDetection(model_name="RT-DETR-L_wired_table_cell_det")
    output = model.predict(equalized, threshold=0.3, batch_size=1)

    for idx, part_cells in enumerate(output):
        bboxes = [BoundingBox.from_array(box['coordinate']) for box in part_cells['boxes']]
        x_max, y_max = max(b.x_max for b in bboxes), max(b.y_max for b in bboxes)
        img = np.full((y_max, x_max, 3), 255, dtype=np.uint8)
        img = Visualizer().draw_bboxes_with_texts(img, bboxes)
        cv2.imwrite(f"table_{idx}.png", img)


def test_paddle_ocr():
    from paddleocr import PaddleOCR
    from paddleocr import TextRecognition

    # 通过 text_detection_model_dir 指定本地模型路径
    pipeline = TextRecognition(
        model_name="PP-OCRv5_server_rec",
        model_dir="/Users/<USER>/Cache/SinopecCache/lanxindownload"
    )

    # ocr_model = OcrEngine.get_instance(OcrModelType.PADDLE, **MODEL_CONFIG['paddle']['depth_elevation'])

    # 读取图片
    img = cv2.imread("/Users/<USER>/Desktop/1.png")

    # # 设置外边距大小
    # top, bottom, left, right = 80, 80, 80, 80
    #
    # # 设置边框颜色 (BGR)
    # color = [255, 255, 255]  # 白色
    #
    # # 添加外边距
    # padded_img = cv2.copyMakeBorder(img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)

    # res = ocr_model.recognize(padded_img)
    res = pipeline.predict([img, img], batch_size=8)
    print(res)


def test_batch_paddle_ocr():
    from paddleocr import TextRecognition
    from pathlib import Path

    root_dir = Path("/Users/<USER>/CodeRepo/08-CADWithAi/AlgoApi/web_data/outputs/batch_ocr")
    files = [f for f in root_dir.glob("*") if f.is_file()]
    files = sorted(files, key=lambda item: int(item.stem.split("_")[1]))
    files = [f.resolve().as_posix() for f in files]

    ocr_model = TextRecognition()
    result = ocr_model.predict(files, batch_size=49)

    texts: List[str] = []
    for rec_res in result:
        text = rec_res['rec_text']
        texts.append(text)
    print(texts)


def test_classifier():
    from pathlib import Path
    root_dir = Path("/Users/<USER>/CodeRepo/08-CADWithAi/AlgoApi/web_data/outputs/batch_ocr")
    files = [f.resolve().as_posix() for f in root_dir.glob("*") if f.is_file()]

    classifier = ClassifierModelManager().load_model(ClassifierModelType.TABLE_CELL_CONTENT)
    classification_result = classifier.predict_batch(files, 16)
    print(classification_result)

if __name__ == '__main__':
    # img_path = '/Users/<USER>/CodeRepo/08-CADWithAi/data/images/drawing_classify/train/剖面图/0416-shang_page_38.png'
    # img_path = r"F:\MyDownload\0416-shang_page_35.png"
    # img_path = r"/Users/<USER>/Desktop/page_26.png"
    # model = YoloModelType.SECTION_PAIR_DETECTION
    #
    # detections = test_detect_image(img_path, model)
    # detections = test_detect_large_image(img_path, model, show=True, save=True)
    # save_detect_large_image(img_path, model)

    # vision_detection_items(img_path, detections)

    # ocr_test()
    test_ocr()
    # test_grid_ocr()
    # test_pdf_metadata()
    # test_table_cutter()
    # cell_detection()
    # test_paddle_ocr()
    # test_text_detection()
    # test_batch_paddle_ocr()
