import os
import sys
import pefile
import ctypes

def find_dll_in_path(dll_name):
    """检查 DLL 是否在系统 PATH 中"""
    paths = os.environ["PATH"].split(os.pathsep)
    for path in paths:
        dll_path = os.path.join(path, dll_name)
        if os.path.exists(dll_path):
            return dll_path
    return None

def check_pyd_dependencies(pyd_path):
    if not os.path.exists(pyd_path):
        print(f"✗ 文件不存在: {pyd_path}")
        return

    try:
        pe = pefile.PE(pyd_path)
    except Exception as e:
        print(f"✗ 无法解析 {pyd_path}: {e}")
        return

    print(f"检查依赖: {pyd_path}")
    if not hasattr(pe, 'DIRECTORY_ENTRY_IMPORT'):
        print("⚠ 未检测到依赖 DLL")
        return

    missing = []
    for entry in pe.DIRECTORY_ENTRY_IMPORT:
        dll_name = entry.dll.decode('utf-8')
        dll_path = find_dll_in_path(dll_name)
        if dll_path:
            try:
                # 尝试加载 DLL 检查位数兼容性
                ctypes.CDLL(dll_path)
                print(f"✓ {dll_name} 找到: {dll_path}")
            except OSError as e:
                print(f"✗ {dll_name} 位数或加载错误: {e}")
                missing.append(dll_name)
        else:
            print(f"✗ {dll_name} 未找到")
            missing.append(dll_name)

    if missing:
        print("\n缺失或无法加载的 DLL:")
        for dll in missing:
            print(f"- {dll}")
    else:
        print("\n所有依赖 DLL 都存在且可加载！")

if __name__ == "__main__":
    # 替换为你的 cannyline.pyd 路径
    pyd_path = r"E:\CODE_MGR\01-SHProject\cannyline-pybind\build\Release\cannyline.pyd"
    check_pyd_dependencies(pyd_path)


import pefile
import os

pyd_path = r"E:/CODE_MGR/01-SHProject/cannyline-pybind/build/Release/cannyline.pyd"

if not os.path.exists(pyd_path):
    print("✗ .pyd文件不存在")
else:
    pe = pefile.PE(pyd_path)
    print("依赖的DLL:")
    if hasattr(pe, 'DIRECTORY_ENTRY_IMPORT'):
        for entry in pe.DIRECTORY_ENTRY_IMPORT:
            print(entry.dll.decode('utf-8'))
    else:
        print("没有找到依赖 DLL")






# import os
# import sys
# import subprocess
#
#
#
# def check_dependencies():
#     pyd_path = r"E:/CODE_MGR/01-SHProject/cannyline-pybind/build/Release/cannyline.pyd"
#
#     if not os.path.exists(pyd_path):
#         print(f"✗ .pyd文件不存在: {pyd_path}")
#         return False
#
#     print(f"✓ .pyd文件存在: {pyd_path}")
#
#     # 使用dumpbin检查依赖（如果有Visual Studio）
#     try:
#         result = subprocess.run(['dumpbin', '/dependents', pyd_path],
#                                 capture_output=True, text=True)
#         if result.returncode == 0:
#             print("依赖的DLL:")
#             print(result.stdout)
#         else:
#             print("无法使用dumpbin检查依赖")
#     except FileNotFoundError:
#         print("dumpbin不可用，请安装Visual Studio")
#
#     return True
#
#
# if __name__ == "__main__":
#     check_dependencies()