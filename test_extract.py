import cv2
import os
from datetime import datetime
from pathlib import Path
from table_extract_line import BalancedTableLineExtractor


class SimpleBatchProcessor:
    def __init__(self):
        """简单的批量图片处理器 - 只生成结果图片"""
        self.extractor = BalancedTableLineExtractor()

    def process_folder(self,
                       input_folder: str,
                       output_folder: str = None,
                       height_threshold: float = 0.65,
                       sensitivity: str = 'medium') -> dict:
        """
        处理文件夹中的所有图片，只保存结果图片

        Args:
            input_folder (str): 输入图片文件夹路径
            output_folder (str): 输出文件夹路径，如果为None则自动创建
            height_threshold (float): 高度阈值
            sensitivity (str): 敏感度 'low', 'medium', 'high'

        Returns:
            dict: 简单的处理结果统计
        """
        # 检查输入文件夹
        if not os.path.exists(input_folder):
            print(f"错误: 输入文件夹不存在: {input_folder}")
            return {"success": False, "message": "输入文件夹不存在"}

        # 创建输出文件夹
        if output_folder is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_folder = f"line_detection_results_{timestamp}"

        output_path = Path(output_folder)
        output_path.mkdir(parents=True, exist_ok=True)

        # 获取所有图片文件
        image_files = self._get_image_files(input_folder)

        if not image_files:
            print("未找到任何图片文件")
            return {"success": False, "message": "未找到图片文件"}

        print(f"开始处理 {len(image_files)} 张图片...")
        print(f"输出文件夹: {output_path}")
        print(f"参数: threshold={height_threshold}, sensitivity={sensitivity}")
        print("-" * 50)

        success_count = 0
        total_lines = 0

        # 处理每张图片
        for i, image_path in enumerate(image_files, 1):
            filename = os.path.basename(image_path)
            print(f"[{i}/{len(image_files)}] 处理: {filename}", end=" ")

            try:
                # 检测竖线
                lines = self.extractor.extract_vertical_lines(
                    image_path,
                    height_threshold=height_threshold,
                    use_left_half=True,
                    sensitivity=sensitivity
                )

                # 生成结果图片文件名
                name_without_ext = Path(filename).stem
                result_filename = f"{name_without_ext}_result.jpg"
                result_path = output_path / result_filename

                # 保存结果图片
                if len(lines) > 0:
                    self.extractor.visualize_final_result(
                        image_path,
                        lines,
                        str(result_path)
                    )
                    print(f"✅ 检测到 {len(lines)} 条线")
                    success_count += 1
                    total_lines += len(lines)
                else:
                    # 即使没检测到线条也保存原图（可选）
                    original_image = cv2.imread(image_path)
                    if original_image is not None:
                        # 在图上标注"未检测到竖线"
                        cv2.putText(original_image, 'No vertical lines detected',
                                    (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 255), 4)
                        cv2.imwrite(str(result_path), original_image)
                    print("⚠️ 未检测到竖线")
                    success_count += 1

            except Exception as e:
                print(f"❌ 处理失败: {str(e)}")

        # 输出统计结果
        print("-" * 50)
        print(f"处理完成!")
        print(f"成功处理: {success_count}/{len(image_files)} 张图片")
        if total_lines > 0:
            print(f"总共检测到: {total_lines} 条竖线")
            print(f"平均每张: {total_lines / success_count:.1f} 条线")
        print(f"结果保存在: {output_path}")

        return {
            "success": True,
            "total_images": len(image_files),
            "successful_images": success_count,
            "total_lines": total_lines,
            "output_folder": str(output_path)
        }

    def _get_image_files(self, folder: str) -> list:
        """获取文件夹中的所有图片文件"""
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp'}
        image_files = []

        for root, dirs, files in os.walk(folder):
            for file in files:
                if Path(file).suffix.lower() in image_extensions:
                    image_files.append(os.path.join(root, file))

        return sorted(image_files)

    def process_single_image(self, image_path: str, output_path: str = None) -> bool:
        """
        处理单张图片

        Args:
            image_path (str): 输入图片路径
            output_path (str): 输出图片路径，如果为None则自动生成

        Returns:
            bool: 是否成功处理
        """
        if not os.path.exists(image_path):
            print(f"错误: 图片文件不存在: {image_path}")
            return False

        try:
            # 检测竖线
            lines = self.extractor.extract_vertical_lines(
                image_path,
                height_threshold=0.65,
                use_left_half=True,
                sensitivity='medium'
            )

            # 生成输出路径
            if output_path is None:
                input_path = Path(image_path)
                output_path = input_path.parent / f"{input_path.stem}_result.jpg"

            # 保存结果图片
            if len(lines) > 0:
                self.extractor.visualize_final_result(image_path, lines, str(output_path))
                print(f"✅ 检测到 {len(lines)} 条竖线，结果已保存: {output_path}")
            else:
                # 保存标注了"未检测到"的原图
                original_image = cv2.imread(image_path)
                if original_image is not None:
                    cv2.putText(original_image, 'No vertical lines detected',
                                (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 255), 4)
                    cv2.imwrite(str(output_path), original_image)
                print(f"⚠️ 未检测到竖线，结果已保存: {output_path}")

            return True

        except Exception as e:
            print(f"❌ 处理失败: {str(e)}")
            return False


# 使用示例
def main():
    """主函数 - 使用示例"""
    processor = SimpleBatchProcessor()

    # 方式1: 处理整个文件夹
    input_folder = r"E:\CodeData\01-SHProject\drawing-classify\train\drill_bar"  # 替换为您的图片文件夹路径

    if os.path.exists(input_folder):
        result = processor.process_folder(
            input_folder=input_folder,
            output_folder="results",  # 输出文件夹名，可以不指定
            height_threshold=0.65,
            sensitivity='medium'
        )

        if result["success"]:
            print(f"\n🎉 批量处理成功完成!")
            print(f"📁 结果文件夹: {result['output_folder']}")
        else:
            print(f"❌ 批量处理失败: {result.get('message')}")
    else:
        print(f"❌ 文件夹不存在: {input_folder}")
        print("请创建文件夹并放入图片，或者修改 input_folder 路径")

        # 方式2: 处理单张图片（示例）
        single_image = "example.jpg"
        if os.path.exists(single_image):
            print(f"\n尝试处理单张图片: {single_image}")
            processor.process_single_image(single_image)


if __name__ == "__main__":
    main()