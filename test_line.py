import os
import sys
import cv2
import numpy as np
import ctypes

# OpenCV DLL 路径（保持和编译时一致）
opencv_path = r"D:\opencv\build\x64\vc16\bin"

# 确认 DLL 是否存在
dll_path = os.path.join(opencv_path, "opencv_world4120.dll")
print("DLL exists:", os.path.exists(dll_path))  # 应该 True

# 添加 DLL 搜索路径
os.add_dll_directory(opencv_path)
os.environ["PATH"] = opencv_path + ";" + os.environ["PATH"]

# 加载 OpenCV DLL
ctypes.WinDLL(dll_path)

# 添加 .pyd 搜索路径
sys.path.append(r"E:/CODE_MGR/01-SHProject/cannyline-pybind/build/Release")

# 导入 cannyline
import cannyline

print("cannyline 导入成功！")

# 3. 加载图像
img = cv2.imread(r"E:\CodeData\01-SHProject\drawing-classify\train\drill_bar\siqingfu_page_26.png", cv2.IMREAD_GRAYSCALE)
lines = cannyline.detect_lines(img, threshold=50)
print("Detected lines:", lines)

# 4. 调用线段检测
lines = cannyline.detect_lines(img, threshold=50)

# 5. 可视化
img_color = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
for line in lines:
    x1, y1, x2, y2 = line
    cv2.line(img_color, (x1, y1), (x2, y2), (0, 0, 255), 2)

cv2.imshow("Detected Lines", img_color)
cv2.waitKey(0)
cv2.destroyAllWindows()

# 6. 打印检测到的线段
print("Detected lines:", lines)