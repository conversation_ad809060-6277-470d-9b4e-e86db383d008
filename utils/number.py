import re


def safe_parse_float(text: str) -> float:
    """
    从字符串中提取第一个合法的浮点数，支持千位分隔符、小数、科学计数法等。
    自动修正 OCR 可能识别错误的负号。
    """
    if not isinstance(text, str):
        raise TypeError("输入必须是字符串")

    # 替换 OCR 常见错误字符
    text = text.replace('−', '-')     # 数学负号 → 标准负号
    text = text.replace('–', '-')     # En dash
    text = text.replace('—', '-')     # Em dash
    text = text.replace('‑', '-')     # Non-breaking hyphen

    # 匹配合法数字模式
    number_pattern = re.compile(
        r"""
        (?<!\w)                       # 前面不是字母或数字
        [-+]?                        # 可选正负号
        (?:
            (?:\d{1,3}(?:,\d{3})+)|  # 千位分隔（如 1,234）
            \d+                      # 或非千位纯数字
        )
        (?:\.\d+)?                   # 可选小数部分
        (?:[eE][-+]?\d+)?            # 可选科学记数法
        (?!\w)                       # 后面不是字母或数字
        """,
        re.VERBOSE
    )

    match = number_pattern.search(text)
    if not match:
        raise ValueError(f"未找到合法的浮点数: '{text}'")

    num_str = match.group()

    # 去除千位分隔符
    num_str = num_str.replace(",", "")

    return float(num_str)


def compare_arrays(A, B):
    i, j = 0, 0
    diffs = []

    while i < len(A) and j < len(B):
        if A[i] == B[j]:
            i += 1
            j += 1
        else:
            # 如果下一个能对上，说明缺失
            if i + 1 < len(A) and A[i + 1] == B[j]:
                diffs.append(("missing_in_B", A[i]))
                i += 1
            elif j + 1 < len(B) and B[j + 1] == A[i]:
                diffs.append(("missing_in_A", B[j]))
                j += 1
            else:
                diffs.append(("mismatch", A[i], B[j]))
                i += 1
                j += 1

    # 处理剩余元素
    while i < len(A):
        diffs.append(("missing_in_B", A[i]))
        i += 1
    while j < len(B):
        diffs.append(("missing_in_A", B[j]))
        j += 1

    return diffs


if __name__ == '__main__':

    test_cases = [
        "1,234.56",        # 美式格式
        "1.234,56",        # 欧洲格式
        "￥1,234.56",       # 货币符号
        "abc123.45def",    # 非法，拒绝
        "1.23e3",          # 科学计数法
        "hello",           # 非法
        "  -12,345.67  ",  # 负数 + 空格
        "1.234.567,89",    # 多千位 + 欧洲
        "1,234,567.89",    # 多千位 + 美式
        "-1,000,000",
        "−1,000,000"
    ]

    for s in test_cases:
        try:
            result = safe_parse_float(s)
            print(f"{s!r:20} -> {result}")
        except Exception as e:
            print(f"{s!r:20} -> ❌ {e}")