import fitz  # PyMuPDF
from typing import List, Optional, Union, Dict, Any
from pydantic import BaseModel
from vision.core.data_types import OcrItem


class PdfTextItem(OcrItem):
    page_number: Optional[int] = None
    image_width: Optional[int] = None
    image_height: Optional[int] = None


class PageInfo(BaseModel):
    page_number: int
    width: int
    height: int
    text_blocks_count: int
    has_images: bool


class PdfProcessor:
    """PDF处理器类，用于提取PDF文本和结构信息"""

    def __init__(self, pdf_content: bytes = None, pdf_path: str = None):
        """
        初始化PDF处理器

        Args:
            pdf_content: PDF文件的字节内容
            pdf_path: PDF文件路径
        """
        self.document = None
        self.total_pages = 0

        if pdf_content:
            self.document = fitz.open(stream=pdf_content, filetype="pdf")
        elif pdf_path:
            self.document = fitz.open(pdf_path)
        else:
            raise ValueError("必须提供pdf_content或pdf_path参数")

        self.total_pages = len(self.document)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.document:
            self.document.close()

    def close(self):
        """关闭PDF文档"""
        if self.document:
            self.document.close()
            self.document = None

    def get_page_info(self, page_numbers: Optional[List[int]] = None) -> List[PageInfo]:
        """
        获取页面基本信息

        Args:
            page_numbers: 指定要获取信息的页码列表，None表示所有页面

        Returns:
            页面信息列表
        """
        if not self.document:
            raise ValueError("PDF文档未打开")

        if page_numbers is None:
            page_numbers = list(range(1, self.total_pages + 1))

        page_infos = []

        for page_num in page_numbers:
            if not (1 <= page_num <= self.total_pages):
                continue

            page = self.document[page_num - 1]  # fitz使用0索引
            page_rect = page.rect

            # 获取文本块信息
            text_dict = page.get_text("dict")
            text_blocks_count = 0
            has_images = False

            for block in text_dict["blocks"]:
                if "lines" in block:
                    text_blocks_count += 1
                elif "image" in block:
                    has_images = True

            page_info = PageInfo(
                page_number=page_num,
                width=int(page_rect.width),
                height=int(page_rect.height),
                text_blocks_count=text_blocks_count,
                has_images=has_images
            )
            page_infos.append(page_info)

        return page_infos

    def extract_text_items(
            self,
            page_numbers: Optional[List[int]] = None,
            granularity: str = "span"  # "span", "line", "block"
    ) -> List[PdfTextItem]:
        """
        提取PDF文本项

        Args:
            page_numbers: 指定要处理的页码列表，None表示所有页面
            granularity: 文本提取粒度 - "span"(最细), "line"(行), "block"(块)

        Returns:
            OcrItem列表
        """
        if not self.document:
            raise ValueError("PDF文档未打开")

        if page_numbers is None:
            page_numbers = list(range(1, self.total_pages + 1))

        ocr_items = []

        for page_num in page_numbers:
            if not (1 <= page_num <= self.total_pages):
                continue

            page = self.document[page_num - 1]
            page_rect = page.rect
            page_width = int(page_rect.width)
            page_height = int(page_rect.height)

            # 获取页面文本块信息
            text_blocks = page.get_text("dict")

            for block in text_blocks["blocks"]:
                if "lines" not in block:  # 跳过图像块
                    continue

                if granularity == "block":
                    # 块级别提取
                    block_text = ""
                    block_bbox = block["bbox"]

                    for line in block["lines"]:
                        for span in line["spans"]:
                            block_text += span["text"]

                    if block_text.strip():
                        ocr_item = PdfTextItem(
                            text=block_text.strip(),
                            confidence=1.0,
                            x1=int(block_bbox[0]),
                            y1=int(block_bbox[1]),
                            x2=int(block_bbox[2]),
                            y2=int(block_bbox[3]),
                            page_number=page_num,
                            image_width=page_width,
                            image_height=page_height
                        )
                        ocr_items.append(ocr_item)

                elif granularity == "line":
                    # 行级别提取
                    for line in block["lines"]:
                        line_text = ""
                        line_bbox = line["bbox"]

                        for span in line["spans"]:
                            line_text += span["text"]

                        if line_text.strip():
                            ocr_item = PdfTextItem(
                                text=line_text.strip(),
                                confidence=1.0,
                                x1=int(line_bbox[0]),
                                y1=int(line_bbox[1]),
                                x2=int(line_bbox[2]),
                                y2=int(line_bbox[3]),
                                page_number=page_num,
                                image_width=page_width,
                                image_height=page_height
                            )
                            ocr_items.append(ocr_item)

                else:  # span级别提取（默认）
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text = span["text"].strip()
                            if text:
                                bbox = span["bbox"]

                                ocr_item = PdfTextItem(
                                    text=text,
                                    confidence=1.0,
                                    x1=int(bbox[0]),
                                    y1=int(bbox[1]),
                                    x2=int(bbox[2]),
                                    y2=int(bbox[3]),
                                    page_number=page_num,
                                    image_width=page_width,
                                    image_height=page_height
                                )
                                ocr_items.append(ocr_item)

        return ocr_items

    def extract_text_with_dpi_conversion(
            self,
            page_numbers: Optional[List[int]] = None,
            dpi: int = 150
    ) -> List[PdfTextItem]:
        """
        将PDF转换为指定DPI后提取文本，获得像素级精确坐标

        Args:
            page_numbers: 指定要处理的页码列表
            dpi: 转换DPI，默认150

        Returns:
            OcrItem列表
        """
        if not self.document:
            raise ValueError("PDF文档未打开")

        if page_numbers is None:
            page_numbers = list(range(1, self.total_pages + 1))

        ocr_items = []

        for page_num in page_numbers:
            if not (1 <= page_num <= self.total_pages):
                continue

            page = self.document[page_num - 1]

            # 转换为指定DPI的图片
            mat = fitz.Matrix(dpi / 72, dpi / 72)
            pix = page.get_pixmap(matrix=mat)

            img_width = pix.width
            img_height = pix.height

            # 计算坐标缩放比例
            scale_x = img_width / page.rect.width
            scale_y = img_height / page.rect.height

            # 获取文本块信息
            text_blocks = page.get_text("dict")

            for block in text_blocks["blocks"]:
                if "lines" not in block:
                    continue

                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"].strip()
                        if text:
                            bbox = span["bbox"]

                            ocr_item = PdfTextItem(
                                text=text,
                                confidence=1.0,
                                x1=int(bbox[0] * scale_x),
                                y1=int(bbox[1] * scale_y),
                                x2=int(bbox[2] * scale_x),
                                y2=int(bbox[3] * scale_y),
                                page_number=page_num,
                                image_width=img_width,
                                image_height=img_height
                            )
                            ocr_items.append(ocr_item)

        return ocr_items

    def extract_page_text(self, page_numbers: Optional[List[int]] = None) -> Dict[int, str]:
        """
        按页提取纯文本

        Args:
            page_numbers: 指定要处理的页码列表

        Returns:
            页码到文本的字典
        """
        if not self.document:
            raise ValueError("PDF文档未打开")

        if page_numbers is None:
            page_numbers = list(range(1, self.total_pages + 1))

        page_texts = {}

        for page_num in page_numbers:
            if not (1 <= page_num <= self.total_pages):
                continue

            page = self.document[page_num - 1]
            page_text = page.get_text()
            page_texts[page_num] = page_text

        return page_texts

    def get_metadata(self) -> Dict[str, Any]:
        """获取PDF元数据"""
        if not self.document:
            raise ValueError("PDF文档未打开")

        return self.document.metadata

    def search_text(self, query: str, page_numbers: Optional[List[int]] = None) -> List[PdfTextItem]:
        """
        在PDF中搜索文本

        Args:
            query: 搜索关键词
            page_numbers: 指定搜索的页码列表

        Returns:
            包含搜索关键词的OcrItem列表
        """
        if not self.document:
            raise ValueError("PDF文档未打开")

        if page_numbers is None:
            page_numbers = list(range(1, self.total_pages + 1))

        search_results = []

        for page_num in page_numbers:
            if not (1 <= page_num <= self.total_pages):
                continue

            page = self.document[page_num - 1]
            page_rect = page.rect
            page_width = int(page_rect.width)
            page_height = int(page_rect.height)

            # 搜索文本
            text_instances = page.search_for(query)

            for rect in text_instances:
                ocr_item = PdfTextItem(
                    text=query,
                    confidence=1.0,
                    x1=int(rect.x0),
                    y1=int(rect.y0),
                    x2=int(rect.x1),
                    y2=int(rect.y1),
                    page_number=page_num,
                    image_width=page_width,
                    image_height=page_height
                )
                search_results.append(ocr_item)

        return search_results


# 便捷函数
def process_pdf_from_bytes(pdf_content: bytes, page_numbers: Optional[List[int]] = None) -> List[PdfTextItem]:
    """从字节内容处理PDF的便捷函数"""
    with PdfProcessor(pdf_content=pdf_content) as processor:
        return processor.extract_text_items(page_numbers=page_numbers)


def process_pdf_from_file(pdf_path: str, page_numbers: Optional[List[int]] = None) -> List[PdfTextItem]:
    """从文件路径处理PDF的便捷函数"""
    with PdfProcessor(pdf_path=pdf_path) as processor:
        return processor.extract_text_items(page_numbers=page_numbers)