
def summarize_ocr_results(ocr_result, use_polys=False):
    """
    汇总 PaddleOCR 的识别结果，仅保留 text 和 bbox 信息
    :param ocr_result: PaddleOCR 的识别结果（列表，每个元素是一个字典）
    :param use_polys: 是否使用多边形坐标（dt_polys）而非矩形框（rec_boxes）
    :return: 汇总后的列表，每个元素格式：{'text': '...', 'bbox': [x1, y1, x2, y2]}
    """
    summarized = []

    for elem in ocr_result:
        # 跳过无效数据
        if not elem or ('rec_texts' not in elem and 'dt_texts' not in elem):
            continue

        # 确定文本和坐标的 key
        texts_key = 'dt_texts' if 'dt_texts' in elem else 'rec_texts'
        boxes_key = 'dt_polys' if use_polys and 'dt_polys' in elem else 'rec_boxes'

        # 如果指定的坐标类型不存在，尝试另一种
        if boxes_key not in elem:
            boxes_key = 'rec_boxes' if boxes_key == 'dt_polys' else 'dt_polys'
            if boxes_key not in elem:
                continue  # 两种坐标都没有，跳过

        # 遍历文本和坐标
        for text, box in zip(elem[texts_key], elem[boxes_key]):
            if use_polys and boxes_key == 'dt_polys':
                # 多边形转矩形框 [x1, y1, x2, y2]
                xs = [p[0] for p in box]
                ys = [p[1] for p in box]
                bbox = [min(xs), min(ys), max(xs), max(ys)]
            else:
                # 直接使用矩形框
                bbox = box.copy() if isinstance(box, list) else box.tolist()

            summarized.append({
                'text': text,
                'bbox': bbox
            })

    return summarized