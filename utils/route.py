import json

from fastapi import File, UploadFile, HTTPException, Form, APIRouter, Body
from loguru import logger
from typing import Type, List

from pydantic import TypeAdapter

from models.base import VisionDetectedResult
from models.integrated_models import LogicResult
from utils.pdf import PdfTextItem
from vision.ocr_engine.engine import OcrModelType

from api.base import Config, ApiResponse
from utils.web import uploadfile_to_image_or_numpy, validate_detection_file, validate_image_file


def create_detection_router(detector_cls: Type, prefix: str, tag_name: str) -> APIRouter:
    """
    创建通用检测路由
    """
    router = APIRouter(prefix=prefix, tags=[tag_name])

    @router.post("/vision_detect")
    async def vision_detect(
            file: UploadFile = File(...),
            confidence: float = Form(default=Config.DEFAULT_CONFIDENCE),
            iou_threshold: float = Form(default=Config.DEFAULT_IOU, alias="iouThreshold"),
            ocr_model: OcrModelType = Form(default=OcrModelType.RAPID, alias="ocrModel")
    ):
        logger.info(f"[{tag_name}][vision_detect] 参数: confidence={confidence}, iou_threshold={iou_threshold}, ocr_model={ocr_model}")
        validate_detection_file(file, confidence, iou_threshold, Config.ALLOWED_EXTENSIONS)

        try:
            test_img = await uploadfile_to_image_or_numpy(file, to_numpy=True)

            detector = detector_cls(ocr_model, confidence, iou_threshold)

            logger.info(f"[{tag_name}] 开始视觉检测: {file.filename}")
            result = detector.vision_detect(file.filename, test_img)
            logger.info(f"[{tag_name}] 视觉检测完成: {file.filename}")

            return ApiResponse.success_response(data=result)

        except HTTPException:
            logger.warning(f"[{tag_name}] HTTPException 捕获: {file.filename}")
            raise
        except Exception as e:
            logger.exception(f"[{tag_name}] 检测过程中发生错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"检测失败: {str(e)}")

    @router.post("/logic_compute")
    async def logic_compute(
            file: UploadFile = File(...),
            ocr_model: OcrModelType = Form(default=OcrModelType.RAPID, alias="ocrModel"),
            detected_result: str = Form(),
            pdf_text: str = Form(default=None, alias="pdfText"),
    ):
        logger.info(f"[{tag_name}][logic_compute] 参数: ocr_model={ocr_model}")
        if not validate_image_file(file, Config.ALLOWED_EXTENSIONS):
            raise HTTPException(status_code=400, detail="不支持的文件格式")

        try:
            test_img = await uploadfile_to_image_or_numpy(file, to_numpy=True)

            detector = detector_cls(ocr_model)

            logger.info(f"[{tag_name}] 开始逻辑计算: {file.filename}")

            detected_result = json.loads(detected_result)
            detected_result = VisionDetectedResult(**detected_result)

            if pdf_text:
                try:
                    pdf_text_list = json.loads(pdf_text)
                    pdf_text_list = pdf_text_list["data"]# 先反序列化成 list[dict]
                    detected_result.pdf_texts = TypeAdapter(List[PdfTextItem]).validate_python(pdf_text_list)
                except Exception as e:
                    logger.warning(f"解析 pdf_text 失败: {e}")
                    detected_result.pdf_texts = []

            result = detector.logic_compute(test_img, detected_result)

            logger.info(f"[{tag_name}] 逻辑计算完成: {file.filename}")
            return ApiResponse.success_response(data=result)

        except HTTPException:
            logger.warning(f"[{tag_name}] HTTPException 捕获: {file.filename}")
            raise
        except Exception as e:
            logger.exception(f"[{tag_name}] 检测过程中发生错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"检测失败: {str(e)}")

    @router.post("/anomaly_check")
    async def anomaly_check(
            detected_result: LogicResult = Body(...),
    ):
        logger.info(f"[{tag_name}][anomaly_check]")

        try:
            detector = detector_cls()

            logger.info(f"[{tag_name}] 开始异常检测")
            detector.anomaly_check(detected_result)
            logger.info(f"[{tag_name}] 异常检测完成")
            return ApiResponse.success_response(data=detected_result)

        except HTTPException:
            logger.warning(f"[{tag_name}] HTTPException 捕获")
            raise
        except Exception as e:
            logger.exception(f"[{tag_name}] 检测过程中发生错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"检测失败: {str(e)}")

    @router.post("/detect")
    async def detect(
            file: UploadFile = File(...),
            pdf_text: str = Form(default=None, alias="pdfText"),
            confidence: float = Form(default=Config.DEFAULT_CONFIDENCE),
            iou_threshold: float = Form(default=Config.DEFAULT_IOU, alias="iouThreshold"),
            ocr_model: OcrModelType = Form(default=OcrModelType.RAPID, alias="ocrModel")
    ):
        logger.info(f"[{tag_name}][detect] 参数: confidence={confidence}, iou_threshold={iou_threshold}, ocr_model={ocr_model}")
        validate_detection_file(file, confidence, iou_threshold, Config.ALLOWED_EXTENSIONS)

        try:
            test_img = await uploadfile_to_image_or_numpy(file, to_numpy=True)

            detector = detector_cls(ocr_model, confidence, iou_threshold)

            logger.info(f"[{tag_name}] 开始视觉检测以及逻辑计算: {file.filename}")

            vision_result = detector.vision_detect(file.filename, test_img)

            if pdf_text:
                try:
                    pdf_text_list = json.loads(pdf_text)  # 先反序列化成 list[dict]
                    vision_result.pdf_texts = TypeAdapter(List[PdfTextItem]).validate_python(pdf_text_list)
                except Exception as e:
                    logger.warning(f"解析 pdf_text 失败: {e}")
                    vision_result.pdf_texts = []

            result = detector.logic_compute(test_img, vision_result)

            logger.info(f"[{tag_name}] 检测与逻辑计算完成: {file.filename}")
            return ApiResponse.success_response(data=result, vision_result=vision_result)

        except HTTPException:
            logger.warning(f"[{tag_name}] HTTPException 捕获: {file.filename}")
            raise
        except Exception as e:
            logger.exception(f"[{tag_name}] 检测过程中发生错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"检测失败: {str(e)}")

    return router
