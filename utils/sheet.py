import string
from typing import List
from models.sheet_models import SheetConfig, MergeCell


class SheetConfigConverter:
    """
    将cells和texts数据转换为SheetConfig格式
    """

    @staticmethod
    def cells_to_sheet_config(cells: List[List[int]], texts: List[str], sheet_name: str = "Sheet1") -> dict:
        """
        将cells和texts转换为SheetConfig的数据格式

        Args:
            cells: List[List[int]], 形状为 [n, 4]，每行包含 [row_start, row_end, col_start, col_end]
            texts: List[str], 长度为 n，每个单元格的文本内容
            sheet_name: 工作表名称

        Returns:
            dict: 符合SheetConfig格式的字典
        """
        if len(cells) != len(texts):
            raise ValueError("cells 和 texts 的长度必须相同")

        # 计算数据范围
        max_row = max(cell[1] for cell in cells) if cells else 1
        max_col = max(cell[3] for cell in cells) if cells else 1
        min_row = min(cell[0] for cell in cells) if cells else 1
        min_col = min(cell[2] for cell in cells) if cells else 1

        # 创建二维数组来存储数据
        data_array = [["" for _ in range(max_col - min_col + 1)]
                      for _ in range(max_row - min_row + 1)]

        # 收集合并单元格信息
        merge_cells = []

        # 填充数据
        for i, (cell, text) in enumerate(zip(cells, texts)):
            row_start, row_end, col_start, col_end = cell

            # 调整为0基索引
            array_row = row_start - min_row
            array_col = col_start - min_col

            # 填充文本到数组中
            if array_row < len(data_array) and array_col < len(data_array[0]):
                data_array[array_row][array_col] = text or ""

            # 如果是合并单元格，添加到merge_cells
            if row_start != row_end or col_start != col_end:
                merge_range = (f"{SheetConfigConverter._col_to_letter(col_start+1)}{row_start+1}:"
                               f"{SheetConfigConverter._col_to_letter(col_end+1)}{row_end+1}")
                merge_cells.append({
                    "range": merge_range,
                    "value": text
                })

        # 构建SheetConfig格式的数据
        config_data = {
            "name": sheet_name,
            "data_format": "array",
            "data": data_array,
            "has_headers": False,  # 根据实际情况调整
            "start_row": min_row,
            "start_col": min_col,
            "merge_cells": merge_cells if merge_cells else []
        }

        return config_data

    @staticmethod
    def _col_to_letter(col_num: int) -> str:
        """
        将列号转换为Excel列字母表示
        例如: 1 -> A, 2 -> B, 27 -> AA
        """
        result = ""
        while col_num > 0:
            col_num -= 1
            result = string.ascii_uppercase[col_num % 26] + result
            col_num //= 26
        return result

    @staticmethod
    def create_sheet_config(cells: List[List[int]], texts: List[str],
                            sheet_name: str = "Sheet1") -> 'SheetConfig':
        """
        直接创建SheetConfig对象

        Args:
            cells: List[List[int]], 形状为 [n, 4]，每行包含 [row_start, row_end, col_start, col_end]
            texts: List[str], 长度为 n，每个单元格的文本内容
            sheet_name: 工作表名称

        Returns:
            SheetConfig: SheetConfig对象实例
        """
        config_dict = SheetConfigConverter.cells_to_sheet_config(cells, texts, sheet_name)

        # 转换merge_cells为MergeCell对象
        merge_cells = [MergeCell(**merge_cell) for merge_cell in config_dict.get("merge_cells", [])]
        config_dict["merge_cells"] = merge_cells

        return SheetConfig(**config_dict)