from typing import List, Optional

from vision.core.data_types import BoundingBox


def find_vertical_split(bboxes: List[BoundingBox]) -> Optional[int]:
    if not bboxes:
        return None

    # 取所有中心点
    centers = [(box.x1 + box.x2) / 2 for box in bboxes if box.x1 is not None and box.x2 is not None]
    centers.sort()

    min_x = min(box.x1 for box in bboxes)
    max_x = max(box.x2 for box in bboxes)

    best_split = None
    best_score = float("-inf")

    for cx in centers:
        left_count = sum(1 for box in bboxes if (box.x1 + box.x2) / 2 <= cx)
        right_count = len(bboxes) - left_count

        left_width = max(cx - min_x, 1)  # 避免除零
        right_width = max(max_x - cx, 1)

        left_density = left_count / left_width
        right_density = right_count / right_width

        score = left_density - right_density
        if score > best_score:
            best_score = score
            best_split = cx

    return int(best_split) if best_split is not None else None