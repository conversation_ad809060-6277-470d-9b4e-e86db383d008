import numpy as np
from typing import List, Tuple, Optional, Union
from collections import Counter


def detect_monotonic_outliers(
        data: List[Union[int, float]],
        direction: str = 'increasing',
        suspicious_indices: Optional[List[int]] = None,
        suspicious_weight: float = 0.1,
        min_valid_ratio: float = 0.6
) -> Tuple[List[int], str, dict]:
    """
    检测数据中的单调性异常点

    Args:
        data: 输入的数据列表
        direction: 'increasing' 或 'decreasing'，指定期望的单调性
        suspicious_indices: 预先知道的可疑数据索引列表
        suspicious_weight: 可疑数据的权重 (0-1之间，越小表示越不可信)
        min_valid_ratio: 最小有效数据比例，用于验证结果合理性

    Returns:
        Tuple[异常点索引列表, 最终确定的单调性方向, 详细统计信息]
    """

    if len(data) < 3:
        return [], direction, {"error": "数据长度不足，无法进行单调性检测"}

    n = len(data)
    suspicious_set = set(suspicious_indices) if suspicious_indices else set()

    # 为每个数据点分配初始权重
    weights = []
    for i in range(n):
        if i in suspicious_set:
            weights.append(suspicious_weight)
        else:
            weights.append(1.0)

    # 投票机制：每个位置作为"正确起点"进行测试
    outlier_votes = Counter()  # 记录每个索引被标记为异常的次数
    total_votes = 0

    # 自动检测最可能的单调性方向
    detected_direction = _detect_direction(data, weights, suspicious_set)
    final_direction = detected_direction if direction == 'auto' else direction

    print(f"检测到的单调性方向: {final_direction}")
    print(f"可疑数据索引: {list(suspicious_set)}")

    # 对每个可能的"正确起点"进行投票
    for anchor_idx in range(n):
        anchor_weight = weights[anchor_idx]

        # 跳过权重过低的锚点
        if anchor_weight < 0.3:
            continue

        outliers_from_anchor = _find_outliers_from_anchor(
            data, anchor_idx, final_direction, weights
        )

        # 根据锚点权重调整投票权重
        vote_weight = anchor_weight
        total_votes += vote_weight

        for outlier_idx in outliers_from_anchor:
            outlier_votes[outlier_idx] += vote_weight

    # 计算每个点的异常概率
    outlier_probabilities = {}
    for idx in range(n):
        if total_votes > 0:
            prob = outlier_votes.get(idx, 0) / total_votes
            outlier_probabilities[idx] = prob
        else:
            outlier_probabilities[idx] = 0

    # 动态确定阈值
    threshold = _calculate_dynamic_threshold(outlier_probabilities, suspicious_set)

    # 确定最终异常点
    final_outliers = [
        idx for idx, prob in outlier_probabilities.items()
        if prob > threshold
    ]

    # 验证结果合理性
    valid_ratio = (n - len(final_outliers)) / n
    if valid_ratio < min_valid_ratio:
        print(f"警告: 有效数据比例 ({valid_ratio:.2f}) 低于最小要求 ({min_valid_ratio})")

    # 统计信息
    stats = {
        "total_points": n,
        "outliers_count": len(final_outliers),
        "valid_ratio": valid_ratio,
        "detected_direction": detected_direction,
        "final_direction": final_direction,
        "threshold_used": threshold,
        "outlier_probabilities": outlier_probabilities,
        "suspicious_indices": list(suspicious_set),
        "vote_summary": dict(outlier_votes)
    }

    return sorted(final_outliers), final_direction, stats


def _detect_direction(data: List[Union[int, float]], weights: List[float], suspicious_set: set) -> str:
    """自动检测数据的主要单调性方向"""

    increasing_score = 0
    decreasing_score = 0
    total_weight = 0

    for i in range(len(data) - 1):
        # 跳过可疑数据点的比较
        if i in suspicious_set or (i + 1) in suspicious_set:
            continue

        weight = min(weights[i], weights[i + 1])
        total_weight += weight

        if data[i + 1] > data[i]:
            increasing_score += weight
        elif data[i + 1] < data[i]:
            decreasing_score += weight

    if total_weight == 0:
        return 'increasing'  # 默认值

    if increasing_score > decreasing_score:
        return 'increasing'
    else:
        return 'decreasing'


def _find_outliers_from_anchor(
        data: List[Union[int, float]],
        anchor_idx: int,
        direction: str,
        weights: List[float]
) -> List[int]:
    """以某个点为锚点，找出违反单调性的异常点"""

    n = len(data)
    outliers = []

    # 向后检查
    current_value = data[anchor_idx]
    for i in range(anchor_idx + 1, n):
        expected_condition = (
            data[i] >= current_value if direction == 'increasing'
            else data[i] <= current_value
        )

        if not expected_condition:
            outliers.append(i)
        else:
            # 更新当前值为正常点的值
            current_value = data[i]

    # 向前检查
    current_value = data[anchor_idx]
    for i in range(anchor_idx - 1, -1, -1):
        expected_condition = (
            data[i] <= current_value if direction == 'increasing'
            else data[i] >= current_value
        )

        if not expected_condition:
            outliers.append(i)
        else:
            # 更新当前值为正常点的值
            current_value = data[i]

    return outliers


def _calculate_dynamic_threshold(outlier_probabilities: dict, suspicious_set: set) -> float:
    """动态计算异常点阈值"""

    probs = list(outlier_probabilities.values())
    if not probs:
        return 0.5

    # 基础阈值：使用概率分布的统计特征
    mean_prob = np.mean(probs)
    std_prob = np.std(probs)
    base_threshold = mean_prob + std_prob

    # 考虑可疑点的调整
    suspicious_probs = [outlier_probabilities[idx] for idx in suspicious_set if idx in outlier_probabilities]
    if suspicious_probs:
        suspicious_mean = np.mean(suspicious_probs)
        # 如果可疑点的平均概率较高，适当降低阈值
        if suspicious_mean > base_threshold:
            base_threshold = (base_threshold + suspicious_mean) / 2

    # 确保阈值在合理范围内
    return max(0.3, min(0.8, base_threshold))


# 示例使用和测试函数
def test_monotonicity_checker():
    """测试函数"""

    # 测试案例1: 递增序列，第一个数据错误
    print("=== 测试案例1: 递增序列，第一个数据错误 ===")
    data1 = [100, 1, 2, 3, 4, 5, 6, 7, 8, 9]  # 第一个数明显异常
    outliers1, direction1, stats1 = detect_monotonic_outliers(data1, direction='increasing')
    print(f"数据: {data1}")
    print(f"异常点索引: {outliers1}")
    print(f"单调性方向: {direction1}")
    print(f"异常概率: {stats1['outlier_probabilities']}")
    print()

    # 测试案例2: 递增序列，中间有几个异常点
    print("=== 测试案例2: 递增序列，中间有异常点 ===")
    data2 = [1, 2, 15, 4, 5, 0, 7, 8, 9, 10]  # 索引2, 5有异常
    outliers2, direction2, stats2 = detect_monotonic_outliers(data2, direction='increasing')
    print(f"数据: {data2}")
    print(f"异常点索引: {outliers2}")
    print(f"异常概率: {stats2['outlier_probabilities']}")
    print()

    # 测试案例3: 带有预知可疑数据
    print("=== 测试案例3: 带有预知可疑数据 ===")
    data3 = [1, 2, 15, 4, 5, 0, 7, 8, 9, 10]
    suspicious_indices = [2, 5, 8]  # 预先知道索引2,5,8可能有问题（其中8是误报）
    outliers3, direction3, stats3 = detect_monotonic_outliers(
        data3,
        direction='increasing',
        suspicious_indices=suspicious_indices,
        suspicious_weight=0.3
    )
    print(f"数据: {data3}")
    print(f"预知可疑索引: {suspicious_indices}")
    print(f"异常点索引: {outliers3}")
    print(f"异常概率: {stats3['outlier_probabilities']}")
    print()

    # 测试案例4: 自动检测单调性方向
    print("=== 测试案例4: 自动检测单调性方向 ===")
    data4 = [10, 9, 15, 7, 6, 5, 4, 3, 2, 1]  # 主要是递减，但有异常点
    outliers4, direction4, stats4 = detect_monotonic_outliers(data4, direction='auto')
    print(f"数据: {data4}")
    print(f"检测到的方向: {direction4}")
    print(f"异常点索引: {outliers4}")
    print()


def simple_test():
    data = [3.8, 3, 3, 9, 18.8, 25.8, 28.8, 32, 35.3]
    # suspicious_indices = [2, 3]
    outliers, direction, stats = detect_monotonic_outliers(data, direction='increasing')
    print(f"异常点索引: {outliers}")



if __name__ == "__main__":
    # test_monotonicity_detector()
    simple_test()
