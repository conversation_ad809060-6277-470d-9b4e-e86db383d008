import io
import os
import cv2
import base64
import numpy as np
from io import BytesIO
from fastapi import UploadFile, HTTPException
from typing import List
from pathlib import Path
from PIL import Image
from typing import Union
from loguru import logger


# 文件验证函数
def validate_image_file(file: UploadFile, allowed_extensions) -> bool:
    """验证上传的图片文件"""
    # 检查文件扩展名
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in allowed_extensions:
        return False

    # 检查MIME类型
    if not file.content_type.startswith('image/'):
        return False

    return True


def validate_detection_file(file: UploadFile, confidence, iou_threshold, allowed_extensions) -> None:
    """验证上传的检测文件"""
    # 验证参数
    if not (0.0 <= confidence <= 1.0):
        raise HTTPException(status_code=400, detail="置信度阈值必须在0.0-1.0之间")
    if not (0.0 <= iou_threshold <= 1.0):
        raise HTTPException(status_code=400, detail="IoU阈值必须在0.0-1.0之间")

    # 验证文件
    if not validate_image_file(file, allowed_extensions):
        raise HTTPException(status_code=400, detail="不支持的文件格式")


async def save_upload_file(file: UploadFile, destination: str, max_file_size) -> str:
    """保存上传的文件"""
    try:
        with open(destination, "wb") as buffer:
            content = await file.read()
            if len(content) > max_file_size:
                raise HTTPException(status_code=413, detail="文件太大")
            buffer.write(content)
        return destination
    except Exception as e:
        logger.error(f"保存文件失败: {e}")
        raise HTTPException(status_code=500, detail="文件保存失败")


def cleanup_temp_files(file_paths: List[str]):
    """清理临时文件"""
    for file_path in file_paths:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"已清理临时文件: {file_path}")
        except Exception as e:
            logger.error(f"清理临时文件失败 {file_path}: {e}")


def cleanup_temp_files_with_prefix(file_dir, prefix: str):
    """清理指定目录下以特定前缀开头的临时文件"""
    try:
        for file_path in Path(file_dir).glob(f"{prefix}*"):
            if file_path.is_file():
                file_path.unlink()
                logger.info(f"已清理临时文件: {file_path}")
    except Exception as e:
        logger.error(f"清理临时文件失败: {e}")


def image_to_base64(image: Union[str, Image.Image, np.ndarray]) -> str:
    """
    将图片转换为base64编码，支持文件路径（str）、PIL.Image、OpenCV图像（np.ndarray）

    :param image: 图片输入，可以是路径字符串、PIL.Image 或 OpenCV 的 ndarray
    :return: base64 字符串，带 data:image/jpeg;base64, 前缀
    """
    try:
        if isinstance(image, str):
            with open(image, "rb") as image_file:
                encoded_string = base64.b64encode(image_file.read()).decode()
                return f"data:image/jpeg;base64,{encoded_string}"

        elif isinstance(image, Image.Image):
            buffer = BytesIO()
            image.save(buffer, format="JPEG")
            encoded_string = base64.b64encode(buffer.getvalue()).decode()
            return f"data:image/jpeg;base64,{encoded_string}"

        elif isinstance(image, np.ndarray):
            # OpenCV 是 BGR，需要转成 RGB 再转为 PIL，或者直接用 cv2.imencode
            success, encoded_image = cv2.imencode(".jpg", image)
            if not success:
                raise ValueError("OpenCV 图像编码失败")
            encoded_string = base64.b64encode(encoded_image.tobytes()).decode()
            return f"data:image/jpeg;base64,{encoded_string}"

        else:
            raise TypeError("不支持的图像类型")

    except Exception as e:
        logger.error(f"图片转 base64 失败: {e}")
        return ""


async def uploadfile_to_image_or_numpy(file: UploadFile, to_numpy: bool = False):
    """
    异步地将 FastAPI UploadFile 转为 PIL.Image 或 numpy.ndarray
    :param file: FastAPI UploadFile
    :param to_numpy: 是否转为 numpy.ndarray，默认 False 返回 PIL.Image
    :return: PIL.Image 或 numpy.ndarray
    """
    try:
        contents = await file.read()  # 使用异步读取
        image = Image.open(io.BytesIO(contents)).convert('RGB')
        return np.array(image) if to_numpy else image
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"图片读取失败: {e}")