from enum import Enum
from pathlib import Path
from typing import Dict, Any
from settings import ROOT


class ClassifierModelType(str, Enum):
    """模型类型枚举"""
    PAGE_TYPE = "page_type"
    TABLE_CELL_CONTENT = "table_cell_content"


class TableCellContentType(Enum):

    EMPTY = "empty"
    NOT_EMPTY = "not_empty"


CLASSIFIER_MODEL_CONFIG: Dict[str, Dict[str, Any]] = {
    ClassifierModelType.PAGE_TYPE: {
        "description": "剖面区域检测模型",
        "weights_path": ROOT / "weights/classifier/page_type.onnx",
        "class_name": ["其他成果表", "剖面图", "图例", "富文本", "平面图", "纯文本", "表格为主", "钻孔柱状图", "错位表格成果表", "静力触探成果表"]
    },
    ClassifierModelType.TABLE_CELL_CONTENT: {
        "description": "表格单元内容检测模型",
        "weights_path": ROOT / "weights/classifier/table_unit_best.onnx",
        "class_name": [
            TableCellContentType.EMPTY.value,
            TableCellContentType.NOT_EMPTY.value
        ]
    }
}
