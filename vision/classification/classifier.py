"""
ONNX图像分类器
"""
import json
import time
import onnxruntime as ort
from pathlib import Path
from typing import Union, List, Optional, Dict, Any, Tuple
from loguru import logger

from vision.core import (
    ClassificationResult, BatchClassificationResult,
    ModelLoadingError
)
from .preprocessor import ClassificationPreprocessor
from .postprocessor import ClassificationPostProcessor


class ImageClassifier:
    """ONNX图像分类器，支持批量推理"""
    
    def __init__(self,
                 onnx_model_path: Union[str, Path],
                 class_names: Optional[List[str]] = None,
                 providers: Optional[List[str]] = None,
                 max_batch_size: int = 16,
                 image_size: Tuple[int, int] = None,
                 normalize_params: Optional[Dict[str, List[float]]] = None):
        """
        初始化ONNX分类器
        
        Args:
            onnx_model_path: ONNX模型文件路径
            class_names: 类别名称列表
            providers: ONNXRuntime执行提供商列表
            max_batch_size: 最大批量大小
            image_size: 图像尺寸 (height, width)
            normalize_params: 归一化参数 {'mean': [...], 'std': [...]}
        """
        self.onnx_model_path = Path(onnx_model_path)
        self.max_batch_size = max_batch_size
        
        # 初始化ONNX会话
        self._init_session(providers)
        
        # 处理类别名称
        self.class_names = self._init_class_names(class_names)
        
        # 确定图像尺寸
        if image_size is None:
            if len(self.input_shape) == 4:
                image_size = (self.input_shape[2], self.input_shape[3])
            else:
                image_size = (224, 224)
        
        # 处理归一化参数
        if normalize_params is None:
            normalize_params = {
                'mean': [0.485, 0.456, 0.406],
                'std': [0.229, 0.224, 0.225]
            }
        
        # 初始化组件
        self.preprocessor = ClassificationPreprocessor(
            image_size=image_size,
            normalize_mean=normalize_params['mean'],
            normalize_std=normalize_params['std']
        )
        self.postprocessor = ClassificationPostProcessor(self.class_names)
        
        logger.info(f"ONNX分类器初始化完成 - 输入尺寸: {self.input_shape}, 最大batch: {self.max_batch_size}")
    
    def _init_session(self, providers: Optional[List[str]]):
        """初始化ONNX推理会话"""
        try:
            if providers is None:
                providers = ['CPUExecutionProvider']
                available_providers = ort.get_available_providers()
                if 'CUDAExecutionProvider' in available_providers:
                    providers.insert(0, 'CUDAExecutionProvider')
            
            self.session = ort.InferenceSession(str(self.onnx_model_path), providers=providers)
            
            # 获取模型输入输出信息
            self.input_name = self.session.get_inputs()[0].name
            self.output_name = self.session.get_outputs()[0].name
            self.input_shape = self.session.get_inputs()[0].shape
            
            # 检查模型是否支持动态batch
            if self.input_shape[0] == -1 or isinstance(self.input_shape[0], str):
                self.supports_dynamic_batch = True
            else:
                self.supports_dynamic_batch = False
                self.max_batch_size = min(self.max_batch_size, self.input_shape[0])
                
        except Exception as e:
            raise ModelLoadingError(f"ONNX模型加载失败: {e}")
    
    def _init_class_names(self, class_names: Optional[List[str]]) -> Optional[List[str]]:
        """初始化类别名称"""
        if class_names is not None:
            return class_names
        
        # 尝试从模型元数据加载
        try:
            info_path = self.onnx_model_path.parent / f"{self.onnx_model_path.stem}_export_info.json"
            if info_path.exists():
                with open(info_path, 'r') as f:
                    export_info = json.load(f)
                if 'class_names' in export_info:
                    logger.info(f"从导出信息加载类别名称")
                    return export_info['class_names']
        except Exception as e:
            logger.warning(f"加载导出信息失败: {e}")
        
        return None
    
    def predict_batch(self, 
                     image_inputs: List[Union[str, Any]],
                     batch_size: Optional[int] = None,
                     top_k: int = 5) -> List[BatchClassificationResult]:
        """
        批量图像分类
        
        Args:
            image_inputs: 图像输入列表
            batch_size: 批量大小
            top_k: 返回前k个预测结果
            
        Returns:
            List[BatchClassificationResult]: 批量分类结果
        """
        if not image_inputs:
            return []
        
        if batch_size is None:
            batch_size = min(self.max_batch_size, len(image_inputs))
        else:
            batch_size = min(batch_size, self.max_batch_size, len(image_inputs))
        
        logger.info(f"开始批量分类 {len(image_inputs)} 张图像，batch_size={batch_size}")
        
        # 预处理
        batch_preprocess_results = self.preprocessor.preprocess_batch(
            image_inputs, batch_size=batch_size
        )
        
        batch_results = []
        total_start_time = time.time()
        
        # 批量推理
        for preprocess_result in batch_preprocess_results:
            batch_start_time = time.time()
            
            try:
                if preprocess_result.batch_data.shape[0] == 0:
                    # 空批次
                    batch_result = BatchClassificationResult(
                        results=[],
                        processing_time=time.time() - batch_start_time,
                        batch_size=0,
                        successful_count=0,
                        failed_indices=[]
                    )
                    batch_results.append(batch_result)
                    continue
                
                # 执行推理
                outputs = self.session.run(
                    [self.output_name],
                    {self.input_name: preprocess_result.batch_data}
                )[0]
                
                # 后处理
                results = self.postprocessor.process_batch_outputs(
                    outputs, preprocess_result, top_k=top_k
                )
                
                # 设置处理时间
                processing_time = time.time() - batch_start_time
                for result in results:
                    if result is not None:
                        result.processing_time = processing_time / len(results)
                
                # 统计结果
                successful_count = len([r for r in results if r is not None])
                failed_indices = [i for i, r in enumerate(results) if r is None]
                
                batch_result = BatchClassificationResult(
                    results=results,
                    processing_time=processing_time,
                    batch_size=len(results),
                    successful_count=successful_count,
                    failed_indices=failed_indices
                )
                
                batch_results.append(batch_result)
                
            except Exception as e:
                logger.error(f"批量推理失败: {e}")
                batch_result = BatchClassificationResult(
                    results=[],
                    processing_time=time.time() - batch_start_time,
                    batch_size=0,
                    successful_count=0,
                    failed_indices=[]
                )
                batch_results.append(batch_result)
        
        total_time = time.time() - total_start_time
        total_successful = sum(br.successful_count for br in batch_results)
        
        logger.info(f"批量分类完成 - 成功: {total_successful}/{len(image_inputs)}, 耗时: {total_time:.2f}s")
        
        return batch_results
    
    def predict_single(self, 
                      image_input: Union[str, Any],
                      top_k: int = 5) -> Optional[ClassificationResult]:
        """
        单张图像分类
        
        Args:
            image_input: 单张图像输入
            top_k: 返回前k个预测结果
            
        Returns:
            Optional[ClassificationResult]: 分类结果
        """
        batch_results = self.predict_batch([image_input], batch_size=1, top_k=top_k)
        
        if batch_results and batch_results[0].results:
            return batch_results[0].results[0]
        return None