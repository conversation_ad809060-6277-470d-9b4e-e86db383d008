"""
图像分类后处理器
"""
import numpy as np
from typing import List, Optional, Dict
from loguru import logger

from vision.core import (
    ClassificationResult, BatchPreprocessResult,
    PostprocessingError
)


class ClassificationPostProcessor:
    """图像分类后处理器"""
    
    def __init__(self, class_names: Optional[List[str]] = None):
        """
        初始化后处理器
        
        Args:
            class_names: 类别名称列表
        """
        self.class_names = class_names
    
    def process_batch_outputs(self, 
                             outputs: np.ndarray,
                             batch_preprocess_result: BatchPreprocessResult,
                             top_k: int = 5) -> List[Optional[ClassificationResult]]:
        """
        处理批量模型输出
        
        Args:
            outputs: 模型输出 (batch_size, num_classes)
            batch_preprocess_result: 批量预处理结果
            top_k: 返回前k个预测结果
            
        Returns:
            List[Optional[ClassificationResult]]: 每张图片的分类结果
        """
        try:
            if len(outputs.shape) == 1:
                outputs = np.expand_dims(outputs, axis=0)
            
            batch_size = outputs.shape[0]
            results = []
            
            for img_idx in range(batch_size):
                if img_idx >= len(batch_preprocess_result.image_metas):
                    results.append(None)
                    continue
                
                try:
                    image_meta = batch_preprocess_result.image_metas[img_idx]
                    image_output = outputs[img_idx]
                    
                    # 计算softmax概率
                    probabilities = self._softmax(image_output)
                    
                    # 获取top-k结果
                    actual_top_k = min(top_k, len(probabilities))
                    top_indices = np.argsort(probabilities)[::-1][:actual_top_k]
                    top_probs = probabilities[top_indices]
                    
                    # 构建top-k预测列表
                    top_k_predictions = []
                    for i in range(actual_top_k):
                        top_k_predictions.append({
                            'class_id': int(top_indices[i]),
                            'class_name': self._get_class_name(int(top_indices[i])),
                            'confidence': float(top_probs[i])
                        })
                    
                    # 创建分类结果
                    result = ClassificationResult(
                        class_id=int(top_indices[0]),
                        class_name=self._get_class_name(int(top_indices[0])),
                        confidence=float(top_probs[0]),
                        top_k_predictions=top_k_predictions,
                        image_index=image_meta.global_index,
                        processing_time=0.0  # 在上层设置
                    )
                    
                    results.append(result)
                    
                except Exception as e:
                    logger.error(f"后处理图像 {img_idx} 失败: {e}")
                    results.append(None)
            
            return results
            
        except Exception as e:
            raise PostprocessingError(f"批量后处理失败: {e}")
    
    def _softmax(self, x: np.ndarray) -> np.ndarray:
        """计算softmax"""
        exp_x = np.exp(x - np.max(x))
        return exp_x / np.sum(exp_x)
    
    def _get_class_name(self, class_id: int) -> str:
        """根据类别ID获取类别名称"""
        if self.class_names and class_id < len(self.class_names):
            return self.class_names[class_id]
        return f"class_{class_id}"