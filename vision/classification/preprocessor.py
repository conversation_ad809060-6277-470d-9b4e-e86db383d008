"""
图像分类预处理器
"""
import cv2
import numpy as np
from PIL import Image
from torchvision import transforms
from typing import List, Union, Optional, Tuple
from loguru import logger

from vision.core import (
    BatchPreprocessResult, ImageMeta, ImageHandler,
    PreprocessingError
)


class ClassificationPreprocessor:
    """图像分类预处理器"""
    
    def __init__(self, 
                 image_size: Tuple[int, int] = (224, 224),
                 normalize_mean: List[float] = None,
                 normalize_std: List[float] = None):
        """
        初始化预处理器
        
        Args:
            image_size: 目标图像尺寸 (height, width)
            normalize_mean: 归一化均值
            normalize_std: 归一化标准差
        """
        self.image_size = image_size
        
        if normalize_mean is None:
            normalize_mean = [0.485, 0.456, 0.406]
        if normalize_std is None:
            normalize_std = [0.229, 0.224, 0.225]
        
        self.transform = transforms.Compose([
            transforms.Resize(self.image_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=normalize_mean, std=normalize_std)
        ])
    
    def preprocess_batch(self, 
                        image_inputs: List[Union[str, np.ndarray, Image.Image]],
                        batch_size: Optional[int] = None) -> List[BatchPreprocessResult]:
        """
        批量预处理图像
        
        Args:
            image_inputs: 图像输入列表
            batch_size: 批量大小，None表示一次处理所有图像
            
        Returns:
            List[BatchPreprocessResult]: 批量预处理结果列表
        """
        if not image_inputs:
            return []
        
        if batch_size is None:
            batch_size = len(image_inputs)
        
        batch_results = []
        
        for i in range(0, len(image_inputs), batch_size):
            batch_inputs = image_inputs[i:i + batch_size]
            try:
                batch_result = self._process_single_batch(batch_inputs, start_index=i)
                batch_results.append(batch_result)
            except Exception as e:
                logger.error(f"预处理批次 {i//batch_size} 失败: {e}")
                # 创建空的结果
                empty_result = BatchPreprocessResult(
                    batch_data=np.zeros((0, 3, *self.image_size), dtype=np.float32),
                    image_metas=[],
                    valid_indices=[]
                )
                batch_results.append(empty_result)
        
        return batch_results
    
    def _process_single_batch(self, 
                             batch_inputs: List[Union[str, np.ndarray, Image.Image]],
                             start_index: int = 0) -> BatchPreprocessResult:
        """处理单个批次"""
        batch_data_list = []
        image_metas = []
        valid_indices = []
        
        for idx, image_input in enumerate(batch_inputs):
            try:
                # 统一输入处理
                bgr_image, source_info = ImageHandler.normalize_input(image_input)
                
                # BGR转RGB
                rgb_image = cv2.cvtColor(bgr_image, cv2.COLOR_BGR2RGB)
                pil_image = Image.fromarray(rgb_image)
                
                # 应用变换
                image_tensor = self.transform(pil_image)
                batch_data_list.append(image_tensor.numpy())
                
                # 创建元数据
                meta = ImageMeta(
                    source_type=source_info['type'],
                    source=source_info.get('source', 'unknown'),
                    original_shape=bgr_image.shape[:2],
                    processed_shape=self.image_size,
                    global_index=start_index + idx,
                    batch_index=len(valid_indices)
                )
                image_metas.append(meta)
                valid_indices.append(start_index + idx)
                
            except Exception as e:
                logger.error(f"预处理图像 {start_index + idx} 失败: {e}")
                continue
        
        # 构建批次数据
        if batch_data_list:
            batch_data = np.stack(batch_data_list, axis=0)
        else:
            batch_data = np.zeros((0, 3, *self.image_size), dtype=np.float32)
        
        return BatchPreprocessResult(
            batch_data=batch_data,
            image_metas=image_metas,
            valid_indices=valid_indices
        )