"""
核心模块
"""
from .data_types import (
    ImageMeta, BatchPreprocessResult, ClassificationResult, DetectionResult,
    BatchResult, BatchClassificationResult, BatchDetectionResult
)
from .input_handler import ImageHandler
from .exceptions import (
    AIProcessingError, InputProcessingError, ModelLoadingError,
    PreprocessingError, InferenceError, PostprocessingError
)
from .model_manager import ModelManager

__all__ = [
    'ImageMeta', 'BatchPreprocessResult', 'ClassificationResult', 'DetectionResult',
    'BatchResult', 'BatchClassificationResult', 'BatchDetectionResult',
    'ImageHandler',
    'AIProcessingError', 'InputProcessingError', 'ModelLoadingError',
    'PreprocessingError', 'InferenceError', 'PostprocessingError', 'ModelManager'
]