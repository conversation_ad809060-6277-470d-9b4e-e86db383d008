"""
统一输入处理器
"""
import cv2
import numpy as np
import requests
import base64
from PIL import Image
from io import BytesIO
from typing import Union, Tuple, Dict, Any, Optional, List

from loguru import logger

from .data_types import BoundingBox
from .exceptions import InputProcessingError


class ImageHandler:
    """统一的输入处理器，支持多种输入格式"""

    @staticmethod
    def crop_bbox(image: np.ndarray, bbox: BoundingBox) -> Optional[np.ndarray]:
        """
        根据 bbox 裁剪图像
        image_input: 图像对象
        bbox: 边界框
        """
        x1, y1, x2, y2 = bbox.xyxy
        # 确保坐标在图像范围内
        x1 = max(0, int(x1))
        y1 = max(0, int(y1))
        x2 = min(image.shape[1], int(x2))
        y2 = min(image.shape[0], int(y2))
        if x2 <= x1 or y2 <= y1:
            logger.warning("无效的裁剪坐标，导致裁剪区域为空")
            # raise InputProcessingError("无效的裁剪坐标，导致裁剪区域为空")
            return None
        return image[y1:y2, x1:x2]

    @staticmethod
    def crop_bboxes(image: np.ndarray, bboxes: List[BoundingBox]) -> List[np.ndarray]:
        crop_images = [ImageHandler.crop_bbox(image, bbox) for bbox in bboxes]
        return crop_images

    @staticmethod
    def normalize_input(image_input: Union[str, np.ndarray, Image.Image]) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        统一处理各种输入格式，返回BGR格式的numpy数组
        
        Args:
            image_input: 图像输入
            
        Returns:
            Tuple[np.ndarray, Dict]: (BGR图像数组, 源信息)
        """
        try:
            if isinstance(image_input, str):
                img, source_info = ImageHandler._handle_string_input(image_input)
            elif isinstance(image_input, Image.Image):
                img, source_info = ImageHandler._handle_pil_input(image_input)
            elif isinstance(image_input, np.ndarray):
                img, source_info = ImageHandler._handle_numpy_input(image_input)
            else:
                raise InputProcessingError(f"不支持的输入类型：{type(image_input)}")
            
            if img is None or img.size == 0:
                raise InputProcessingError("处理后的图像为空")
                
            if len(img.shape) != 3 or img.shape[2] != 3:
                raise InputProcessingError(f"图像格式错误，期望3通道BGR图像，得到：{img.shape}")
            
            source_info['final_shape'] = img.shape
            return img, source_info
            
        except Exception as e:
            raise InputProcessingError(f"输入处理失败: {e}")
    
    @staticmethod
    def _handle_string_input(image_input: str) -> Tuple[np.ndarray, Dict[str, Any]]:
        """处理字符串输入（文件路径、URL、base64）"""
        source_info = {'source': image_input}
        
        if image_input.startswith(('http://', 'https://')):
            source_info['type'] = 'url'
            img = ImageHandler._load_from_url(image_input)
        elif image_input.startswith('data:image'):
            source_info['type'] = 'base64'
            img = ImageHandler._load_from_base64(image_input)
        else:
            source_info['type'] = 'path'
            img = cv2.imread(image_input)
            if img is None:
                raise ValueError(f"无法读取图像文件：{image_input}")
        
        return img, source_info
    
    @staticmethod
    def _handle_pil_input(image_input: Image.Image) -> Tuple[np.ndarray, Dict[str, Any]]:
        """处理PIL图像输入"""
        source_info = {
            'type': 'pil',
            'mode': image_input.mode,
            'size': image_input.size
        }
        
        # 转换PIL图像到numpy数组
        if image_input.mode == 'RGBA':
            rgb_img = Image.new('RGB', image_input.size, (255, 255, 255))
            rgb_img.paste(image_input, mask=image_input.split()[-1])
            img_array = np.array(rgb_img)
        elif image_input.mode == 'L':
            img_array = np.array(image_input.convert('RGB'))
        else:
            img_array = np.array(image_input.convert('RGB'))
        
        img = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        return img, source_info
    
    @staticmethod
    def _handle_numpy_input(image_input: np.ndarray) -> Tuple[np.ndarray, Dict[str, Any]]:
        """处理numpy数组输入"""
        source_info = {
            'type': 'numpy',
            'shape': image_input.shape,
            'dtype': str(image_input.dtype)
        }
        
        img = ImageHandler._normalize_numpy_array(image_input)
        return img, source_info
    
    @staticmethod
    def _load_from_url(url: str) -> np.ndarray:
        """从URL加载图像"""
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        image = Image.open(BytesIO(response.content))
        img_array = np.array(image.convert('RGB'))
        return cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
    
    @staticmethod
    def _load_from_base64(base64_str: str) -> np.ndarray:
        """从Base64字符串加载图像"""
        if ',' in base64_str:
            base64_data = base64_str.split(',')[1]
        else:
            base64_data = base64_str
        
        image_data = base64.b64decode(base64_data)
        image = Image.open(BytesIO(image_data))
        img_array = np.array(image.convert('RGB'))
        return cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
    
    @staticmethod
    def _normalize_numpy_array(img_array: np.ndarray) -> np.ndarray:
        """标准化numpy数组格式"""
        img = img_array.copy()
        
        # 处理数据类型
        if img.dtype == np.uint8:
            pass
        elif img.dtype in [np.float32, np.float64]:
            if img.max() <= 1.0:
                img = (img * 255).astype(np.uint8)
            else:
                img = img.astype(np.uint8)
        else:
            img = img.astype(np.uint8)
        
        # 处理维度
        if len(img.shape) == 2:
            img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
        elif len(img.shape) == 3:
            if img.shape[2] == 1:
                img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
            elif img.shape[2] == 4:
                img = img[:, :, :3]
        
        return img