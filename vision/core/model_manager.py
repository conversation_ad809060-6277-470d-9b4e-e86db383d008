import threading
from typing import Dict, Any
from loguru import logger


class ModelManager:
    """模型管理器，使用单例模式管理多个YOLO模型"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.models: Dict[str, Any] = {}
            self.initialized = True

    def unload_model(self, model_name: str):
        """卸载模型释放内存"""
        if model_name in self.models:
            del self.models[model_name]
            logger.info(f"模型已卸载: {model_name}")

    def list_models(self) -> list:
        """列出所有已加载的模型"""
        return list(self.models.keys())

    def get_memory_usage(self) -> Dict[str, Any]:
        """获取内存使用情况"""
        import psutil
        process = psutil.Process()
        return {
            "loaded_models": len(self.models),
            "model_names": list(self.models.keys()),
            "memory_mb": process.memory_info().rss / 1024 / 1024
        }