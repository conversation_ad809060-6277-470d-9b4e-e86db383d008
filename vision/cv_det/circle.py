import cv2
import numpy as np

# 读取图片
img = cv2.imread("/Users/<USER>/CodeRepo/08-CADWithAi/data/images/KC2024-122/page_17.png")
output = img.copy()

# 转为灰度图
gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

# 高斯模糊（减少噪声）
gray = cv2.G<PERSON>sianBlur(gray, (9, 9), 2)

# 霍夫圆检测
circles = cv2.HoughCircles(
    gray,
    cv2.HOUGH_GRADIENT,   # 检测方法
    dp=1.2,               # 累加器分辨率与图像分辨率的反比
    minDist=20,           # 圆心之间的最小距离
    param1=100,           # Canny 边缘检测高阈值
    param2=30,            # 圆心累加器阈值（越小检测越多，误检率也会高）
    minRadius=0,          # 最小半径
    maxRadius=0           # 最大半径（0 表示不限制）
)

# 如果找到了圆
if circles is not None:
    circles = np.uint16(np.around(circles))
    for (x, y, r) in circles[0, :]:
        # 画圆
        cv2.circle(output, (x, y), r, (0, 255, 0), 2)
        # 画圆心
        cv2.circle(output, (x, y), 2, (0, 0, 255), 3)

# 显示结果
cv2.imshow("Detected Circles", output)
cv2.waitKey(0)
cv2.destroyAllWindows()
