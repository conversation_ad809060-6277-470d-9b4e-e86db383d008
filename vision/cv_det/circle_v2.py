import cv2
import numpy as np

def detect_small_cad_circles(img_path, scale=3, min_r=1, max_r=6):
    img = cv2.imread(img_path)
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 放大图像
    big = cv2.resize(gray, (0, 0), fx=scale, fy=scale, interpolation=cv2.INTER_LINEAR)

    # 二值化（确保线条是白色）
    _, binary = cv2.threshold(big, 200, 255, cv2.THRESH_BINARY_INV)

    # 边缘检测
    edges = cv2.Canny(binary, 50, 150)

    # 霍夫圆检测（针对小圆）
    circles = cv2.HoughCircles(
        edges,
        cv2.HOUGH_GRADIENT,
        dp=1,
        minDist=5,       # 圆心之间最小距离
        param1=50,       # Canny 高阈值
        param2=10,       # 圆心累加器阈值（越小越容易检测）
        minRadius=min_r * scale,
        maxRadius=max_r * scale
    )

    output = img.copy()
    output[:, :] = (255, 255, 255)  # 白色背景
    detected_circles = []
    if circles is not None:
        circles = np.uint16(np.around(circles))
        for (x, y, r) in circles[0, :]:
            # 缩回原图比例
            x //= scale
            y //= scale
            r //= scale
            detected_circles.append((x, y, r))
            cv2.circle(output, (x, y), r, (0, 255, 0), 1)
            cv2.circle(output, (x, y), 1, (0, 0, 255), 2)

    return output, detected_circles


if __name__ == "__main__":
    result, circles = detect_small_cad_circles("/Users/<USER>/CodeRepo/08-CADWithAi/data/images/KC2024-122/page_17.png")
    print(f"Detected {len(circles)} small circles")
    cv2.imshow("Small Circles", result)
    cv2.waitKey(0)
