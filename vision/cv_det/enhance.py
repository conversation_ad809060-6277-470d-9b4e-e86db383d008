import cv2
import numpy as np


def enhance_image(image, scale=2.0):
    # 1. 读取并灰度化
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # 2. 放大（推荐 cubic 插值，比最近邻平滑，同时不会太糊）
    h, w = gray.shape
    gray = cv2.resize(gray, (int(w * scale), int(h * scale)), interpolation=cv2.INTER_CUBIC)

    # 3. 轻度去噪（保边缘）
    denoise = cv2.bilateralFilter(gray, d=5, sigmaColor=30, sigmaSpace=30)

    # 4. 对比度拉伸（避免笔画灰模糊）
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    enhanced = clahe.apply(denoise)

    # 5. 二值化（可选，OCR 引擎不同效果不同）
    _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

    return binary