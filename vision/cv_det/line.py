import cv2
import numpy as np


def filter_lines_by_angle(lines, keep_ranges, min_length=0):
    """
    按角度区间过滤直线

    参数:
        lines: HoughLinesP 或 LSD 输出的直线 [[x1, y1, x2, y2], ...]
        keep_ranges: 要保留的角度区间 [(min_angle, max_angle), ...] (角度范围 0~180)
        min_length: 直线最小长度 (像素)
    返回:
        过滤后的直线
    """
    filtered = []
    for line in lines:
        if isinstance(line[0], (list, np.ndarray)):
            x1, y1, x2, y2 = line[0]
        else:  # LSD 返回的是 (x1,y1,x2,y2) 浮点数
            x1, y1, x2, y2 = line

        dx, dy = x2 - x1, y2 - y1
        length = np.hypot(dx, dy)
        if length < min_length:
            continue

        # 计算角度并归一化到 [0,180)
        angle = np.degrees(np.arctan2(dy, dx))
        angle = abs(angle)
        if angle > 90:
            angle = 180 - angle

        # 判断是否在保留区间
        keep = any(low <= angle <= high for (low, high) in keep_ranges)
        if keep:
            filtered.append([x1, y1, x2, y2])
    return filtered


def exclude_lines_by_angle(lines, exclude_ranges, min_length=0):
    """
    按角度区间排除直线

    参数:
        lines: HoughLinesP 或 LSD 输出的直线 [[x1, y1, x2, y2], ...]
        exclude_ranges: 要排除的角度区间 [(min_angle, max_angle), ...] (角度范围 0~180)
        min_length: 直线最小长度 (像素)
    返回:
        过滤后的直线
    """
    filtered = []
    for line in lines:
        if isinstance(line[0], (list, np.ndarray)):  # HoughLinesP 返回 [[[x1,y1,x2,y2]]]
            x1, y1, x2, y2 = line[0]
        else:  # LSD 返回 [x1,y1,x2,y2] 浮点数
            x1, y1, x2, y2 = line

        dx, dy = x2 - x1, y2 - y1
        length = np.hypot(dx, dy)
        if length < min_length:
            continue

        # 计算角度并归一化到 [0,180)
        angle = np.degrees(np.arctan2(dy, dx))
        angle = abs(angle)
        if angle > 90:
            angle = 180 - angle

        # 判断是否落在排除区间
        in_excluded = any(low <= angle <= high for (low, high) in exclude_ranges)
        if not in_excluded:
            filtered.append([x1, y1, x2, y2])
    return filtered


if __name__ == '__main__':

    # 读图 & 灰度
    img = cv2.imread("/Users/<USER>/CodeRepo/08-CADWithAi/paddlex-api/output/0416-shang_page_254_0_res.png")
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    edges = cv2.Canny(gray, 50, 150)

    lines = cv2.HoughLinesP(edges, 1, np.pi / 180, 100, minLineLength=50, maxLineGap=10)

    # 🚫 排除 30°–60° 的直线，其余都保留
    filtered_lines = exclude_lines_by_angle(lines, exclude_ranges=[(45, 135)], min_length=10)

    result = img.copy()
    for x1, y1, x2, y2 in filtered_lines:
        cv2.line(result, (x1, y1), (x2, y2), (0, 0, 255), 2)

    cv2.imwrite("excluded_lines.png", result)

