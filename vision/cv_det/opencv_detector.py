import cv2
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Tuple, Optional, Union
import os


class OpenCVDetector:
    """
    OpenCV图像检测工具类
    提供圆形、矩形、直线检测以及图像预览等功能
    """

    def __init__(self, image_path: Optional[str] = None, image: Optional[np.ndarray] = None):
        """
        初始化检测器

        Args:
            image_path: 图片路径
            image: 图片数组，如果提供则优先使用
        """
        if image is not None:
            self.original_image = image.copy()
        elif image_path and os.path.exists(image_path):
            self.original_image = cv2.imread(image_path)
        else:
            self.original_image = None

        self.processed_image = None
        self.gray_image = None

        if self.original_image is not None:
            self.gray_image = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2GRAY)
            self.processed_image = self.original_image.copy()

    def load_image(self, image_path: str) -> bool:
        """
        加载新图片

        Args:
            image_path: 图片路径

        Returns:
            bool: 是否加载成功
        """
        if os.path.exists(image_path):
            self.original_image = cv2.imread(image_path)
            if self.original_image is not None:
                self.gray_image = cv2.cvtColor(self.original_image, cv2.COLOR_BGR2GRAY)
                self.processed_image = self.original_image.copy()
                return True
        return False

    def detect_circles(self,
                       dp: float = 1,
                       min_dist: int = 20,
                       param1: int = 50,
                       param2: int = 30,
                       min_radius: int = 0,
                       max_radius: int = 0,
                       draw_result: bool = True) -> List[Tuple[int, int, int]]:
        """
        检测圆形

        Args:
            dp: 累加器分辨率与图像分辨率的反比
            min_dist: 检测到的圆心之间的最小距离
            param1: Canny边缘检测的高阈值
            param2: 累加器阈值
            min_radius: 最小半径
            max_radius: 最大半径
            draw_result: 是否在图像上绘制结果

        Returns:
            List[Tuple[int, int, int]]: 检测到的圆形列表 (x, y, radius)
        """
        if self.gray_image is None:
            return []

        circles = cv2.HoughCircles(
            self.gray_image,
            cv2.HOUGH_GRADIENT,
            dp=dp,
            minDist=min_dist,
            param1=param1,
            param2=param2,
            minRadius=min_radius,
            maxRadius=max_radius
        )

        detected_circles = []
        if circles is not None:
            circles = np.round(circles[0, :]).astype("int")
            for (x, y, r) in circles:
                detected_circles.append((x, y, r))
                if draw_result:
                    cv2.circle(self.processed_image, (x, y), r, (0, 255, 0), 2)
                    cv2.circle(self.processed_image, (x, y), 2, (0, 0, 255), 3)

        return detected_circles

    def detect_rectangles(self,
                          min_area: int = 1000,
                          approx_epsilon: float = 0.02,
                          draw_result: bool = True) -> List[np.ndarray]:
        """
        检测矩形

        Args:
            min_area: 最小面积
            approx_epsilon: 轮廓近似精度
            draw_result: 是否在图像上绘制结果

        Returns:
            List[np.ndarray]: 检测到的矩形轮廓列表
        """
        if self.gray_image is None:
            return []

        # 边缘检测
        edges = cv2.Canny(self.gray_image, 50, 150)

        # 查找轮廓
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        rectangles = []
        for contour in contours:
            # 计算轮廓面积
            area = cv2.contourArea(contour)
            if area < min_area:
                continue

            # 轮廓近似
            perimeter = cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, approx_epsilon * perimeter, True)

            # 检查是否为矩形（4个顶点）
            if len(approx) == 4:
                rectangles.append(approx)
                if draw_result:
                    cv2.drawContours(self.processed_image, [approx], -1, (255, 0, 0), 2)

        return rectangles

    def detect_lines(self,
                     rho: int = 1,
                     theta: float = np.pi / 180,
                     threshold: int = 100,
                     min_line_length: int = 50,
                     max_line_gap: int = 10,
                     draw_result: bool = True) -> List[Tuple[int, int, int, int]]:
        """
        检测直线

        Args:
            rho: 距离分辨率
            theta: 角度分辨率
            threshold: 累加器阈值
            min_line_length: 最小线段长度
            max_line_gap: 最大线段间隙
            draw_result: 是否在图像上绘制结果

        Returns:
            List[Tuple[int, int, int, int]]: 检测到的直线列表 (x1, y1, x2, y2)
        """
        if self.gray_image is None:
            return []

        # 边缘检测
        edges = cv2.Canny(self.gray_image, 50, 150)

        # 霍夫直线检测
        lines = cv2.HoughLinesP(
            edges,
            rho,
            theta,
            threshold,
            minLineLength=min_line_length,
            maxLineGap=max_line_gap
        )

        detected_lines = []
        if lines is not None:
            for line in lines:
                x1, y1, x2, y2 = line[0]
                detected_lines.append((x1, y1, x2, y2))
                if draw_result:
                    cv2.line(self.processed_image, (x1, y1), (x2, y2), (0, 255, 255), 2)

        return detected_lines

    def detect_horizontal_lines(self,
                                angle_tolerance: float = 0,
                                min_line_length: int = 50,
                                draw_result: bool = True) -> List[Tuple[int, int, int, int]]:
        """
        检测水平线

        Args:
            angle_tolerance: 角度容差（度）
            min_line_length: 最小线段长度
            draw_result: 是否在图像上绘制结果

        Returns:
            List[Tuple[int, int, int, int]]: 检测到的水平线列表
        """
        all_lines = self.detect_lines(min_line_length=min_line_length, draw_result=False)

        horizontal_lines = []
        for x1, y1, x2, y2 in all_lines:
            # 计算角度
            angle = np.abs(np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi)

            # 检查是否为水平线
            if angle <= angle_tolerance or angle >= (180 - angle_tolerance):
                horizontal_lines.append((x1, y1, x2, y2))
                if draw_result:
                    cv2.line(self.processed_image, (x1, y1), (x2, y2), (255, 255, 0), 2)

        return horizontal_lines

    def detect_vertical_lines(self,
                              angle_tolerance: float = 0,
                              min_line_length: int = 50,
                              draw_result: bool = True) -> List[Tuple[int, int, int, int]]:
        """
        检测垂直线

        Args:
            angle_tolerance: 角度容差（度）
            min_line_length: 最小线段长度
            draw_result: 是否在图像上绘制结果

        Returns:
            List[Tuple[int, int, int, int]]: 检测到的垂直线列表
        """
        all_lines = self.detect_lines(min_line_length=min_line_length, draw_result=False)

        vertical_lines = []
        for x1, y1, x2, y2 in all_lines:
            # 计算角度
            angle = np.abs(np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi)

            # 检查是否为垂直线
            if 90 - angle_tolerance <= angle <= 90 + angle_tolerance:
                vertical_lines.append((x1, y1, x2, y2))
                if draw_result:
                    cv2.line(self.processed_image, (x1, y1), (x2, y2), (255, 0, 255), 2)

        return vertical_lines

    def reset_processed_image(self):
        """重置处理后的图像为原始图像"""
        if self.original_image is not None:
            self.processed_image = self.original_image.copy()

    def get_image_info(self) -> dict:
        """
        获取图像信息

        Returns:
            dict: 图像信息字典
        """
        if self.original_image is None:
            return {}

        height, width = self.original_image.shape[:2]
        channels = self.original_image.shape[2] if len(self.original_image.shape) > 2 else 1

        return {
            'width': width,
            'height': height,
            'channels': channels,
            'dtype': str(self.original_image.dtype),
            'size': self.original_image.size
        }

    @staticmethod
    def preview_image(image: Union[str, np.ndarray],
                      title: str = "Image Preview",
                      figsize: Tuple[int, int] = (10, 8),
                      cmap: Optional[str] = None) -> None:
        """
        静态方法：预览图片

        Args:
            image: 图片路径或图片数组
            title: 标题
            figsize: 图片大小
            cmap: 颜色映射（用于灰度图）
        """
        if isinstance(image, str):
            if not os.path.exists(image):
                print(f"图片文件不存在: {image}")
                return
            img = cv2.imread(image)
        else:
            img = image.copy()

        if img is None:
            print("无法加载图片")
            return

        # 转换颜色空间（OpenCV使用BGR，matplotlib使用RGB）
        if len(img.shape) == 3:
            img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        else:
            img_rgb = img
            if cmap is None:
                cmap = 'gray'

        plt.figure(figsize=figsize)
        plt.imshow(img_rgb, cmap=cmap)
        plt.title(title)
        plt.axis('off')
        plt.tight_layout()
        plt.show()

    @staticmethod
    def compare_images(*images: Union[str, np.ndarray],
                       titles: Optional[List[str]] = None,
                       figsize: Tuple[int, int] = (15, 5)) -> None:
        """
        静态方法：对比多张图片

        Args:
            images: 图片路径或图片数组
            titles: 图片标题列表
            figsize: 整体图片大小
        """
        if not images:
            return

        n_images = len(images)
        if titles is None:
            titles = [f"Image {i + 1}" for i in range(n_images)]

        fig, axes = plt.subplots(1, n_images, figsize=figsize)
        if n_images == 1:
            axes = [axes]

        for i, (image, title) in enumerate(zip(images, titles)):
            if isinstance(image, str):
                if not os.path.exists(image):
                    print(f"图片文件不存在: {image}")
                    continue
                img = cv2.imread(image)
            else:
                img = image.copy()

            if img is None:
                print(f"无法加载第{i + 1}张图片")
                continue

            # 转换颜色空间
            if len(img.shape) == 3:
                img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                axes[i].imshow(img_rgb)
            else:
                axes[i].imshow(img, cmap='gray')

            axes[i].set_title(title)
            axes[i].axis('off')

        plt.tight_layout()
        plt.show()

    def preview_results(self, figsize: Tuple[int, int] = (15, 5)) -> None:
        """
        预览原图和处理结果

        Args:
            figsize: 图片大小
        """
        if self.original_image is None or self.processed_image is None:
            print("没有可预览的图片")
            return

        self.compare_images(
            self.original_image,
            self.processed_image,
            titles=["Original Image", "Processed Image"],
            figsize=figsize
        )

    def save_result(self, output_path: str) -> bool:
        """
        保存处理结果

        Args:
            output_path: 输出路径

        Returns:
            bool: 是否保存成功
        """
        if self.processed_image is None:
            return False

        return cv2.imwrite(output_path, self.processed_image)


# 使用示例
if __name__ == "__main__":
    test_img = cv2.imread("/Users/<USER>/CodeRepo/08-CADWithAi/data/images/drawing_classify/train/表格为主/0416-shang_page_28.png")

    # 预览测试图像
    OpenCVDetector.preview_image(test_img, "Test Image")

    # 创建检测器实例
    detector = OpenCVDetector(image=test_img)

    # 获取图像信息
    info = detector.get_image_info()
    print("图像信息:", info)

    vec_lines = detector.detect_vertical_lines(0)
    hor_lines = detector.detect_horizontal_lines(0)

    detector.preview_results()
