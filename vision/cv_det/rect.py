from typing import List
import cv2
import numpy as np
from vision.core.data_types import BoundingBox


def extract_rectangle(image: np.array, min_ratio=0.1, max_ratio=0.8) -> List[BoundingBox]:
    """
    从检测结果中提取矩形区域信息
    """
    # 计算图片像素点数据
    height, width = image.shape[:2]
    area = height * width
    # 计算最大最小区域
    min_area = area * min_ratio
    max_area = area * max_ratio

    # 图像预处理
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    edges = cv2.Canny(blurred, 50, 150)

    # 查找轮廓
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    rectangles: List[BoundingBox] = []

    for cnt in contours:
        # 近似轮廓
        approx = cv2.approxPolyDP(cnt, 0.02 * cv2.arcLength(cnt, True), True)
        area = cv2.contourArea(cnt)

        # 判断是否是矩形：4个点、面积合适
        if len(approx) == 4 and min_area < area < max_area and cv2.isContourConvex(approx):
            x, y, w, h = cv2.boundingRect(approx)
            rectangles.append(BoundingBox(x1=x, y1=y, x2=x + w, y2=y + h))

    return rectangles