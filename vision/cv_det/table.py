import cv2
import numpy as np
import matplotlib.pyplot as plt


def detect_horizontal_lines(image_path, output_path=None):
    """
    检测图片中的所有水平线

    参数:
    image_path: 输入图片路径
    output_path: 输出图片路径（可选）

    返回:
    horizontal_lines: 检测到的水平线列表
    """

    # 读取图片
    img = cv2.imread(image_path)
    if img is None:
        print(f"无法读取图片: {image_path}")
        return None

    # 转换为灰度图
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 应用高斯模糊以减少噪声
    blurred = cv2.G<PERSON><PERSON>Blur(gray, (5, 5), 0)

    # 使用Canny边缘检测
    edges = cv2.Canny(blurred, 50, 150, apertureSize=3)

    # 使用霍夫变换检测直线
    lines = cv2.HoughLinesP(edges, 1, np.pi / 180, threshold=100,
                            minLineLength=50, maxLineGap=10)

    horizontal_lines = []

    if lines is not None:
        # 创建一个副本用于绘制结果
        result_img = img.copy()

        for line in lines:
            x1, y1, x2, y2 = line[0]

            # 计算线条的角度
            angle = np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi

            # 判断是否为水平线（角度接近0度或180度）
            if abs(angle) <= 15 or abs(angle - 180) <= 15 or abs(angle + 180) <= 15:
                horizontal_lines.append((x1, y1, x2, y2))
                # 在图片上绘制水平线（红色）
                cv2.line(result_img, (x1, y1), (x2, y2), (0, 0, 255), 3)

        # 显示结果
        plt.figure(figsize=(15, 10))

        # 原图
        plt.subplot(2, 2, 1)
        plt.imshow(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        plt.title('原始图片')
        plt.axis('off')

        # 边缘检测结果
        plt.subplot(2, 2, 2)
        plt.imshow(edges, cmap='gray')
        plt.title('边缘检测结果')
        plt.axis('off')

        # 检测到的水平线
        plt.subplot(2, 2, 3)
        plt.imshow(cv2.cvtColor(result_img, cv2.COLOR_BGR2RGB))
        plt.title(f'检测到的水平线 (共{len(horizontal_lines)}条)')
        plt.axis('off')

        # 只显示水平线
        line_img = np.zeros_like(img)
        for x1, y1, x2, y2 in horizontal_lines:
            cv2.line(line_img, (x1, y1), (x2, y2), (0, 255, 0), 3)

        plt.subplot(2, 2, 4)
        plt.imshow(cv2.cvtColor(line_img, cv2.COLOR_BGR2RGB))
        plt.title('仅水平线')
        plt.axis('off')

        plt.tight_layout()
        plt.show()

        # 保存结果图片
        if output_path:
            cv2.imwrite(output_path, result_img)
            print(f"结果已保存到: {output_path}")

    else:
        print("未检测到任何直线")

    return horizontal_lines


def detect_horizontal_lines_advanced(image_path, output_path=None):
    """
    高级水平线检测方法，使用形态学操作
    """

    # 读取图片
    img = cv2.imread(image_path)
    if img is None:
        print(f"无法读取图片: {image_path}")
        return None

    # 转换为灰度图
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 二值化
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

    # 创建水平结构元素
    horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))

    # 形态学操作检测水平线
    horizontal_lines_img = cv2.morphologyEx(binary, cv2.MORPH_OPEN, horizontal_kernel)

    # 查找轮廓
    contours, _ = cv2.findContours(horizontal_lines_img, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 在原图上绘制检测到的水平线
    result_img = img.copy()
    horizontal_lines = []

    for contour in contours:
        # 获取边界框
        x, y, w, h = cv2.boundingRect(contour)
        # 过滤太小的线条
        if w > 30 and h < 10:  # 长度大于30像素，高度小于10像素
            cv2.rectangle(result_img, (x, y), (x + w, y + h), (0, 255, 0), 2)
            horizontal_lines.append((x, y, x + w, y + h))

    # 显示结果
    plt.figure(figsize=(12, 8))

    plt.subplot(2, 2, 1)
    plt.imshow(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
    plt.title('原始图片')
    plt.axis('off')

    plt.subplot(2, 2, 2)
    plt.imshow(binary, cmap='gray')
    plt.title('二值化图片')
    plt.axis('off')

    plt.subplot(2, 2, 3)
    plt.imshow(horizontal_lines_img, cmap='gray')
    plt.title('检测到的水平线')
    plt.axis('off')

    plt.subplot(2, 2, 4)
    plt.imshow(cv2.cvtColor(result_img, cv2.COLOR_BGR2RGB))
    plt.title(f'标记结果 (共{len(horizontal_lines)}条线)')
    plt.axis('off')

    plt.tight_layout()
    plt.show()

    if output_path:
        cv2.imwrite(output_path, result_img)
        print(f"结果已保存到: {output_path}")

    return horizontal_lines


# 使用示例
if __name__ == "__main__":
    # 请替换为你的图片路径
    image_path = "/Users/<USER>/CodeRepo/08-CADWithAi/paddlex-api/output/0416-shang_page_254_0_res.png"
    output_path = "result_with_horizontal_lines.jpg"

    print("方法1: 使用霍夫变换检测水平线")
    lines1 = detect_horizontal_lines(image_path, output_path)
    if lines1:
        print(f"检测到 {len(lines1)} 条水平线")
        for i, (x1, y1, x2, y2) in enumerate(lines1):
            print(f"线条 {i + 1}: ({x1}, {y1}) -> ({x2}, {y2})")

    print("\n" + "=" * 50 + "\n")

    print("方法2: 使用形态学操作检测水平线")
    lines2 = detect_horizontal_lines_advanced(image_path, "result_morphology.jpg")
    if lines2:
        print(f"检测到 {len(lines2)} 条水平线")
        for i, (x, y, x2, y2) in enumerate(lines2):
            print(f"线条 {i + 1}: 位置({x}, {y}), 尺寸({x2 - x} x {y2 - y})")