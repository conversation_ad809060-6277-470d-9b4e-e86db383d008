import cv2
import numpy as np
import os
from collections import defaultdict


class CellDetectorWithMinArea:
    def __init__(self, input_dir, output_dir,
                 min_line_length=30,
                 distance_threshold=5,
                 angle_threshold=15,
                 intersection_tolerance=5,
                 min_cell_size=5,  # 最小单元格尺寸（宽/高）
                 min_area=100,  # 最小单元格面积
                 debug=False):
        """初始化单元格检测器，支持最小面积过滤"""
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.min_line_length = min_line_length
        self.distance_threshold = distance_threshold  # 点合并阈值
        self.angle_threshold = angle_threshold  # 线段角度分类阈值
        self.intersection_tolerance = intersection_tolerance  # 交点检测容差
        self.min_cell_size = min_cell_size  # 最小单元格宽/高
        self.min_area = min_area  # 最小单元格面积
        self.debug = debug  # 调试模式开关

        # 创建LSD线段检测器
        self.lsd = cv2.createLineSegmentDetector(0)

        # 确保输出目录存在
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def detect_lines(self, image):
        """使用LSD算法检测图像中的线段"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        detected_lines, _, _, _ = self.lsd.detect(gray)

        lines = []
        if detected_lines is not None:
            for line in detected_lines:
                x1, y1, x2, y2 = line[0]
                length = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
                if length >= self.min_line_length:
                    lines.append([float(x1), float(y1), float(x2), float(y2)])

        return lines

    def classify_lines(self, lines):
        """将线段分类为水平线和垂直线"""
        horizontal_lines = []
        vertical_lines = []

        for line in lines:
            x1, y1, x2, y2 = line
            dx = abs(x2 - x1)
            dy = abs(y2 - y1)

            if dx < 1e-6:  # 完全垂直
                vertical_lines.append(line)
            elif dy < 1e-6:  # 完全水平
                horizontal_lines.append(line)
            else:
                # 计算线段与水平线的夹角
                angle = np.degrees(np.arctan2(dy, dx))
                if angle < self.angle_threshold:  # 接近水平
                    horizontal_lines.append(line)
                elif angle > 90 - self.angle_threshold:  # 接近垂直
                    vertical_lines.append(line)

        return horizontal_lines, vertical_lines

    def line_intersection(self, line1, line2):
        """计算两条线段的交点"""
        x1, y1, x2, y2 = line1
        x3, y3, x4, y4 = line2

        denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)

        if abs(denom) < 1e-10:  # 平行或重合线段
            return None

        # 计算参数t和u
        t_num = (x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)
        t = t_num / denom

        u_num = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3))
        u = u_num / denom

        # 计算交点坐标
        x = x1 + t * (x2 - x1)
        y = y1 + t * (y2 - y1)

        # 计算线段长度
        line1_length = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
        line2_length = np.sqrt((x4 - x3) ** 2 + (y4 - y3) ** 2)

        # 计算t和u的允许范围（基于线段长度的比例）
        t_tolerance = self.intersection_tolerance / line1_length if line1_length > 0 else 0
        u_tolerance = self.intersection_tolerance / line2_length if line2_length > 0 else 0

        # 检查交点是否在线段上（允许一定容差）
        if (-t_tolerance <= t <= 1 + t_tolerance) and (-u_tolerance <= u <= 1 + u_tolerance):
            return (x, y)

        return None

    def point_to_line_distance(self, point, line):
        """计算点到线段的距离"""
        x, y = point
        x1, y1, x2, y2 = line

        line_vec = (x2 - x1, y2 - y1)
        point_vec = (x - x1, y - y1)
        line_len_sq = line_vec[0] ** 2 + line_vec[1] ** 2

        if line_len_sq == 0:  # 线段是一个点
            return np.sqrt((x - x1) ** 2 + (y - y1) ** 2)

        # 计算投影参数t
        t = max(0, min(1, (point_vec[0] * line_vec[0] + point_vec[1] * line_vec[1]) / line_len_sq))
        projection = (x1 + t * line_vec[0], y1 + t * line_vec[1])

        # 计算点到投影点的距离
        return np.sqrt((x - projection[0]) ** 2 + (y - projection[1]) ** 2)

    def calculate_intersections(self, horizontal_lines, vertical_lines):
        """计算水平线和垂直线的所有交点"""
        intersections = []

        for h_line in horizontal_lines:
            for v_line in vertical_lines:
                intersection = self.line_intersection(h_line, v_line)
                if intersection is not None:
                    # 验证交点是否确实在线段上
                    dist_to_h = self.point_to_line_distance(intersection, h_line)
                    dist_to_v = self.point_to_line_distance(intersection, v_line)

                    if dist_to_h <= self.intersection_tolerance and dist_to_v <= self.intersection_tolerance:
                        intersections.append(intersection)

        return intersections

    def merge_close_points(self, points, threshold):
        """合并距离小于阈值的点"""
        if not points:
            return points

        # 四舍五入到整数，简化合并逻辑
        rounded_points = [(round(x), round(y)) for x, y in points]
        unique_points = list(set(rounded_points))  # 去重

        # 按x和y坐标排序
        unique_points.sort()
        return unique_points

    def get_cells(self, intersections, horizontal_lines, vertical_lines):
        """
        单元格检测逻辑：
        当存在(x1,y1)、(x2,y1)、(x1,y2)、(x2,y2)四个交点时（允许坐标微小差异），
        且宽高和面积符合要求，则视为一个单元格
        """
        if not intersections:
            if self.debug:
                print("没有检测到任何交点")
            return []

        # 构建交点查找表，便于快速查询
        point_set = set(intersections)

        # 按x和y坐标分组，便于查找相邻点
        x_groups = defaultdict(list)  # 按x坐标分组的y值列表
        y_groups = defaultdict(list)  # 按y坐标分组的x值列表

        for (x, y) in intersections:
            x_groups[x].append(y)
            y_groups[y].append(x)

        # 对每组进行排序
        for x in x_groups:
            x_groups[x].sort()
        for y in y_groups:
            y_groups[y].sort()

        cells = []

        # 遍历所有交点作为左上角点(x1,y1)
        for (x1, y1) in intersections:
            # 1. 查找同一水平线上的右侧相邻点(x2,y1)
            if y1 not in y_groups:
                continue

            x_candidates = y_groups[y1]
            try:
                idx = x_candidates.index(x1)
                # 只考虑右侧最近的点
                if idx + 1 < len(x_candidates):
                    x2 = x_candidates[idx + 1]
                    if (x2, y1) in point_set:
                        # 2. 查找同一垂直线上的下方相邻点(x1,y2)
                        if x1 not in x_groups:
                            continue

                        y_candidates = x_groups[x1]
                        try:
                            y_idx = y_candidates.index(y1)
                            if y_idx + 1 < len(y_candidates):
                                y2 = y_candidates[y_idx + 1]
                                if (x1, y2) in point_set:
                                    # 3. 检查右下角点(x2,y2)是否存在
                                    if (x2, y2) in point_set:
                                        # 4. 计算宽高和面积
                                        width = x2 - x1
                                        height = y2 - y1
                                        area = width * height

                                        # 5. 双重过滤：尺寸和面积都要符合要求
                                        if (width >= self.min_cell_size and
                                                height >= self.min_cell_size and
                                                area >= self.min_area):
                                            # 添加单元格：左上、右上、左下、右下
                                            cells.append([
                                                x1, y1,  # 左上
                                                x2, y1,  # 右上
                                                x1, y2,  # 左下
                                                x2, y2  # 右下
                                            ])
                        except ValueError:
                            continue
            except ValueError:
                continue

        if self.debug:
            print(f"检测到 {len(cells)} 个符合面积要求的单元格")
        return cells

    def visualize_detection(self, image, horizontal_lines, vertical_lines, intersections, cells=None):
        """可视化线段、交点和单元格检测结果，并将单元格填充为灰色"""
        vis_image = image.copy()

        # 绘制水平线段 (蓝色)
        for line in horizontal_lines:
            x1, y1, x2, y2 = map(int, line)
            cv2.line(vis_image, (x1, y1), (x2, y2), (255, 0, 0), 2)

        # 绘制垂直线段 (绿色)
        for line in vertical_lines:
            x1, y1, x2, y2 = map(int, line)
            cv2.line(vis_image, (x1, y1), (x2, y2), (0, 255, 0), 2)

        # 绘制交点 (红色)
        for (x, y) in intersections:
            cv2.circle(vis_image, (int(x), int(y)), 5, (0, 0, 255), -1)
            if self.debug:
                cv2.putText(vis_image, f"({x},{y})", (x + 5, y),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)

        # 绘制并填充单元格为灰色
        if cells:
            # 灰色的BGR值 (OpenCV使用BGR格式)
            gray_color = (128, 128, 128)  # 灰色
            border_color = (0, 255, 255)  # 黄色边框，便于区分

            for cell in cells:
                x1, y1, x2, y2, x3, y3, x4, y4 = cell
                # 定义单元格多边形顶点
                pts = np.array([
                    [x1, y1], [x2, y2], [x4, y4], [x3, y3]
                ], np.int32)
                # 填充单元格为灰色
                cv2.fillPoly(vis_image, [pts], gray_color)
                # 绘制单元格边界，使边缘更清晰
                cv2.polylines(vis_image, [pts], isClosed=True, color=border_color, thickness=2)
                # 调试模式下显示单元格信息
                if self.debug:
                    center_x = int((x1 + x4) / 2)
                    center_y = int((y1 + y4) / 2)
                    cell_idx = cells.index(cell)
                    width = x2 - x1
                    height = y3 - y1
                    area = width * height
                    cv2.putText(vis_image, f"{cell_idx}({int(area)})", (center_x, center_y),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)

        return vis_image

    def process_single_image(self, image_path):
        """处理单张图像，检测线段、交点和单元格"""
        image = cv2.imread(image_path)
        if image is None:
            print(f"无法读取图像: {image_path}")
            return None

        filename = os.path.basename(image_path)
        name, ext = os.path.splitext(filename)

        # 定义输出文件路径
        output_path = os.path.join(self.output_dir, f"{name}_result{ext}")

        print(f"处理图像: {filename}")

        # 1. 检测线段
        lines = self.detect_lines(image)
        print(f"  - 检测到 {len(lines)} 条线段")

        # 2. 分类线段
        horizontal_lines, vertical_lines = self.classify_lines(lines)
        print(f"  - 水平线段: {len(horizontal_lines)} 条")
        print(f"  - 垂直线段: {len(vertical_lines)} 条")

        # 3. 计算交点
        intersections = self.calculate_intersections(horizontal_lines, vertical_lines)
        print(f"  - 原始交点: {len(intersections)} 个")

        # 合并交点（简化为整数坐标去重）
        if intersections:
            merged_intersections = self.merge_close_points(intersections, self.distance_threshold)
            print(f"  - 合并后交点: {len(merged_intersections)} 个")
        else:
            merged_intersections = intersections

        # 4. 检测单元格（应用面积过滤）
        cells = self.get_cells(merged_intersections, horizontal_lines, vertical_lines)
        print(f"  - 检测到符合面积要求的单元格: {len(cells)} 个")

        # 5. 生成可视化结果
        vis_image = self.visualize_detection(image, horizontal_lines, vertical_lines, merged_intersections, cells)
        cv2.imwrite(output_path, vis_image)
        print(f"  - 检测结果保存至: {output_path}")

        return {
            'filename': filename,
            'output_path': output_path,
            'cells': cells
        }

    def process_batch_images(self):
        """批量处理目录中的所有图像"""
        results = []
        image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif')

        if not os.path.exists(self.input_dir):
            print(f"错误: 输入目录不存在 - {self.input_dir}")
            return results

        image_files = [f for f in os.listdir(self.input_dir)
                       if f.lower().endswith(image_extensions)]

        if not image_files:
            print(f"警告: 在 {self.input_dir} 中未找到任何图像文件")
            return results

        for filename in image_files:
            image_path = os.path.join(self.input_dir, filename)
            result = self.process_single_image(image_path)
            if result:
                results.append(result)
                print()  # 分隔不同图像的输出

        return results


# 使用示例
if __name__ == "__main__":
    # 初始化检测器，根据需要调整参数
    detector = CellDetectorWithMinArea(
        input_dir = r"/Users/<USER>/CodeRepo/08-CADWithAi/data/images/drawing_classify/train/表格为主",  # 输入图像目录
        output_dir = r"output",  # 输出结果目录
        min_line_length=10,  # 最小线段长度
        distance_threshold=5,  # 点合并距离阈值
        angle_threshold=15,  # 线段角度分类阈值
        intersection_tolerance=10,  # 交点检测容差
        min_cell_size=5,  # 最小单元格尺寸
        min_area=80,  # 最小单元格面积，可根据需要调整
        debug=True  # 开启调试模式
    )

    print("开始处理图像...")
    results = detector.process_batch_images()

    # 保存单元格数据
    for result in results:
        if result['cells']:
            base, ext = os.path.splitext(result['output_path'])
            cell_file_path = f"{base}_cells.npy"
            np.save(cell_file_path, np.array(result['cells']))
            print(f"单元格坐标数据保存至: {cell_file_path}")

