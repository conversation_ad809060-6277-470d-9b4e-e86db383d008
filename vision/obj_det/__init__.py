"""
目标检测模块
"""
import os
from loguru import logger
from .preprocessor import DetectionPreprocessor
from .postprocessor import DetectionPostProcessor
from .detector import YOLODetector
from .yolo_holder import YoloModelType, YOLO_MODEL_CONFIG
from ..core.model_manager import ModelManager

__all__ = [
    'DetectionPreprocessor',
    'DetectionPostProcessor',
    'YOLODetector',
    'YOLOModelManager',
    'YoloModelType'
]


class YOLOModelManager(ModelManager):
    """模型管理器"""

    def load_model(self, model_type: YoloModelType, **kwargs) -> YOLODetector:
        """
        加载或获取模型

        Args:
            model_type: 模型类型
            **kwargs: YOLODetector 初始化参数

        Returns:
            YOLODetector: 检测器实例
        """
        if model_type.name not in self.models:
            logger.info(f"加载模型: {model_type.name}")
            model_path = YOLO_MODEL_CONFIG[model_type]['weights_path']
            if not os.path.exists(model_path):
                raise ValueError(f"模型文件不存在: {model_path}. 请检查配置或下载模型。")
            self.models[model_type.name] = YOLODetector(
                model_path,
                YOLO_MODEL_CONFIG[model_type]['class_name'],
                **kwargs
            )
        return self.models[model_type.name]
