"""
YOLO目标检测器
"""
import json
import time
import onnxruntime as ort
import cv2
import numpy as np
from pathlib import Path
from typing import Union, List, Optional, Dict, Any, Tuple
from loguru import logger

from vision.core import DetectionResult, BatchDetectionResult, ModelLoadingError
from .preprocessor import DetectionPreprocessor
from .postprocessor import DetectionPostProcessor


class YOLODetector:
    """YOLO目标检测器，支持批量推理"""

    def __init__(self,
                 onnx_model_path: str,
                 class_names: Optional[Union[List[str], Dict[int, str]]] = None,
                 confidence_thres: float = 0.5,
                 iou_thres: float = 0.45,
                 max_batch_size: int = 8, **kwargs):
        """
        初始化YOLO检测器

        Args:
            onnx_model_path: ONNX模型路径
            class_names: 类别名称列表或字典
            confidence_thres: 置信度阈值
            iou_thres: NMS的IoU阈值
            max_batch_size: 最大批量大小
        """
        self.onnx_model_path = onnx_model_path
        self.max_batch_size = max_batch_size

        # 初始化ONNX会话
        self._init_session()

        # 处理类别名称
        self.classes = self._init_class_names(class_names)

        # 初始化组件
        self.preprocessor = DetectionPreprocessor((self.input_height, self.input_width))
        self.postprocessor = DetectionPostProcessor(confidence_thres, iou_thres)

        logger.info(f"YOLO检测器初始化完成 - 输入尺寸: {self.input_width}x{self.input_height}, 最大batch: {self.max_batch_size}")

    def _init_session(self):
        """初始化ONNX推理会话"""
        try:
            providers = ["CUDAExecutionProvider", "CPUExecutionProvider"] if ort.get_device() == "GPU" else ["CPUExecutionProvider"]
            self.session = ort.InferenceSession(self.onnx_model_path, providers=providers)

            # 获取模型输入输出信息
            self.model_inputs = self.session.get_inputs()
            self.input_shape = self.model_inputs[0].shape

            # 检查模型是否支持动态batch
            if self.input_shape[0] == -1 or isinstance(self.input_shape[0], str):
                self.supports_dynamic_batch = True
            else:
                self.supports_dynamic_batch = False
                self.max_batch_size = self.input_shape[0]

            self.input_height, self.input_width = self.input_shape[2], self.input_shape[3]

        except Exception as e:
            raise ModelLoadingError(f"YOLO模型加载失败: {e}")

    def _init_class_names(self, class_names: Optional[Union[List[str], Dict[int, str]]]) -> Dict[int, str]:
        """初始化类别名称"""
        if class_names is None:
            try:
                metadata = self.session.get_modelmeta().custom_metadata_map.get('names')
                if metadata:
                    classes = json.loads(metadata) if isinstance(metadata, str) else metadata
                else:
                    output_shape = self.session.get_outputs()[0].shape
                    num_classes = output_shape[-2] - 4
                    classes = {i: f"class_{i}" for i in range(num_classes)}
            except Exception:
                classes = {i: f"class_{i}" for i in range(80)}
        else:
            if isinstance(class_names, list):
                classes = {i: name for i, name in enumerate(class_names)}
            else:
                classes = class_names

        return classes

    def predict_large_image(self,
                           image_input: Union[str, np.ndarray],
                           tile_size: Tuple[int, int],
                           overlap_ratio: float = 0.2,
                           batch_size: Optional[int] = None,
                           merge_nms_threshold: float = 0.5) -> List[DetectionResult]:
        """
        预测大图片，通过分割成小块进行检测

        Args:
            image_input: 输入图像（路径或numpy数组）
            tile_size: 分割块大小 (width, height)
            overlap_ratio: 重叠比例，用于避免边界目标被切断
            batch_size: 批量处理大小
            merge_nms_threshold: 合并结果时的NMS阈值

        Returns:
            List[DetectionResult]: 整图的检测结果
        """
        start_time = time.time()

        # 加载图像
        if isinstance(image_input, str):
            image = cv2.imread(image_input)
            if image is None:
                raise ValueError(f"无法加载图像: {image_input}")
        elif isinstance(image_input, np.ndarray):
            image = image_input.copy()
        else:
            raise ValueError("不支持的图像输入类型")

        orig_height, orig_width = image.shape[:2]
        tile_width, tile_height = tile_size

        logger.info(f"开始分割大图预测 - 原图尺寸: {orig_width}x{orig_height}, "
                   f"分割块尺寸: {tile_width}x{tile_height}, 重叠比例: {overlap_ratio}")

        # 计算分割参数
        overlap_w = int(tile_width * overlap_ratio)
        overlap_h = int(tile_height * overlap_ratio)
        step_w = tile_width - overlap_w
        step_h = tile_height - overlap_h

        # 生成分割块
        tiles_info = []
        tile_images = []

        for y in range(0, orig_height, step_h):
            for x in range(0, orig_width, step_w):
                # 确定分割块边界
                x1 = x
                y1 = y
                x2 = min(x + tile_width, orig_width)
                y2 = min(y + tile_height, orig_height)

                # 如果分割块太小，跳过
                if (x2 - x1) < tile_width * 0.5 or (y2 - y1) < tile_height * 0.5:
                    continue

                # 提取分割块
                tile = image[y1:y2, x1:x2]

                # 如果分割块尺寸不足，进行填充
                if tile.shape[0] < tile_height or tile.shape[1] < tile_width:
                    padded_tile = np.full((tile_height, tile_width, 3), 114, dtype=np.uint8)
                    padded_tile[:tile.shape[0], :tile.shape[1]] = tile
                    tile = padded_tile

                tiles_info.append({
                    'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2,
                    'tile_index': len(tile_images)
                })
                tile_images.append(tile)

        logger.info(f"生成 {len(tile_images)} 个分割块")

        # 批量检测分割块
        if batch_size is None:
            batch_size = self.max_batch_size

        all_detections = []

        # 保存原来的目标尺寸
        original_target_size = (self.preprocessor.target_height, self.preprocessor.target_width)

        try:
            # 设置分割块尺寸作为目标尺寸
            self.preprocessor.set_target_size((tile_height, tile_width))

            batch_results = self.predict_batch(
                tile_images,
                batch_size=batch_size,
                target_size=(tile_height, tile_width)
            )

            # 收集所有检测结果并转换坐标到原图
            for batch_result in batch_results:
                for tile_idx, tile_detections in enumerate(batch_result.results):
                    if tile_detections:  # 如果有检测结果
                        # 获取对应的分割块信息
                        global_tile_idx = None
                        for detection in tile_detections:
                            if hasattr(detection, 'image_index'):
                                # 根据image_index找到对应的tile_info
                                for tile_info in tiles_info:
                                    if tile_info['tile_index'] == detection.image_index:
                                        global_tile_idx = tile_info['tile_index']
                                        break
                                break

                        if global_tile_idx is not None:
                            tile_info = tiles_info[global_tile_idx]
                        else:
                            # 如果无法找到对应关系，使用tile_idx作为备选
                            if tile_idx < len(tiles_info):
                                tile_info = tiles_info[tile_idx]
                            else:
                                continue

                        # 转换坐标到原图
                        for detection in tile_detections:
                            # 计算在原图中的坐标
                            orig_x1 = detection.x1 + tile_info['x1']
                            orig_y1 = detection.y1 + tile_info['y1']
                            orig_x2 = detection.x2 + tile_info['x1']
                            orig_y2 = detection.y2 + tile_info['y1']

                            # 确保坐标在原图边界内
                            orig_x1 = max(0, min(orig_x1, orig_width))
                            orig_y1 = max(0, min(orig_y1, orig_height))
                            orig_x2 = max(0, min(orig_x2, orig_width))
                            orig_y2 = max(0, min(orig_y2, orig_height))

                            # 创建新的检测结果
                            new_detection = DetectionResult(
                                class_id=detection.class_id,
                                class_name=detection.class_name,
                                confidence=detection.confidence,
                                x1=int(orig_x1),
                                y1=int(orig_y1),
                                x2=int(orig_x2),
                                y2=int(orig_y2),
                                image_index=0  # 重置为原图的索引
                            )
                            all_detections.append(new_detection)

        finally:
            # 恢复原来的目标尺寸
            self.preprocessor.set_target_size(original_target_size)

        # 对合并后的结果进行NMS去重
        if all_detections:
            final_detections = self._merge_overlapping_detections(
                all_detections, merge_nms_threshold
            )
        else:
            final_detections = []

        total_time = time.time() - start_time
        logger.info(f"大图预测完成 - 检测到 {len(final_detections)} 个目标, 耗时: {total_time:.2f}s")

        return final_detections

    def _merge_overlapping_detections(self,
                                    detections: List[DetectionResult],
                                    nms_threshold: float) -> List[DetectionResult]:
        """
        合并重叠的检测结果

        Args:
            detections: 所有检测结果
            nms_threshold: NMS阈值

        Returns:
            List[DetectionResult]: 合并后的检测结果
        """
        if not detections:
            return []

        # 按类别分组
        class_groups = {}
        for detection in detections:
            class_id = detection.class_id
            if class_id not in class_groups:
                class_groups[class_id] = []
            class_groups[class_id].append(detection)

        merged_detections = []

        # 对每个类别分别进行NMS
        for class_id, class_detections in class_groups.items():
            if len(class_detections) == 1:
                merged_detections.extend(class_detections)
                continue

            # 准备NMS输入
            boxes = []
            scores = []
            for detection in class_detections:
                boxes.append([detection.x1, detection.y1,
                             detection.x2 - detection.x1,
                             detection.y2 - detection.y1])
                scores.append(detection.confidence)

            # 应用NMS
            indices = cv2.dnn.NMSBoxes(
                boxes, scores,
                self.postprocessor.confidence_thres,
                nms_threshold
            )

            # 收集保留的检测结果
            if indices is not None:
                if hasattr(indices, 'flatten'):
                    indices = indices.flatten()
                else:
                    indices = [indices] if not isinstance(indices, list) else indices

                for idx in indices:
                    merged_detections.append(class_detections[idx])

        return merged_detections
    
    def predict_batch(self, 
                     image_inputs: List[Union[str, Any]],
                     batch_size: Optional[int] = None,
                     target_size: Tuple[int, int] = None) -> List[BatchDetectionResult]:
        """
        批量目标检测
        
        Args:
            image_inputs: 图像输入列表
            batch_size: 批量大小
            target_size: 目标图像大小
            
        Returns:
            List[BatchDetectionResult]: 批量检测结果
        """
        if not image_inputs:
            return []
        
        if batch_size is None:
            batch_size = min(self.max_batch_size, len(image_inputs))
        else:
            batch_size = min(batch_size, self.max_batch_size, len(image_inputs))
        
        # 设置图片大小
        if target_size:
            self.preprocessor.set_target_size(target_size)
        
        logger.info(f"开始批量检测 {len(image_inputs)} 张图像，batch_size={batch_size}")
        
        # 预处理
        batch_preprocess_results = self.preprocessor.preprocess_batch(
            image_inputs, batch_size=batch_size
        )
        
        batch_results = []
        total_start_time = time.time()
        
        # 批量推理
        for preprocess_result in batch_preprocess_results:
            batch_start_time = time.time()
            
            try:
                if preprocess_result.batch_data.shape[0] == 0:
                    # 空批次
                    batch_result = BatchDetectionResult(
                        results=[],
                        processing_time=time.time() - batch_start_time,
                        batch_size=0,
                        successful_count=0,
                        failed_indices=[]
                    )
                    batch_results.append(batch_result)
                    continue
                
                # 执行推理
                outputs = self.session.run(
                    None,
                    {self.model_inputs[0].name: preprocess_result.batch_data}
                )
                
                # 后处理
                detections_per_image = self.postprocessor.process_batch_outputs(
                    outputs, preprocess_result, self.classes, self.preprocessor
                )
                
                # 统计结果
                successful_count = len([d for d in detections_per_image if d])
                failed_indices = [i for i, d in enumerate(detections_per_image) if not d]
                
                batch_result = BatchDetectionResult(
                    results=detections_per_image,
                    processing_time=time.time() - batch_start_time,
                    batch_size=len(detections_per_image),
                    successful_count=successful_count,
                    failed_indices=failed_indices
                )
                
                batch_results.append(batch_result)
                
            except Exception as e:
                logger.error(f"批量推理失败: {e}")
                batch_result = BatchDetectionResult(
                    results=[],
                    processing_time=time.time() - batch_start_time,
                    batch_size=0,
                    successful_count=0,
                    failed_indices=[]
                )
                batch_results.append(batch_result)
        
        total_time = time.time() - total_start_time
        total_successful = sum(br.successful_count for br in batch_results)
        
        logger.info(f"批量检测完成 - 成功: {total_successful}/{len(image_inputs)}, 耗时: {total_time:.2f}s")
        
        return batch_results
    
    def predict_single(self, 
                      image_input: Union[str, Any],
                      target_size: Tuple[int, int] = None) -> List[DetectionResult]:
        """
        单张图像检测
        
        Args:
            image_input: 单张图像输入
            target_size: 目标图像大小
            
        Returns:
            List[DetectionResult]: 检测结果列表
        """
        batch_results = self.predict_batch([image_input], batch_size=1, target_size=target_size)
        
        if batch_results and batch_results[0].results:
            return batch_results[0].results[0]
        return []
    
    def update_thresholds(self, 
                         confidence_thres: Optional[float] = None,
                         iou_thres: Optional[float] = None):
        """更新阈值参数"""
        self.postprocessor.update_thresholds(confidence_thres, iou_thres)
        logger.info(f"阈值已更新 - 置信度: {self.postprocessor.confidence_thres}, IoU: {self.postprocessor.iou_thres}")