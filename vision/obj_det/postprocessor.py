"""
目标检测后处理器
"""
import cv2
import numpy as np
from typing import List, Dict
from loguru import logger

from vision.core import DetectionResult, BatchPreprocessResult, PostprocessingError


class DetectionPostProcessor:
    """目标检测后处理器"""
    
    def __init__(self, confidence_thres: float = 0.5, iou_thres: float = 0.45):
        """
        初始化后处理器
        
        Args:
            confidence_thres: 置信度阈值
            iou_thres: NMS的IoU阈值
        """
        self.confidence_thres = confidence_thres
        self.iou_thres = iou_thres
    
    def process_batch_outputs(self, 
                             outputs: List[np.ndarray],
                             batch_preprocess_result: BatchPreprocessResult,
                             classes: Dict[int, str],
                             preprocessor) -> List[List[DetectionResult]]:
        """
        处理批量模型输出
        
        Args:
            outputs: 模型输出
            batch_preprocess_result: 批量预处理结果
            classes: 类别映射
            preprocessor: 预处理器（用于坐标转换）
            
        Returns:
            List[List[DetectionResult]]: 每张图片的检测结果列表
        """
        try:
            batch_output = outputs[0]  # shape: (batch_size, num_classes+4, num_detections)
            
            if len(batch_output.shape) == 2:
                batch_output = np.expand_dims(batch_output, axis=0)
            
            batch_size = batch_output.shape[0]
            results_per_image = []
            
            # 处理每张图片
            for img_idx in range(batch_size):
                if img_idx >= len(batch_preprocess_result.image_metas):
                    results_per_image.append([])
                    continue
                
                try:
                    image_meta = batch_preprocess_result.image_metas[img_idx]
                    image_output = batch_output[img_idx]  # (num_classes+4, num_detections)
                    
                    # 转置为 (num_detections, num_classes+4)
                    image_output = image_output.T
                    
                    # 提取边界框和置信度
                    boxes = image_output[:, :4]
                    scores = image_output[:, 4:]
                    
                    # 找到每个检测的最高置信度和对应类别
                    max_scores = np.max(scores, axis=1)
                    class_ids = np.argmax(scores, axis=1)
                    
                    # 过滤低置信度检测
                    valid_indices = max_scores >= self.confidence_thres
                    
                    if not np.any(valid_indices):
                        results_per_image.append([])
                        continue
                    
                    valid_boxes = boxes[valid_indices]
                    valid_scores = max_scores[valid_indices]
                    valid_class_ids = class_ids[valid_indices]
                    
                    # 坐标转换
                    valid_boxes = preprocessor.scale_boxes_to_original(valid_boxes, image_meta)
                    
                    # NMS
                    nms_indices = self._apply_nms(valid_boxes, valid_scores)
                    
                    # 构建检测结果
                    detections = []
                    for idx in nms_indices:
                        x1, y1, x2, y2 = valid_boxes[idx]
                        class_id = int(valid_class_ids[idx])
                        detections.append(DetectionResult(
                            class_id=class_id,
                            class_name=classes.get(class_id, f"class_{class_id}"),
                            confidence=float(valid_scores[idx]),
                            x1=int(x1),
                            y1=int(y1),
                            x2=int(x2),
                            y2=int(y2),
                            image_index=image_meta.global_index
                        ))
                    
                    results_per_image.append(detections)
                    
                except Exception as e:
                    logger.error(f"后处理图像 {img_idx} 失败: {e}")
                    results_per_image.append([])
            
            return results_per_image
            
        except Exception as e:
            raise PostprocessingError(f"批量后处理失败: {e}")
    
    def _apply_nms(self, boxes: np.ndarray, scores: np.ndarray) -> List[int]:
        """应用非极大值抑制"""
        x1, y1, x2, y2 = boxes[:, 0], boxes[:, 1], boxes[:, 2], boxes[:, 3]
        cv_boxes = np.column_stack([x1, y1, x2 - x1, y2 - y1])
        
        indices = cv2.dnn.NMSBoxes(
            cv_boxes.tolist(),
            scores.tolist(),
            self.confidence_thres,
            self.iou_thres
        )
        
        if indices is not None:
            return indices.flatten().tolist() if hasattr(indices, 'flatten') else [indices]
        return []
    
    def update_thresholds(self, 
                         confidence_thres: float = None,
                         iou_thres: float = None):
        """更新阈值参数"""
        if confidence_thres is not None:
            self.confidence_thres = confidence_thres
        if iou_thres is not None:
            self.iou_thres = iou_thres