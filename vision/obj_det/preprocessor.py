"""
目标检测预处理器
"""
import cv2
import numpy as np
from PIL import Image
from typing import List, Union, Optional, Tuple, Dict, Any
from loguru import logger

from vision.core import (
    BatchPreprocessResult, ImageMeta, ImageHandler,
    PreprocessingError
)


class DetectionPreprocessor:
    """目标检测预处理器"""
    
    def __init__(self, target_size: Tuple[int, int]):
        """
        初始化预处理器
        
        Args:
            target_size: 目标图像尺寸 (height, width)
        """
        self.target_height, self.target_width = target_size
    
    def set_target_size(self, target_size: Tuple[int, int]):
        """设置目标尺寸"""
        self.target_height, self.target_width = target_size
    
    def preprocess_batch(self, 
                        image_inputs: List[Union[str, np.ndarray, Image.Image]],
                        batch_size: Optional[int] = None) -> List[BatchPreprocessResult]:
        """
        批量预处理图像
        
        Args:
            image_inputs: 图像输入列表
            batch_size: 批量大小，None表示一次处理所有图像
            
        Returns:
            List[BatchPreprocessResult]: 批量预处理结果列表
        """
        if not image_inputs:
            return []
        
        if batch_size is None:
            batch_size = len(image_inputs)
        
        batch_results = []
        
        for i in range(0, len(image_inputs), batch_size):
            batch_inputs = image_inputs[i:i + batch_size]
            try:
                batch_result = self._process_single_batch(batch_inputs, start_index=i)
                batch_results.append(batch_result)
            except Exception as e:
                logger.error(f"预处理批次 {i//batch_size} 失败: {e}")
                # 创建空的结果
                empty_result = BatchPreprocessResult(
                    batch_data=np.zeros((0, 3, self.target_height, self.target_width), dtype=np.float32),
                    image_metas=[],
                    valid_indices=[]
                )
                batch_results.append(empty_result)
        
        return batch_results
    
    def _process_single_batch(self, 
                             batch_inputs: List[Union[str, np.ndarray, Image.Image]],
                             start_index: int = 0) -> BatchPreprocessResult:
        """处理单个批次"""
        batch_data_list = []
        image_metas = []
        valid_indices = []
        
        for idx, image_input in enumerate(batch_inputs):
            try:
                # 统一输入处理
                bgr_image, source_info = ImageHandler.normalize_input(image_input)
                
                # 获取原始图像尺寸
                img_height, img_width = bgr_image.shape[:2]
                
                # 转换为RGB进行预处理
                rgb_image = cv2.cvtColor(bgr_image, cv2.COLOR_BGR2RGB)
                
                # Letterbox处理
                letterboxed_img, ratio, pad = self._letterbox(rgb_image)
                
                # 归一化和维度调整
                image_data = np.array(letterboxed_img, dtype=np.float32) / 255.0
                image_data = np.transpose(image_data, (2, 0, 1))  # HWC -> CHW
                
                batch_data_list.append(image_data)
                
                # 创建元数据
                meta = ImageMeta(
                    source_type=source_info['type'],
                    source=source_info.get('source', 'unknown'),
                    original_shape=(img_height, img_width),
                    processed_shape=(self.target_height, self.target_width),
                    global_index=start_index + idx,
                    batch_index=len(valid_indices)
                )
                
                # 添加letterbox信息到元数据
                meta.ratio = ratio
                meta.pad = pad
                
                image_metas.append(meta)
                valid_indices.append(start_index + idx)
                
            except Exception as e:
                logger.error(f"预处理图像 {start_index + idx} 失败: {e}")
                continue
        
        # 构建批次数据
        if batch_data_list:
            batch_data = np.stack(batch_data_list, axis=0)
        else:
            batch_data = np.zeros((0, 3, self.target_height, self.target_width), dtype=np.float32)
        
        return BatchPreprocessResult(
            batch_data=batch_data,
            image_metas=image_metas,
            valid_indices=valid_indices
        )
    
    def _letterbox(self, 
                   img: np.ndarray,
                   color: Tuple[int, int, int] = (114, 114, 114),
                   scaleup: bool = True) -> Tuple[np.ndarray, Tuple[float, float], Tuple[float, float]]:
        """Letterbox填充，保持宽高比"""
        shape = img.shape[:2]  # (height, width)
        new_shape = (self.target_height, self.target_width)
        
        # 计算缩放比例
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        if not scaleup:
            r = min(r, 1.0)
        
        # 计算新的未填充尺寸
        new_unpad = (int(round(shape[1] * r)), int(round(shape[0] * r)))
        
        # 计算填充
        dw = new_shape[1] - new_unpad[0]
        dh = new_shape[0] - new_unpad[1]
        
        # 缩放图像
        if shape[::-1] != new_unpad:
            img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)
        
        # 计算填充位置
        top, bottom = dh // 2, dh - dh // 2
        left, right = dw // 2, dw - dw // 2
        
        # 添加边框
        img = cv2.copyMakeBorder(img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)
        
        return img, (r, r), (dw / 2, dh / 2)
    
    def scale_boxes_to_original(self, boxes: np.ndarray, image_meta: ImageMeta) -> np.ndarray:
        """将边界框从模型坐标系转换到原图坐标系"""
        ratio = image_meta.ratio
        dw, dh = image_meta.pad
        original_shape = image_meta.original_shape
        
        # 从中心点格式转换为角点格式
        x_center, y_center, width, height = boxes[:, 0], boxes[:, 1], boxes[:, 2], boxes[:, 3]
        x1 = x_center - width / 2
        y1 = y_center - height / 2
        x2 = x_center + width / 2
        y2 = y_center + height / 2
        
        # 移除填充
        x1 -= dw
        y1 -= dh
        x2 -= dw
        y2 -= dh
        
        # 缩放到原图尺寸
        x1 /= ratio[0]
        y1 /= ratio[1]
        x2 /= ratio[0]
        y2 /= ratio[1]
        
        # 限制在图像边界内
        img_h, img_w = original_shape
        x1 = np.clip(x1, 0, img_w - 1)
        y1 = np.clip(y1, 0, img_h - 1)
        x2 = np.clip(x2, 0, img_w - 1)
        y2 = np.clip(y2, 0, img_h - 1)
        
        return np.column_stack([x1, y1, x2, y2])