from enum import Enum
from typing import Dict, Any
from settings import ROOT


class SectionElemType(str, Enum):
    """剖面元素类型枚举"""
    SECTION = "section"  # 剖面
    SECTION_NUMBER = "section_number"  # 剖面编号
    SCALE = "scale"  # 标尺
    DRILL_HOLE = "drill_hole"  # 钻孔
    SOIL_SAMPLE = "soil_sample"  # 土样
    SOIL_SAMPLE_CIRCLE = "soil_sample_circle"  # 土样点
    SOIL_SAMPLE_TRIANGLE = "soil_sample_triangle"  # 土样三角形
    DEPTH_ELEVATION_PAIR = "depth_elevation_pair"  # 深度高程数据对
    SPACING = "spacing"  # 间距


class PlaneElemType(str, Enum):
    """平面元素类型枚举"""
    COORDINATE = "coordinate"  # 坐标
    COORDINATE_WITH_LINE = "coordinate_with_line"  # 带线的坐标
    DRILL = "drill"  # 钻孔
    DRILL_INFO = "drill_info"  # 钻孔信息
    DRILL_INFO_TYPE1 = "drill_info_type1"  # 钻孔信息类型1
    DRILL_INFO_TYPE2 = "drill_info_type2"  # 钻孔信息类型2
    LITTLE_DRILL = "little_drill"  # 小钻孔


class LegendElemType(str, Enum):
    """图例元素类型枚举"""
    LEGEND = "legend"  # 图例
    LEGEND_IMG = "legend_img"  # 图例图片
    LEGEND_TEXT = "legend_text"  # 图例文本


class XTableElemType(str, Enum):
    """错位表格元素类型枚举"""
    TABLE = "table"
    TABLE_INT = "table_int"
    TABLE_NUMBER = "table_number"
    TABLE_INFO = "table_info"


class YoloModelType(str, Enum):
    """模型类型枚举"""
    SECTION_FULL = "section_full"
    SECTION_PAIR_DETECTION = "section_pair_detection"  # 剖面图高程深度数据对检测
    SECTION_SOIL_SAMPLE_DETECTION = "soil_sample_detection"
    PLANE_DETECTION = "plane_detection"
    LEGEND_DETECTION = "legend_detection"
    XTABLE_DETECTION = "xtable_detection"  # 错位表格检测
    XTABLE_DETAIL_DETECTION = "xtable_detail_detection"  # 错位表格细节检测


YOLO_MODEL_CONFIG: Dict[str, Dict[str, Any]] = {
    YoloModelType.SECTION_FULL: {
        "description": "剖面详细数据识别模型",
        "weights_path": ROOT / "weights/yolo/section_full.onnx",
        "class_name": [
            SectionElemType.SECTION.value,
            SectionElemType.SECTION_NUMBER.value,
            SectionElemType.SCALE.value,
            SectionElemType.DRILL_HOLE.value
        ]
    },
    YoloModelType.SECTION_SOIL_SAMPLE_DETECTION: {
        "description": "取土样检测模型",
        "weights_path": ROOT /"weights/yolo/sample_point_dynamic.onnx",
        "class_name": [
            SectionElemType.SOIL_SAMPLE_CIRCLE.value,
            SectionElemType.SOIL_SAMPLE_TRIANGLE.value
        ]
    },
    YoloModelType.SECTION_PAIR_DETECTION: {
        "description": "剖面图高程深度数据对",
        "weights_path": ROOT / "weights/yolo/pair_896.onnx",
        "class_name": [
            SectionElemType.DEPTH_ELEVATION_PAIR.value,
        ]
    },
    YoloModelType.PLANE_DETECTION: {
        "description": "平面钻口检测模型",
        "weights_path": ROOT /"weights/yolo/plane_detection.onnx",
        "class_name": [
            PlaneElemType.COORDINATE.value,
            PlaneElemType.COORDINATE_WITH_LINE.value,
            PlaneElemType.DRILL.value,
            PlaneElemType.DRILL_INFO.value,
            PlaneElemType.DRILL_INFO_TYPE1.value,
            PlaneElemType.DRILL_INFO_TYPE2.value,
            PlaneElemType.LITTLE_DRILL.value
        ]
    },
    YoloModelType.LEGEND_DETECTION: {
        "description": "图例检测模型",
        "weights_path": ROOT /"weights/yolo/legend_detection.onnx",
        "class_name": [
            LegendElemType.LEGEND.value,
        ]
    },
    YoloModelType.XTABLE_DETECTION: {
        "description": "错位表格检测模型",
        "weights_path": ROOT /"weights/yolo/table_recg.onnx",
        "class_name": [
            XTableElemType.TABLE.value,
        ]
    },
    YoloModelType.XTABLE_DETAIL_DETECTION: {
        "description": "错位表格细节检测模型",
        "weights_path": ROOT /"weights/yolo/cell_recg.onnx",
        "class_name": [
            XTableElemType.TABLE_INT.value,
            XTableElemType.TABLE_NUMBER.value,
            XTableElemType.TABLE_INFO.value,
        ]
    },
}
