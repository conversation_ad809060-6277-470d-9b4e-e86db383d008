import cv2
import numpy as np
from PIL import Image
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Union, Optional

from pydantic import BaseModel, Field, ConfigDict

from vision.core import ImageHandler
from vision.core.data_types import OcrItem, BoundingBox


class OcrResult(BaseModel):
    """OCR识别结果的统一数据结构"""

    items: Optional[List[OcrItem]] = Field(default_factory=list)  # 每行文本的详细信息

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True
    )

    @property
    def texts(self) -> List[str]:
        """返回所有识别文本的列表"""
        return [item.text if item else "" for item in self.items]

    def to_dict(self) -> Dict[str, Any]:
        return {
            'texts': self.texts,
            'items': [item.model_dump() for item in self.items]
        }

    def merge(self, other: 'OcrResult') -> 'OcrResult':
        """合并另一个OcrResult到当前结果中"""
        if not isinstance(other, OcrResult):
            raise ValueError("只能合并OcrResult对象")

        merged_items = self.items + other.items

        return OcrResult(items=merged_items)


class BaseOcrModel(ABC):
    """OCR模型的抽象基类"""

    def __init__(self, **kwargs):
        self.model_name = self.__class__.__name__
        self.config = kwargs
        self._model_loaded = False

    @abstractmethod
    def load_model(self) -> None:
        """加载模型"""
        pass

    @abstractmethod
    def predict(self, image: Union[str, np.ndarray, Image.Image], **kwargs) -> OcrResult:
        """执行OCR识别"""
        pass

    @staticmethod
    def _sort_items(ocr_items: List[OcrItem]) -> List[OcrItem]:
        """对OCR识别结果进行排序，先按y坐标，再按x坐标"""
        return BoundingBox.sort_bboxes(ocr_items)

    def preprocess_image(self, image: Union[str, np.ndarray, Image.Image]) -> np.ndarray:
        """图像预处理，统一转换为numpy array格式"""
        return ImageHandler.normalize_input(image)[0]

    def is_model_loaded(self) -> bool:
        """检查模型是否已加载"""
        return self._model_loaded
