import cv2
import numpy as np
from PIL import Image
from typing import Union

from vision.core.data_types import OcrItem, BoundingBox
from vision.ocr_engine.base import BaseOcrModel, OcrResult


class CnocrModel(BaseOcrModel):
    """CNOCR模型实现"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.ocr = None

    def load_model(self) -> None:
        try:
            from cnocr import CnOcr
            self.ocr = CnOcr()
            self._model_loaded = True
        except ImportError:
            raise ImportError("请安装cnocr: pip install cnocr")

    def predict(self, image: Union[str, np.ndarray, Image.Image], **kwargs) -> OcrResult:
        if self.ocr is None:
            self.load_model()

        img = self.preprocess_image(image)
        # CNOCR接受PIL Image格式
        if isinstance(img, np.ndarray):
            img = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))

        results = self.ocr.ocr(img)

        # 安全检查results，避免numpy数组布尔判断错误
        if results is None or (hasattr(results, '__len__') and len(results) == 0):
            return OcrResult()

        lines = []

        for result in results:
            text = result['text']
            confidence = result.get('score', 1.0)  # CNOCR可能没有置信度
            bbox = result.get('position', [])

            # 转换bbox格式，安全处理numpy数组
            bbox_formatted = []
            if bbox is not None and hasattr(bbox, '__len__') and len(bbox) >= 4:
                try:
                    # 将bbox转换为列表以避免numpy数组问题
                    if hasattr(bbox, 'tolist'):
                        bbox = bbox.tolist()

                    if isinstance(bbox[0], (list, tuple)):  # 四个点的格式
                        x_coords = [point[0] for point in bbox]
                        y_coords = [point[1] for point in bbox]
                        bbox_formatted = [
                            int(min(x_coords)), int(min(y_coords)),
                            int(max(x_coords)), int(max(y_coords))
                        ]
                    else:  # 直接是坐标格式
                        bbox_formatted = [int(coord) for coord in bbox[:4]]
                except (IndexError, TypeError, ValueError):
                    bbox_formatted = []

            lines.append(OcrItem(
                text=text,
                confidence=confidence,
                **BoundingBox.from_array(bbox_formatted).bbox
            ))

        self._sort_items(lines)

        return OcrResult(items=lines)
