import re
import threading
from enum import Enum
from typing import Union, List, Dict, Any, Optional

import numpy as np
from PIL import Image, ImageOps
from loguru import logger
from paddleocr import TextRecognition

from settings import MODEL_CONFIG
from vision.core import ImageHandler
from vision.core.data_types import BoundingBox, OcrItem
from vision.core.utils import calculate_iou, intersection_area
from vision.ocr_engine.base import BaseOcrModel, OcrResult


class OcrModelType(str, Enum):
    PADDLE = 'paddle'
    CNOCR = 'cnocr'
    RAPID = 'rapid'
    TESSERACT = 'tesseract'


class OcrHelper:

    @staticmethod
    def _get_items(items: Union[List['OcrItem'], 'OcrResult', 'OcrItem']) -> List['OcrItem']:
        """
        获取 OcrItem 列表或 OcrResult 对象中的 OcrItem 列表

        Args:
            items: OcrItem、OcrItem 列表或 OcrResult 对象

        Returns:
            List[OcrItem]: OcrItem 列表
        """
        if isinstance(items, OcrItem):
            return [items]
        elif isinstance(items, list):
            if all(isinstance(i, OcrItem) for i in items):
                return items
            else:
                raise TypeError("items list should contain only OcrItem")
        elif isinstance(items, OcrResult):
            return items.items
        else:
            raise TypeError("items should be OcrResult, OcrItem or List[OcrItem]")

    @staticmethod
    def find_inner_item(bbox: BoundingBox, items: Union[List[OcrItem], OcrResult]) -> List[OcrItem]:
        """
        根据给定的边界框过滤识别结果中的文本项

        Args:
            bbox: BoundingBox 对象
            items: OcrItem 列表或 OcrResult 对象

        Returns:
            List[OcrItem]: 位于给定 bbox 内的 OcrItem 列表
        """
        items = OcrHelper._get_items(items)

        filtered_items = []
        target_box = bbox.xyxy

        for ocr_item in items:
            if ocr_item.x1 >= target_box[0] and \
                ocr_item.y1 >= target_box[1] and \
                ocr_item.x2 <= target_box[2] and \
                ocr_item.y2 <= target_box[3]:
                filtered_items.append(ocr_item)

        return filtered_items

    @staticmethod
    def find_best_text(bbox: BoundingBox, texts: OcrResult) -> str:
        """
        在识别结果中找到与给定边界框最匹配的文本

        Args:
            bbox: BoundingBox 对象
            texts: OcrResult 对象，包含多个 OCR 识别项

        Returns:
            str: 与给定 bbox 匹配度最高的文本（基于 IoU），找不到则返回空字符串
        """
        best_item = OcrEngine.find_best_item(bbox, texts)
        if best_item:
            return best_item.text
        return ""


    @staticmethod
    def find_best_item(bbox: BoundingBox, texts: OcrResult) -> OcrItem:
        """
        在识别结果中找到与给定边界框最匹配的文本

        Args:
            bbox: BoundingBox 对象
            texts: OcrResult 对象，包含多个 OCR 识别项

        Returns:
            OcrItem: 与给定 bbox 匹配度最高的 OcrItem（基于 IoU），找不到则返回 None
        """
        best_item = None
        best_iou = 0.0
        target_box = bbox.xyxy

        for item in texts.items:
            iou = calculate_iou(target_box, item.xyxy)
            if iou > best_iou:
                best_iou = iou
                best_item = item

        return best_item

    @staticmethod
    def find_items(bbox: BoundingBox, texts: OcrResult, threshold: float) -> List[OcrItem]:
        """
        在识别结果中找到与给定边界框匹配度超过阈值的文本项

        Args:
            bbox: BoundingBox 对象
            texts: OcrResult 对象，包含多个 OCR 识别项
            threshold: IoU 阈值

        Returns:
            List[OcrItem]: 匹配度超过阈值的 OcrItem 列表
        """
        matched_items = []
        target_box = bbox.xyxy

        for item in texts.items:
            inter_area = intersection_area(target_box, item.xyxy)
            iou = inter_area / item.area
            if item.area > 0 and iou >= threshold:
                matched_items.append((iou, item))

        # 按照 IoU 降序排序
        matched_items.sort(key=lambda x: x[0], reverse=True)
        return [item for _, item in matched_items]

    @staticmethod
    def find_overlap_items(bbox: BoundingBox, texts: Union[OcrResult, List[OcrItem]], threshold: float = 0.8) -> List[OcrItem]:
        """
        找到在给定 bbox 内部重叠比例超过阈值的 OCR 识别项

        Args:
            bbox: BoundingBox 对象
            texts: OcrResult 对象，包含多个 OCR 识别项
            threshold: 面积重叠比例阈值 (0~1)，
                       计算公式： overlap_area / item_area >= threshold

        Returns:
            List[OcrItem]: 重叠比例超过阈值的 OcrItem 列表
        """
        x1, y1, x2, y2 = bbox.xyxy
        matched_items = []

        items = OcrHelper._get_items(texts)

        for item in items:
            ix1, iy1, ix2, iy2 = item.xyxy

            # 计算交集矩形
            inter_x1 = max(x1, ix1)
            inter_y1 = max(y1, iy1)
            inter_x2 = min(x2, ix2)
            inter_y2 = min(y2, iy2)

            if inter_x1 < inter_x2 and inter_y1 < inter_y2:
                inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1)
            else:
                inter_area = 0

            # 占比
            if item.area > 0:
                overlap_ratio = inter_area / item.area
                if overlap_ratio >= threshold:
                    matched_items.append(item)

        return BoundingBox.sort_bboxes(matched_items)

    @staticmethod
    def find_items_in_y_range(
        y_min: float, y_max: float, items: Union[List[OcrItem], OcrResult], threshold: float = 0.0
    ) -> List[OcrItem]:
        """
        在识别结果中找到在给定 y 范围内的文本项

        Args:
            y_min: 最小 y 坐标
            y_max: 最大 y 坐标
            items: List[OcrItem] 或 OcrResult 对象，包含多个 OCR 识别项
            threshold: IoU 阈值

        Returns:
            List[OcrItem]: 在给定 y 范围内并且匹配度超过阈值的 OcrItem 列表
        """
        items = OcrHelper._get_items(items)

        matched_items = []
        for item in items:
            x_max = item.xyxy[2]
            if item.area > 0 and (intersection_area(item.xyxy, [0, y_min, x_max, y_max]) / item.area) >= threshold:
                matched_items.append(item)
        return matched_items

    @staticmethod
    def keep_chinese_alnum(text: str) -> str:
        return ''.join(re.findall(r'[\u4e00-\u9fa5a-zA-Z0-9]+', text))

    @staticmethod
    def clean_ocr_text(text: str, repl=' ') -> str:
        """
        清理 OCR 识别文本中的多余空白符。
        - 去除多余空格、换行符、制表符等
        - 保留必要的词之间的空格
        """
        # 替换所有空白字符为一个空格
        text = re.sub(r'\s+', repl, text)
        # 去除首尾空格
        return text.strip()

    @staticmethod
    def fix_ocr_float_separation(text: str) -> str:
        """
        修复 OCR 导致的小数点和数字被错误分隔的情况
        支持:
            - '4 . 47'   → '4.47'
            - '31. 00'   → '31.00'
            - '4 96'     → '4.96'
            - '12 05'    → '12.05'
        """
        if not text:
            return text

        # 去除首尾空格
        text = text.strip()

        # 修复 "31. 00" / "4 . 47" → "31.00" / "4.47"
        #   \d+ 支持多位整数部分
        text = re.sub(r'(\d+)\s*\.\s*(\d+)', r'\1.\2', text)

        # 修复 "4 96" → "4.96"（仅当第二个是两位数字时）
        # text = re.sub(r'\b(\d{1,2})\s+(\d{2})\b', r'\1.\2', text)

        # 合并多余空格
        text = re.sub(r'\s+', ' ', text)

        return text

    @staticmethod
    def build_ocr_result(detections: List[BoundingBox], texts: Union[OcrResult, List[OcrItem]]):
        """
        将检测结果和 OCR 结果结合，生成最终的 OCR 结果
        :param detections: 检测结果列表
        :param texts: OCR 结果对象
        :return: 结合后的 OCR 结果对象
        """
        if not detections:
            return OcrResult()

        ocr_items = OcrHelper._get_items(texts)

        final_items = []
        for det in detections:
            items = OcrHelper.find_overlap_items(det, ocr_items)
            if items:
                final_items.append(OcrItem(
                    **det.model_dump(),
                    text="\n".join(item.text for item in items),
                ))

        return OcrResult(items=final_items)


class OcrEngine(OcrHelper):
    """OCR引擎主类，统一调用接口（单例模式，每种模型只加载一次）"""

    _instances: Dict[str, "OcrEngine"] = {}
    _lock = threading.Lock()  # 线程安全

    def __new__(cls, model_name: str = 'paddle', **model_config):
        key = cls._make_key(model_name, model_config)

        # 双重检查锁定，确保线程安全
        if key not in cls._instances:
            with cls._lock:
                if key not in cls._instances:
                    instance = super().__new__(cls)
                    instance._initialized = False
                    cls._instances[key] = instance

        return cls._instances[key]

    @classmethod
    def _make_key(cls, model_name: str, model_config: Dict[str, Any]) -> str:
        """生成实例的唯一标识"""
        config_items = tuple(sorted(model_config.items()))
        return f"{model_name}:{hash(config_items)}"

    @classmethod
    def get_instance(cls, model_name: Union[OcrModelType, str] = OcrModelType.RAPID, **model_config) -> "OcrEngine":
        """获取指定模型的单例实例"""
        if isinstance(model_name, OcrModelType):
            model_name = model_name.value
        return cls(model_name, **model_config)

    @classmethod
    def clear_all_instances(cls) -> None:
        """清除所有实例（用于测试或重置）"""
        with cls._lock:
            cls._instances.clear()

    def __init__(self, model_name: Union[OcrModelType, str] = OcrModelType.RAPID, **model_config):
        """
        初始化OCR引擎

        Args:
            model_name: 模型名称 ('paddle', 'cnocr', 'rapid')
            **model_config: 模型特定配置参数
        """
        # 防止重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return

        self.model_name = model_name
        self.model_config = model_config
        self.model = self._create_model()
        self._initialized = True

    def _create_model(self) -> BaseOcrModel:
        """创建OCR模型实例"""
        # 这里需要导入具体的模型类，根据实际情况调整
        model_map = {
            'paddle': 'PaddleOcrModel',
            'cnocr': 'CnocrModel',
            'rapid': 'RapidOcrModel',
            'tesseract': 'TesseractOcrModel'
        }

        if self.model_name not in model_map:
            raise ValueError(f"不支持的模型: {self.model_name}，支持的模型: {list(model_map.keys())}")

        try:
            if self.model_name == 'paddle':
                from vision.ocr_engine.ppocr_model import PaddleOcrModel
                return PaddleOcrModel(**self.model_config)
            elif self.model_name == 'cnocr':
                from vision.ocr_engine.cnocr_model import CnocrModel
                return CnocrModel(**self.model_config)
            elif self.model_name == 'rapid':
                from vision.ocr_engine.rapidocr_model import RapidOcrModel
                return RapidOcrModel(**self.model_config)
            elif self.model_name == 'tesseract':
                from vision.ocr_engine.tesseract_model import TesseractOcrModel
                return TesseractOcrModel(**self.model_config)
            raise ValueError(f"未知模型名称: {self.model_name}")
        except ImportError as e:
            raise ImportError(f"无法导入模型 {self.model_name}: {e}")

    def recognize(self, image: Union[str, np.ndarray, Image.Image]) -> OcrResult:
        """
        执行OCR识别

        Args:
            image: 输入图片，支持文件路径、numpy数组、PIL Image

        Returns:
            OcrResult: 识别结果
        """
        try:
            # 确保模型已加载
            if not self.model.is_model_loaded():
                self.model.load_model()

            return self.model.predict(image)
        except Exception as e:
            logger.exception(f"OCR识别失败: {e}")
            return OcrResult()

    def recognize_with_padding(self, image: Union[str, np.ndarray, Image.Image]) -> OcrResult:
        """
        将图像先补成正方形，再四周 padding 50 白色背景，
        进行 OCR 识别，并将结果坐标恢复到原图坐标系。
        """
        # 1. 转换为 PIL.Image
        if isinstance(image, str):
            img = Image.open(image).convert("RGB")
        elif isinstance(image, np.ndarray):
            img = Image.fromarray(image).convert("RGB")
        elif isinstance(image, Image.Image):
            img = image.convert("RGB")
        else:
            raise ValueError("Unsupported image type")

        w, h = img.size
        max_side = max(w, h)

        # 2. 先补齐成正方形
        delta_w = max_side - w
        delta_h = max_side - h
        pad_left = delta_w // 2
        pad_top = delta_h // 2
        pad_right = delta_w - pad_left
        pad_bottom = delta_h - pad_top

        square_img = ImageOps.expand(img, border=(pad_left, pad_top, pad_right, pad_bottom), fill="white")

        # 3. 再四周额外 padding 50
        outer_padding = 50
        padded_img = ImageOps.expand(square_img, border=outer_padding, fill="white")

        # 4. OCR识别
        result = self.recognize(padded_img)

        # 5. 坐标还原
        restored_items = []
        for item in result.items:
            restored_item = OcrItem(
                text=item.text,
                confidence=item.confidence,
                x1=item.x1 - pad_left - outer_padding,
                y1=item.y1 - pad_top - outer_padding,
                x2=item.x2 - pad_left - outer_padding,
                y2=item.y2 - pad_top - outer_padding,
            )
            restored_items.append(restored_item)

        return OcrResult(items=restored_items)

    def get_model_info(self) -> Dict[str, Any]:
        """获取当前模型信息"""
        return {
            'model_name': self.model_name,
            'model_class': self.model.__class__.__name__,
            'config': self.model_config,
            'is_loaded': self.model.is_model_loaded()
        }

    def __repr__(self) -> str:
        return f"OcrEngine(model_name='{self.model_name}', config={self.model_config})"

    @classmethod
    def list_all_instances(cls) -> Dict[str, Dict[str, Any]]:
        """列出所有已创建的实例信息"""
        return {
            key: {
                'model_name': instance.model_name,
                'config': instance.model_config,
                'is_loaded': instance.model.is_model_loaded() if hasattr(instance, 'model') else False
            }
            for key, instance in cls._instances.items()
        }


def batch_text_rec(
        image_input: Union[str, np.ndarray, Image.Image],
        bboxes: List[BoundingBox],
        model_type: str,
) -> OcrResult:
    config = MODEL_CONFIG['paddle']['text_recognition']
    if model_type in config:
        config = config[model_type]
    else:
        config = {}

    image, _ = ImageHandler.normalize_input(image_input)
    crop_images = ImageHandler.crop_bboxes(image, bboxes)
    ocr_model = TextRecognition(**config)
    result = ocr_model.predict(crop_images, batch_size=49)

    texts: List[str] = []
    items: List[OcrItem] = []
    for rec_res, bbox in zip(result, bboxes):
        text = rec_res['rec_text']
        texts.append(text)
        items.append(OcrItem(
            **bbox.model_dump(),
            text=text,
        ).model_copy(update=dict(
            confidence=rec_res['rec_score']
        )))

    return OcrResult(items=items)



if __name__ == '__main__':
    # ocr_engine = OcrEngine()
    # print(ocr_engine.recognize('E:/CodeData/01-SHProject/section_view/test.png'))

   print(OcrEngine('rapid').recognize('/Users/<USER>/Desktop/test1.png'))

