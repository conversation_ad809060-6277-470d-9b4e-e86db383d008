from typing import List, Tu<PERSON>, TypeVar, Callable

import cv2
import numpy as np

from settings import OUTPUT
from utils.grouper import Grouper
from vision.core.data_types import OcrItem, BoundingBox
from vision.core.utils import bbox_overlap_ratio
from vision.ocr_engine.base import OcrResult
from vision.utils.grid_image_builder import GridLayout, CellPosition, ResizeStrategy, GridImageBuilder

BoundingBox_ = TypeVar('BoundingBox_', bound='BoundingBox')


class OcrResultMapper:
    """OCR结果映射器 - 负责将网格图像的OCR结果映射回原始图像坐标"""

    def map_results_to_original(
            self,
            detections: List[BoundingBox_],
            ocr_result: OcrResult,
            positions: List[CellPosition],
            layout: GridLayout
    ) -> Tuple[List[OcrItem], List[str]]:
        """
        将OCR结果映射回原始图像坐标

        Args:
            detections: 原始检测结果
            ocr_result: 网格图像的OCR结果
            positions: 网格中各cell的位置信息
            layout: 网格布局信息

        Returns:
            <PERSON><PERSON>[映射后的OCR项列表, 文本列表]
        """
        mapped_items = []
        mapped_texts = []

        for pos in positions:
            if pos.index >= len(detections):
                continue

            detection = detections[pos.index]

            # 查找该位置对应的OCR项
            cell_items = self._find_overlapping_ocr_items(ocr_result.items, pos)

            if cell_items:
                cell_items = BoundingBox.sort_bboxes(cell_items)
                # 转换坐标到原始图像空间
                mapped_item = self._transform_coordinates(cell_items[0], pos, detection)
                # 提取文本
                text = "\n".join([item.text for item in cell_items])
                mapped_item.text = text
                mapped_items.append(mapped_item)
                mapped_texts.append(text)
            else:
                # 如果没有找到OCR项，添加空结果保持索引对应
                mapped_item = OcrItem(
                    x1=detection.x1,
                    y1=detection.y1,
                    x2=detection.x2,
                    y2=detection.y2,
                    text="",
                    confidence=0.0
                )
                mapped_items.append(mapped_item)
                mapped_texts.append("")

        return mapped_items, mapped_texts

    def _find_overlapping_ocr_items(
            self,
            ocr_items: List[OcrItem],
            position: CellPosition
    ) -> List[OcrItem]:
        """查找与指定位置重叠的OCR项"""
        if not ocr_items:
            return []

        overlapping = []
        cell_bbox = BoundingBox(
            x1=position.x,
            y1=position.y,
            x2=position.x + position.width,
            y2=position.y + position.height
        )

        for item in ocr_items:
            if self._boxes_overlap(item, cell_bbox, 0.3):
                overlapping.append(item)

        return overlapping

    def _transform_coordinates(
            self,
            ocr_item: OcrItem,
            position: CellPosition,
            detection: BoundingBox_
    ) -> OcrItem:
        """将OCR坐标转换到原始图像坐标系"""
        # 计算相对位置
        rel_x1 = ocr_item.x1 - position.x
        rel_y1 = ocr_item.y1 - position.y
        rel_x2 = ocr_item.x2 - position.x
        rel_y2 = ocr_item.y2 - position.y

        # 计算缩放比例
        original_width = detection.x2 - detection.x1
        original_height = detection.y2 - detection.y1

        scale_x = original_width / position.width
        scale_y = original_height / position.height

        # 转换到原始坐标系
        new_x1 = int(detection.x1 + rel_x1 * scale_x)
        new_y1 = int(detection.y1 + rel_y1 * scale_y)
        new_x2 = int(detection.x1 + rel_x2 * scale_x)
        new_y2 = int(detection.y1 + rel_y2 * scale_y)

        return OcrItem(
            x1=new_x1,
            y1=new_y1,
            x2=new_x2,
            y2=new_y2,
            text=ocr_item.text,
            confidence=ocr_item.confidence
        )

    @staticmethod
    def _boxes_overlap(bbox1: BoundingBox, bbox2: BoundingBox, iou_threshold: float = 0.6) -> bool:
        """检查两个边界框是否重叠"""
        iou = bbox_overlap_ratio(bbox1.xyxy, bbox2.xyxy)
        return iou >= iou_threshold

    @staticmethod
    def _get_text_by_position(texts: List[str], index: int) -> str:
        """根据位置索引获取文本"""
        if isinstance(texts, list) and index < len(texts):
            return texts[index].strip()
        return ""


class BatchOcrProcessor:
    """
    批量OCR处理器 - 整合网格图像构建和结果映射

    职责：
    1. 管理批次处理逻辑
    2. 协调GridImageBuilder和OcrResultMapper
    3. 提供统一的批量OCR接口
    4. 根据边界框尺寸进行智能分组
    """

    def __init__(
            self,
            max_items_per_batch: int = 16,
            grid_builder: GridImageBuilder = None,
            result_mapper: OcrResultMapper = None,
            bbox_grouper: Grouper = None
    ):
        """
        Args:
            max_items_per_batch: 每批次最大处理项数
            grid_builder: 网格图像构建器
            result_mapper: 结果映射器
            bbox_grouper: 边界框分组器
        """
        self.max_items_per_batch = max_items_per_batch
        self.grid_builder = grid_builder or GridImageBuilder()
        self.result_mapper = result_mapper or OcrResultMapper()
        self.bbox_grouper = bbox_grouper or Grouper()

    def process(
            self,
            detection_results: List[BoundingBox_],
            source_image: np.ndarray,
            ocr_function: Callable
    ) -> OcrResult:
        """
        主处理方法

        Args:
            detection_results: 检测结果列表
            source_image: 源图像
            ocr_function: OCR函数

        Returns:
            OcrResult: 聚合的OCR结果
        """
        if not detection_results:
            return OcrResult()

        # 1. 根据边界框尺寸进行分组
        size_groups = self.bbox_grouper.simple_group(detection_results, lambda d: d.area)

        all_ocr_items = [None] * len(detection_results)  # 保持原始顺序
        all_texts = [""] * len(detection_results)

        # 2. 对每个尺寸组进行处理
        for idx, group_indices in enumerate(size_groups):
            group_bboxes = [detection_results[i] for i in group_indices]
            group_items, group_texts = self._process_size_group(
                group_bboxes, group_indices, source_image, ocr_function
            )

            # ----------- Debug Start -----------
            # crop_images = self.grid_builder.extract_regions(group_bboxes, source_image)
            # img_with_ocr, _, _ = self.grid_builder.build_grid_image(crop_images, group_texts)
            # img_without_ocr, _, _ = self.grid_builder.build_grid_image(crop_images)
            # img_merge = cv2.hconcat([img_with_ocr, img_without_ocr])
            # cv2.imwrite(OUTPUT / f"batch_ocr_{idx}.png", img_merge)
            # ----------- Debug End -----------

            # 将结果按原始索引放回对应位置
            for i, (item, text) in enumerate(zip(group_items, group_texts)):
                original_index = group_indices[i]
                all_ocr_items[original_index] = item
                all_texts[original_index] = text

        items = [item for text, item in zip(all_texts, all_ocr_items) if text]

        return OcrResult(items=items)

    def _process_size_group(
            self,
            group_bboxes: List[BoundingBox_],
            group_indices: List[int],
            source_image: np.ndarray,
            ocr_function: Callable
    ) -> Tuple[List[OcrItem], List[str]]:
        """
        处理单个尺寸组

        Args:
            group_bboxes: 当前组的边界框列表
            group_indices: 当前组在原始列表中的索引
            source_image: 源图像
            ocr_function: OCR函数

        Returns:
            Tuple[OCR项列表, 文本列表]
        """
        all_items = []
        all_texts = []

        # 将组内数据按max_items_per_batch进行分批处理
        for i in range(0, len(group_bboxes), self.max_items_per_batch):
            batch_bboxes = group_bboxes[i:i + self.max_items_per_batch]
            batch_indices = group_indices[i:i + self.max_items_per_batch]

            batch_items, batch_texts = self._process_batch(
                batch_bboxes, batch_indices, source_image, ocr_function
            )

            all_items.extend(batch_items)
            all_texts.extend(batch_texts)

        return all_items, all_texts

    def _process_batch(
            self,
            batch: List[BoundingBox_],
            batch_indices: List[int],
            source_image: np.ndarray,
            ocr_function: Callable
    ) -> Tuple[List[OcrItem], List[str]]:
        """
        处理单个批次

        Args:
            batch: 当前批次的检测结果
            batch_indices: 当前批次在原始列表中的索引
            source_image: 源图像
            ocr_function: OCR函数

        Returns:
            Tuple[OCR项列表, 文本列表]
        """
        # 1. 提取区域图像
        region_images = self.grid_builder.extract_regions(batch, source_image)

        # 2. 构建网格图像
        grid_image, layout, positions = self.grid_builder.build_grid_image(region_images)

        # 3. 执行OCR
        ocr_result = ocr_function(grid_image)

        # 4. 映射结果回原始坐标
        mapped_items, mapped_texts = self.result_mapper.map_results_to_original(
            batch, ocr_result, positions, layout
        )

        return mapped_items, mapped_texts

    @classmethod
    def create_with_strategy(
            cls,
            resize_strategy: ResizeStrategy,
            max_items_per_batch: int = 16,
            size_tolerance: float = 0.3,
            **grid_builder_kwargs
    ) -> 'BatchOcrProcessor':
        """
        创建指定策略的批处理器

        Args:
            resize_strategy: 缩放策略
            max_items_per_batch: 每批次最大项数
            size_tolerance: 尺寸分组容忍度
            **grid_builder_kwargs: 传递给GridImageBuilder的额外参数

        Returns:
            BatchOcrProcessor实例
        """
        grid_builder = GridImageBuilder(
            resize_strategy=resize_strategy,
            **grid_builder_kwargs
        )
        bbox_grouper = Grouper(tolerance=size_tolerance)

        return cls(
            max_items_per_batch=max_items_per_batch,
            grid_builder=grid_builder,
            bbox_grouper=bbox_grouper
        )