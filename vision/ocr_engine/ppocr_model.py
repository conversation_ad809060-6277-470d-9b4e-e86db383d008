import cv2
import numpy as np
from PIL import Image
from paddleocr import PaddleOCR
from typing import Union, Optional

from vision.core.data_types import OcrItem, BoundingBox
from vision.ocr_engine.base import BaseOcrModel, OcrResult



class PaddleOcrModel(BaseOcrModel):
    """PaddleOCR模型实现"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.ocr: Optional[PaddleOCR] = None

    def load_model(self) -> None:
        try:
            self.ocr = PaddleOCR(**self.config)
            self._model_loaded = True
        except ImportError:
            raise ImportError("请安装paddleocr")

    def predict(self, image: Union[str, np.ndarray, Image.Image], **kwargs) -> OcrResult:
        use_polys = kwargs.get('use_polys', False)

        if self.ocr is None:
            self.load_model()

        img = self.preprocess_image(image)
        results = self.ocr.predict(img)

        # 安全检查results，避免numpy数组布尔判断错误
        if results is None or (hasattr(results, '__len__') and len(results) == 0) or not results[0]:
            return OcrResult()

        lines = []

        for elem in results:
            # 跳过无效数据，安全处理numpy数组
            if elem is None or (hasattr(elem, '__len__') and len(elem) == 0) or ('rec_texts' not in elem and 'dt_texts' not in elem):
                continue

            # 确定文本和坐标的 key
            texts_key = 'dt_texts' if 'dt_texts' in elem else 'rec_texts'
            boxes_key = 'dt_polys' if use_polys and 'dt_polys' in elem else 'rec_boxes'
            scores_key = 'dt_scores' if 'dt_scores' in elem else 'rec_scores'

            # 如果指定的坐标类型不存在，尝试另一种
            if boxes_key not in elem:
                boxes_key = 'rec_boxes' if boxes_key == 'dt_polys' else 'dt_polys'
                if boxes_key not in elem:
                    continue  # 两种坐标都没有，跳过

            # 遍历文本和坐标
            for text, box, score in zip(elem[texts_key], elem[boxes_key], elem[scores_key]):
                if use_polys and boxes_key == 'dt_polys':
                    # 多边形转矩形框 [x1, y1, x2, y2]
                    xs = [p[0] for p in box]
                    ys = [p[1] for p in box]
                    bbox = [min(xs), min(ys), max(xs), max(ys)]
                else:
                    # 直接使用矩形框
                    bbox = box.copy() if isinstance(box, list) else box.tolist()


                lines.append(OcrItem(
                    text=text,
                    confidence=score,
                    **BoundingBox.from_array(bbox).bbox
                ))
        self._sort_items(lines)

        return OcrResult(items=lines)
