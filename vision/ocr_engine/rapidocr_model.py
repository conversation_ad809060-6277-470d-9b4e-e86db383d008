import cv2
import numpy as np
from PIL import Image
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Union

from vision.core.data_types import OcrItem, BoundingBox
from vision.ocr_engine.base import BaseOcrModel, OcrResult


class RapidOcrModel(BaseOcrModel):
    """RapidOCR模型实现"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.ocr = None

    def load_model(self) -> None:
        try:
            from rapidocr_onnxruntime import RapidOCR
            self.ocr = RapidOCR(
                det_use_cuda=self.config.get('use_gpu', False),
                cls_use_cuda=self.config.get('use_gpu', False),
                rec_use_cuda=self.config.get('use_gpu', False),
                det_model_path=self.config.get('det_model_path'),
                cls_model_path=self.config.get('cls_model_path'),
                rec_model_path=self.config.get('rec_model_path')
            )
            self._model_loaded = True
        except ImportError:
            try:
                from rapidocr_openvino import RapidOCR
                self.ocr = RapidOCR()
                self._model_loaded = True
            except ImportError:
                raise ImportError("请安装rapidocr: uv add rapidocr-onnxruntime")

    def predict(self, image: Union[str, np.ndarray, Image.Image], **kwargs) -> OcrResult:
        if self.ocr is None:
            self.load_model()

        img = self.preprocess_image(image)

        # RapidOCR接受numpy array格式
        if isinstance(img, np.ndarray) and img.shape[2] == 3:
            # BGR转RGB
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        result, elapse = self.ocr(img)

        # 安全检查result，避免numpy数组布尔判断错误
        if result is None or (hasattr(result, '__len__') and len(result) == 0):
            return OcrResult()

        lines = []

        for item in result:
            bbox = item[0]  # 边界框坐标 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
            text = item[1]  # 文本内容
            confidence = item[2]  # 置信度

            # 转换bbox格式为[x1, y1, x2, y2]，安全处理numpy数组
            bbox_formatted = []
            if bbox is not None and hasattr(bbox, '__len__') and len(bbox) >= 4:
                try:
                    # 将bbox转换为列表以避免numpy数组问题
                    if hasattr(bbox, 'tolist'):
                        bbox = bbox.tolist()

                    x_coords = [point[0] for point in bbox]
                    y_coords = [point[1] for point in bbox]
                    bbox_formatted = [
                        int(min(x_coords)), int(min(y_coords)),
                        int(max(x_coords)), int(max(y_coords))
                    ]
                except (IndexError, TypeError, ValueError):
                    bbox_formatted = []

            lines.append(OcrItem(
                text=text,
                confidence=confidence,
                **BoundingBox.from_array(bbox_formatted).bbox
            ))
        self._sort_items(lines)

        return OcrResult(items=lines)