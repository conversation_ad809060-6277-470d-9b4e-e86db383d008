import cv2
import numpy as np
from PIL import Image
from typing import List, Dict, Any, Union, Optional
import re

from vision.core.data_types import OcrItem, BoundingBox
from vision.ocr_engine.base import BaseOcrModel, OcrResult


class TesseractOcrModel(BaseOcrModel):
    """Tesseract OCR模型实现 - 支持多种预定义配置"""

    # 预定义配置
    PRESETS = {
        'roman_with_symbols': {
            'name': '罗马数字+短横线+引号',
            'whitelist': 'IVXLCDM-\' ',
            'psm': 6,
            'min_confidence': 60,
            'description': '识别罗马数字、短横线和引号'
        },
        'numbers_with_dots_brackets': {
            'name': '数字+点+括号',
            'whitelist': '0123456789.() ',
            'psm': 6,
            'min_confidence': 70,
            'description': '识别数字、点和括号'
        },
        'alphanumeric_with_dash': {
            'name': '字母数字+短横线',
            'whitelist': '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz- ',
            'psm': 6,
            'min_confidence': 65,
            'description': '识别数字、字母和短横线'
        },
        'digits_only': {
            'name': '纯数字',
            'whitelist': '0123456789 ',
            'psm': 8,
            'min_confidence': 80,
            'description': '只识别数字'
        },
        'roman_numerals': {
            'name': '罗马数字',
            'whitelist': 'IVXLCDM ',
            'psm': 8,
            'min_confidence': 70,
            'description': '只识别罗马数字'
        },
        'default': {
            'name': '默认配置',
            'whitelist': None,
            'psm': 3,
            'min_confidence': 50,
            'description': '默认OCR配置'
        }
    }

    def __init__(self, preset: str = None, **kwargs):
        """
        初始化TesseractOCR模型

        Args:
            preset: 预定义配置名称，可选值：
                   - 'roman_with_symbols': 罗马数字+短横线+分号
                   - 'numbers_with_dots_brackets': 数字+点+括号
                   - 'alphanumeric_with_dash': 字母数字+短横线
                   - 'digits_only': 纯数字
                   - 'roman_numerals': 罗马数字
                   - 'default': 默认配置
            **kwargs: 其他配置参数，会覆盖预设配置
        """
        super().__init__(**kwargs)
        self.ocr = None

        # 应用预设配置
        if preset and preset in self.PRESETS:
            preset_config = self.PRESETS[preset].copy()
            preset_config.update(kwargs)  # 用户配置覆盖预设
            self.config.update(preset_config)
            self.preset_name = preset
        else:
            self.preset_name = 'custom'

        # Tesseract配置参数
        self.lang = self.config.get('lang', 'eng')
        self.psm = self.config.get('psm', 3)
        self.oem = self.config.get('oem', 3)

        # 字符过滤配置
        self.whitelist = self.config.get('whitelist', None)
        self.blacklist = self.config.get('blacklist', None)

        # 其他配置
        self.min_confidence = self.config.get('min_confidence', 0.0)
        self.preserve_interword_spaces = self.config.get('preserve_interword_spaces', True)

        # 验证配置
        self.validate_text = self.config.get('validate_text', False)

        # 自定义Tesseract配置
        self.custom_config = self._build_tesseract_config()

    @classmethod
    def create_roman_with_symbols(cls, **kwargs):
        """创建罗马数字+短横线+分号的OCR实例"""
        return cls(preset='roman_with_symbols', **kwargs)

    @classmethod
    def create_numbers_with_dots_brackets(cls, **kwargs):
        """创建数字+点+括号的OCR实例"""
        return cls(preset='numbers_with_dots_brackets', **kwargs)

    @classmethod
    def create_alphanumeric_with_dash(cls, **kwargs):
        """创建字母数字+短横线的OCR实例"""
        return cls(preset='alphanumeric_with_dash', **kwargs)

    @classmethod
    def create_digits_only(cls, **kwargs):
        """创建纯数字OCR实例"""
        return cls(preset='digits_only', **kwargs)

    @classmethod
    def create_roman_numerals(cls, **kwargs):
        """创建罗马数字OCR实例"""
        return cls(preset='roman_numerals', **kwargs)

    def _build_tesseract_config(self) -> str:
        """构建Tesseract配置字符串"""
        config_parts = []

        # 基础配置
        config_parts.append(f'--psm {self.psm}')
        config_parts.append(f'--oem {self.oem}')

        # 字符白名单配置
        if self.whitelist:
            escaped_whitelist = self._escape_tesseract_chars(self.whitelist)
            config_parts.append(f'-c tessedit_char_whitelist={escaped_whitelist}')

        # 字符黑名单配置
        if self.blacklist:
            escaped_blacklist = self._escape_tesseract_chars(self.blacklist)
            config_parts.append(f'-c tessedit_char_blacklist={escaped_blacklist}')

        # 如果是专用配置，禁用词典以提高精度
        if self.preset_name != 'default' and self.preset_name != 'custom':
            config_parts.extend([
                '-c tessedit_write_images=false',
                '-c load_system_dawg=false',
                '-c load_freq_dawg=false',
                '-c load_punc_dawg=false',
                '-c load_number_dawg=false',
                '-c load_unambig_dawg=false',
                '-c load_bigram_dawg=false',
                '-c load_fixed_length_dawgs=false',
                '-c tessedit_enable_dict_correction=false',
            ])

        # 其他配置
        if not self.preserve_interword_spaces:
            config_parts.append('-c preserve_interword_spaces=0')

        # 添加额外的自定义配置
        extra_config = self.config.get('extra_config', [])
        if isinstance(extra_config, list):
            config_parts.extend(extra_config)
        elif isinstance(extra_config, str):
            config_parts.append(extra_config)

        return ' '.join(config_parts)

    def _escape_tesseract_chars(self, chars: str) -> str:
        """转义Tesseract特殊字符"""
        special_chars = {
            '\\': '\\\\',
            '"': '\\"',
            "'": "\\'",
            '|': '\\|',
            '&': '\\&',
            ';': '\\;',
            '(': '\\(',
            ')': '\\)',
            '<': '\\<',
            '>': '\\>',
            ' ': '\\ '
        }

        escaped = chars
        for char, escaped_char in special_chars.items():
            escaped = escaped.replace(char, escaped_char)

        return escaped

    def load_model(self) -> None:
        """加载Tesseract模型"""
        try:
            import pytesseract
            self.ocr = pytesseract

            try:
                self.ocr.get_tesseract_version()
                self._model_loaded = True
            except Exception as e:
                raise RuntimeError(f"Tesseract不可用: {e}")

        except ImportError:
            raise ImportError("请安装pytesseract: pip install pytesseract")

    def predict(self, image: Union[str, np.ndarray, Image.Image], **kwargs) -> OcrResult:
        """执行OCR识别"""
        if self.ocr is None:
            self.load_model()

        img = self.preprocess_image(image)

        # 转换为PIL Image格式
        if isinstance(img, np.ndarray):
            if len(img.shape) == 3 and img.shape[2] == 3:
                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            img = Image.fromarray(img)

        # 合并运行时配置
        runtime_config = kwargs.get('config', '')
        full_config = f"{self.custom_config} {runtime_config}".strip()

        try:
            # 获取详细的OCR数据
            ocr_data = self.ocr.image_to_data(
                img,
                lang=self.lang,
                config=full_config,
                output_type=self.ocr.Output.DICT
            )

            return self._parse_tesseract_output(ocr_data)

        except Exception as e:
            print(f"Tesseract OCR识别失败: {e}")
            return OcrResult()

    def _parse_tesseract_output(self, ocr_data: Dict) -> OcrResult:
        """解析Tesseract输出数据"""
        lines = []
        all_text = []

        n_boxes = len(ocr_data['text'])

        for i in range(n_boxes):
            text = ocr_data['text'][i].strip()
            confidence = float(ocr_data['conf'][i])

            # 过滤低置信度和空文本
            if confidence < self.min_confidence or not text:
                continue

            # 应用文本验证
            if self.validate_text and not self._validate_text_for_preset(text):
                continue

            # 应用后处理过滤
            if self._should_filter_text(text):
                continue

            # 获取边界框坐标
            x = ocr_data['left'][i]
            y = ocr_data['top'][i]
            w = ocr_data['width'][i]
            h = ocr_data['height'][i]

            bbox = BoundingBox(
                x1=x,
                y1=y,
                x2=x + w,
                y2=y + h
            )

            ocr_item = OcrItem(
                text=text,
                confidence=confidence / 100.0,
                **bbox.model_dump()
            )

            lines.append(ocr_item)
            all_text.append(text)

        lines = self._sort_items(lines)

        return OcrResult(items=lines)

    def _validate_text_for_preset(self, text: str) -> bool:
        """根据预设配置验证文本"""
        if self.preset_name == 'roman_numerals' or self.preset_name == 'roman_with_symbols':
            return self._is_valid_roman_text(text)
        elif self.preset_name == 'digits_only':
            return text.replace(' ', '').isdigit()
        elif self.preset_name == 'numbers_with_dots_brackets':
            return bool(re.match(r'^[0-9.() ]+$', text))
        elif self.preset_name == 'alphanumeric_with_dash':
            return bool(re.match(r'^[a-zA-Z0-9\- ]+$', text))

        return True

    def _is_valid_roman_text(self, text: str) -> bool:
        """验证罗马数字文本（可能包含其他允许字符）"""
        if self.preset_name == 'roman_with_symbols':
            # 允许罗马数字、短横线、分号
            return bool(re.match(r'^[IVXLCDM\-; ]+$', text.upper()))
        else:
            # 纯罗马数字验证
            return bool(re.match(r'^[IVXLCDM ]+$', text.upper()))

    def _should_filter_text(self, text: str) -> bool:
        """判断文本是否应该被过滤"""
        # 最小长度过滤
        min_text_length = self.config.get('min_text_length', 0)
        if len(text) < min_text_length:
            return True

        # 最大长度过滤
        max_text_length = self.config.get('max_text_length', float('inf'))
        if len(text) > max_text_length:
            return True

        # 自定义正则表达式过滤
        exclude_patterns = self.config.get('exclude_patterns', [])
        for pattern in exclude_patterns:
            if re.search(pattern, text):
                return True

        return False

    def get_available_presets(self) -> Dict[str, Dict[str, Any]]:
        """获取所有可用的预设配置"""
        return self.PRESETS.copy()

    def get_current_config(self) -> Dict[str, Any]:
        """获取当前配置信息"""
        return {
            'preset': self.preset_name,
            'lang': self.lang,
            'psm': self.psm,
            'oem': self.oem,
            'whitelist': self.whitelist,
            'blacklist': self.blacklist,
            'min_confidence': self.min_confidence,
            'tesseract_config': self.custom_config
        }