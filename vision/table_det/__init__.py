"""
目标检测模块
"""
import os
from enum import Enum

from loguru import logger
from paddleocr import LayoutDetection, TableCellsDetection
from paddleocr._models._object_detection import ObjectDetection

from ..core.model_manager import ModelManager


class PPStructureModelType(str, Enum):
    """模型类型枚举"""
    LAYOUT_DETECTION = "layout_detection"
    TABLE_CELL_DET = "table_cell_det"


class PPStructureModelManager(ModelManager):
    """模型管理器"""

    def load_model(self, model_type: PPStructureModelType, **kwargs) -> ObjectDetection:
        """
        加载或获取模型

        Args:
            model_type: 模型类型
            **kwargs: YOLODetector 初始化参数

        Returns:
            YOLODetector: 检测器实例
        """
        if model_type.name not in self.models:
            logger.info(f"加载布局检测模型: {model_type.name}")
            if model_type == PPStructureModelType.LAYOUT_DETECTION:
                self.models[model_type.name] = LayoutDetection(model_name="PP-DocLayout_plus-L")
            elif model_type == PPStructureModelType.TABLE_CELL_DET:
                self.models[model_type.name] = TableCellsDetection(model_name="RT-DETR-L_wired_table_cell_det")
            else:
                raise ValueError(f"不支持的模型类型: {model_type}")
        return self.models[model_type.name]

