import numpy as np
from typing import List, Tuple, Union, Optional
import cv2
import json

from loguru import logger


class CoordinateMapper:
    def __init__(self, original_size: Tuple[int, int]):
        """
        通用坐标映射器

        Args:
            original_size: 原图尺寸 (width, height)
        """
        self.original_width, self.original_height = original_size
        logger.info(f"原图尺寸: {self.original_width}x{self.original_height}")

    def map_from_crop_region(self,
                             coordinates: List[List[Tuple[int, int]]],
                             crop_region: Tuple[int, int, int, int],
                             cropped_size: Optional[Tuple[int, int]] = None,
                             show_info: bool = False) -> List[List[Tuple[int, int]]]:
        """
        将裁剪区域中的坐标映射回原图坐标

        Args:
            coordinates: 坐标列表，每个元素是一个坐标点列表 [[(x1,y1), (x2,y2), (x3,y3), (x4,y4)], ...]
            crop_region: 裁剪区域 (x1, y1, x2, y2)
            cropped_size: 裁剪后图片的实际尺寸 (width, height)，如果为None则根据crop_region计算
            show_info: 显示信息

        Returns:
            映射到原图坐标系的坐标列表
        """
        crop_x1, crop_y1, crop_x2, crop_y2 = crop_region
        crop_width = crop_x2 - crop_x1
        crop_height = crop_y2 - crop_y1

        if cropped_size:
            actual_width, actual_height = cropped_size
            # 计算缩放比例
            scale_x = crop_width / actual_width
            scale_y = crop_height / actual_height
        else:
            scale_x = scale_y = 1.0
            actual_width, actual_height = crop_width, crop_height

        if show_info:
            logger.info(f"裁剪区域: ({crop_x1}, {crop_y1}) -> ({crop_x2}, {crop_y2})")
            logger.info(f"理论尺寸: {crop_width}x{crop_height}")
            logger.info(f"实际尺寸: {actual_width}x{actual_height}")
            logger.info(f"缩放比例: {scale_x:.3f}x{scale_y:.3f}")

        mapped_coordinates = []

        for box_points in coordinates:
            mapped_box = []
            for x, y in box_points:
                # 先根据缩放比例调整，再加上偏移量
                mapped_x = int(x * scale_x + crop_x1)
                mapped_y = int(y * scale_y + crop_y1)
                mapped_box.append((mapped_x, mapped_y))
            mapped_coordinates.append(mapped_box)

        return mapped_coordinates

    def map_from_scaled_image(self,
                              coordinates: List[List[Tuple[int, int]]],
                              scaled_size: Tuple[int, int]) -> List[List[Tuple[int, int]]]:
        """
        将缩放后图片中的坐标映射回原图坐标

        Args:
            coordinates: 坐标列表
            scaled_size: 缩放后的图片尺寸 (width, height)

        Returns:
            映射到原图坐标系的坐标列表
        """
        scaled_width, scaled_height = scaled_size

        # 计算缩放比例
        scale_x = self.original_width / scaled_width
        scale_y = self.original_height / scaled_height

        logger.info(f"原图尺寸: {self.original_width}x{self.original_height}")
        logger.info(f"缩放后尺寸: {scaled_width}x{scaled_height}")
        logger.info(f"恢复比例: {scale_x:.3f}x{scale_y:.3f}")

        mapped_coordinates = []

        for box_points in coordinates:
            mapped_box = []
            for x, y in box_points:
                mapped_x = int(x * scale_x)
                mapped_y = int(y * scale_y)
                mapped_box.append((mapped_x, mapped_y))
            mapped_coordinates.append(mapped_box)

        return mapped_coordinates

    def map_with_transform_matrix(self,
                                  coordinates: List[List[Tuple[int, int]]],
                                  transform_matrix: np.ndarray) -> List[List[Tuple[int, int]]]:
        """
        使用变换矩阵将坐标映射回原图坐标

        Args:
            coordinates: 坐标列表
            transform_matrix: 3x3变换矩阵

        Returns:
            映射到原图坐标系的坐标列表
        """
        logger.info(f"使用变换矩阵进行坐标映射")
        logger.info(f"变换矩阵:\n{transform_matrix}")

        def transform_point(x, y):
            point = np.array([x, y, 1]).reshape(3, 1)
            transformed = transform_matrix @ point
            return int(transformed[0, 0]), int(transformed[1, 0])

        mapped_coordinates = []

        for box_points in coordinates:
            mapped_box = []
            for x, y in box_points:
                mapped_x, mapped_y = transform_point(x, y)
                mapped_box.append((mapped_x, mapped_y))
            mapped_coordinates.append(mapped_box)

        return mapped_coordinates

    def batch_map_coordinates(self,
                              coordinate_batches: List[dict]) -> List[List[Tuple[int, int]]]:
        """
        批量处理多个区域的坐标映射

        Args:
            coordinate_batches: 坐标批次列表，每个元素格式：
                               {
                                   'coordinates': [...],  # 坐标列表
                                   'crop_region': (x1, y1, x2, y2),  # 可选
                                   'cropped_size': (width, height),   # 可选
                                   'scaled_size': (width, height),   # 可选
                                   'transform_matrix': np.ndarray     # 可选
                               }

        Returns:
            所有映射后的坐标列表
        """
        all_mapped_coordinates = []

        for i, batch in enumerate(coordinate_batches):
            logger.info(f"\n处理第 {i + 1} 个批次...")

            coordinates = batch['coordinates']
            logger.info(f"输入坐标数量: {len(coordinates)}")

            if 'crop_region' in batch:
                mapped_coords = self.map_from_crop_region(
                    coordinates,
                    batch['crop_region'],
                    batch.get('cropped_size')
                )
            elif 'scaled_size' in batch:
                mapped_coords = self.map_from_scaled_image(
                    coordinates,
                    batch['scaled_size']
                )
            elif 'transform_matrix' in batch:
                mapped_coords = self.map_with_transform_matrix(
                    coordinates,
                    batch['transform_matrix']
                )
            else:
                logger.warning(f"警告: 第 {i + 1} 个批次缺少映射参数，直接返回原坐标")
                mapped_coords = coordinates

            all_mapped_coordinates.extend(mapped_coords)
            logger.info(f"映射了 {len(mapped_coords)} 个坐标框")

        return all_mapped_coordinates

    def validate_coordinates(self,
                             coordinates: List[List[Tuple[int, int]]],
                             min_area: int = 100) -> List[List[Tuple[int, int]]]:
        """
        验证和过滤坐标

        Args:
            coordinates: 坐标列表
            min_area: 最小面积阈值

        Returns:
            过滤后的坐标列表
        """
        valid_coordinates = []

        for i, box_points in enumerate(coordinates):
            if len(box_points) < 3:
                logger.info(f"警告: 坐标框 {i} 点数少于3个，跳过")
                continue

            # 计算面积
            area = self._calculate_polygon_area(box_points)

            # 检查是否在原图范围内
            if not self._is_within_bounds(box_points):
                logger.info(f"警告: 坐标框 {i} 超出原图边界，跳过")
                continue

            # 检查面积是否满足要求
            if area < min_area:
                logger.info(f"警告: 坐标框 {i} 面积 {area} 小于最小阈值 {min_area}，跳过")
                continue

            valid_coordinates.append(box_points)

        logger.info(f"坐标验证: 输入 {len(coordinates)} 个，有效 {len(valid_coordinates)} 个")
        return valid_coordinates

    def _calculate_polygon_area(self, points: List[Tuple[int, int]]) -> float:
        """计算多边形面积"""
        if len(points) < 3:
            return 0

        x = [p[0] for p in points]
        y = [p[1] for p in points]

        # 使用鞋带公式计算面积
        area = 0.5 * abs(sum(x[i] * y[i + 1] - x[i + 1] * y[i] for i in range(-1, len(x) - 1)))
        return area

    def _is_within_bounds(self, points: List[Tuple[int, int]]) -> bool:
        """检查坐标是否在原图范围内"""
        for x, y in points:
            if x < 0 or x >= self.original_width or y < 0 or y >= self.original_height:
                return False
        return True

    def get_bounding_boxes(self,
                           coordinates: List[List[Tuple[int, int]]]) -> List[Tuple[int, int, int, int]]:
        """
        从坐标点列表获取边界框

        Args:
            coordinates: 坐标列表

        Returns:
            边界框列表 [(x1, y1, x2, y2), ...]
        """
        bboxes = []

        for box_points in coordinates:
            if not box_points:
                continue

            xs = [p[0] for p in box_points]
            ys = [p[1] for p in box_points]

            x1, x2 = min(xs), max(xs)
            y1, y2 = min(ys), max(ys)

            bboxes.append((x1, y1, x2, y2))

        return bboxes

    def visualize_coordinates(self,
                              coordinates: List[List[Tuple[int, int]]],
                              image_path: str = None,
                              output_path: str = "coordinates_visualization.png",
                              colors: List[Tuple[int, int, int]] = None) -> np.ndarray:
        """
        可视化坐标

        Args:
            coordinates: 坐标列表
            image_path: 背景图片路径，如果为None则创建空白图片
            output_path: 输出图片路径
            colors: 颜色列表，BGR格式

        Returns:
            可视化图片数组
        """
        if image_path:
            vis_image = cv2.imread(image_path)
            if vis_image is None:
                logger.info(f"警告: 无法加载图片 {image_path}，使用空白背景")
                vis_image = np.ones((self.original_height, self.original_width, 3), dtype=np.uint8) * 255
        else:
            vis_image = np.ones((self.original_height, self.original_width, 3), dtype=np.uint8) * 255

        if colors is None:
            colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255), (0, 255, 255)]

        for i, box_points in enumerate(coordinates):
            color = colors[i % len(colors)]
            thickness = 2

            # 绘制多边形
            if len(box_points) >= 3:
                points = np.array(box_points, dtype=np.int32)
                cv2.polylines(vis_image, [points], True, color, thickness)

                # 绘制编号
                center_x = int(sum(p[0] for p in box_points) / len(box_points))
                center_y = int(sum(p[1] for p in box_points) / len(box_points))
                cv2.putText(vis_image, str(i), (center_x - 10, center_y + 5),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, thickness)

        if output_path:
            cv2.imwrite(output_path, vis_image)
            logger.info(f"坐标可视化已保存到: {output_path}")

        return vis_image

    def save_coordinates_to_json(self,
                                 coordinates: List[List[Tuple[int, int]]],
                                 output_path: str = "coordinates.json",
                                 metadata: dict = None):
        """
        保存坐标到JSON文件

        Args:
            coordinates: 坐标列表
            output_path: 输出JSON文件路径
            metadata: 额外的元数据
        """
        data = {
            'original_size': [self.original_width, self.original_height],
            'total_boxes': len(coordinates),
            'coordinates': coordinates,
            'bounding_boxes': self.get_bounding_boxes(coordinates)
        }

        if metadata:
            data['metadata'] = metadata

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        logger.info(f"坐标数据已保存到: {output_path}")


# 实用工具函数
class CoordinateUtils:
    @staticmethod
    def bbox_to_points(bbox: Tuple[int, int, int, int]) -> List[Tuple[int, int]]:
        """
        将边界框转换为四个角点

        Args:
            bbox: 边界框 (x1, y1, x2, y2)

        Returns:
            四个角点 [(x1,y1), (x2,y1), (x2,y2), (x1,y2)]
        """
        x1, y1, x2, y2 = bbox
        return [(x1, y1), (x2, y1), (x2, y2), (x1, y2)]

    @staticmethod
    def points_to_bbox(points: List[Tuple[int, int]]) -> Tuple[int, int, int, int]:
        """
        将坐标点转换为边界框

        Args:
            points: 坐标点列表

        Returns:
            边界框 (x1, y1, x2, y2)
        """
        if not points:
            return 0, 0, 0, 0

        xs = [p[0] for p in points]
        ys = [p[1] for p in points]
        return min(xs), min(ys), max(xs), max(ys)

    @staticmethod
    def rotate_points(points: List[Tuple[int, int]],
                      angle: float,
                      center: Tuple[int, int]) -> List[Tuple[int, int]]:
        """
        旋转坐标点

        Args:
            points: 坐标点列表
            angle: 旋转角度（弧度）
            center: 旋转中心

        Returns:
            旋转后的坐标点列表
        """
        cx, cy = center
        cos_a = np.cos(angle)
        sin_a = np.sin(angle)

        rotated_points = []
        for x, y in points:
            # 平移到原点
            x_shifted = x - cx
            y_shifted = y - cy

            # 旋转
            x_rotated = x_shifted * cos_a - y_shifted * sin_a
            y_rotated = x_shifted * sin_a + y_shifted * cos_a

            # 平移回去
            x_final = int(x_rotated + cx)
            y_final = int(y_rotated + cy)

            rotated_points.append((x_final, y_final))

        return rotated_points


# 使用示例
def example_usage():
    """使用示例"""

    # 模拟从不同区域识别到的cell boxes坐标
    # 每个坐标框用四个角点表示
    region1_coordinates = [
        [(10, 20), (190, 20), (190, 80), (10, 80)],  # 第一个单元格
        [(200, 20), (390, 20), (390, 80), (200, 80)],  # 第二个单元格
        [(10, 100), (190, 100), (190, 160), (10, 160)]  # 第三个单元格
    ]

    region2_coordinates = [
        [(20, 10), (100, 15), (95, 70), (15, 65)],  # 稍微倾斜的单元格
        [(120, 12), (200, 18), (195, 72), (115, 66)]  # 另一个倾斜的单元格
    ]

    try:
        # 初始化坐标映射器 (原图尺寸: 1200x800)
        mapper = CoordinateMapper(original_size=(1200, 800))

        # 情况1: 从裁剪区域映射
        logger.info("=== 情况1: 从裁剪区域映射 ===")
        crop_region = (100, 100, 500, 300)  # 裁剪区域
        mapped_coords_1 = mapper.map_from_crop_region(
            region1_coordinates, crop_region
        )
        logger.info(f"映射结果: {len(mapped_coords_1)} 个坐标框")

        # 情况2: 从缩放图片映射
        logger.info("\n=== 情况2: 从缩放图片映射 ===")
        scaled_size = (600, 400)  # 缩放后尺寸
        mapped_coords_2 = mapper.map_from_scaled_image(
            region2_coordinates, scaled_size
        )
        logger.info(f"映射结果: {len(mapped_coords_2)} 个坐标框")

        # 情况3: 批量处理
        logger.info("\n=== 情况3: 批量处理 ===")
        coordinate_batches = [
            {
                'coordinates': region1_coordinates,
                'crop_region': (100, 100, 500, 300)
            },
            {
                'coordinates': region2_coordinates,
                'scaled_size': (600, 400)
            }
        ]
        all_mapped_coords = mapper.batch_map_coordinates(coordinate_batches)
        logger.info(f"批量映射结果: {len(all_mapped_coords)} 个坐标框")

        # 验证坐标
        logger.info("\n=== 情况4: 验证坐标 ===")
        valid_coords = mapper.validate_coordinates(all_mapped_coords, min_area=500)

        # 可视化结果
        mapper.visualize_coordinates(valid_coords, output_path="mapped_coordinates.png")

        # 保存结果
        mapper.save_coordinates_to_json(
            valid_coords,
            "mapped_coordinates.json",
            metadata={'regions': 2, 'total_original_boxes': len(region1_coordinates) + len(region2_coordinates)}
        )

        # 获取边界框
        bboxes = mapper.get_bounding_boxes(valid_coords)
        logger.info(f"\n边界框: {bboxes}")

        logger.info(f"\n✅ 坐标映射完成！")
        logger.info(f"总计处理: {len(all_mapped_coords)} 个坐标框")
        logger.info(f"验证通过: {len(valid_coords)} 个坐标框")

    except Exception as e:
        logger.info(f"❌ 错误: {e}")


if __name__ == "__main__":
    example_usage()