import cv2
import numpy as np
from typing import List, Tuple, Optional
import os

from loguru import logger
from sentry_sdk.profiler.continuous_profiler import thread_sleep

from vision.core import ImageHandler


class TableImageCutter:
    def __init__(self, image_path, threshold_ratio: float = 0.9, min_segment_ratio: float = 0.02):
        """
        初始化表格图片切割器

        Args:
            image_path: 图片路径
            threshold_ratio: 线条长度阈值比例，默认0.9（90%）
            min_segment_ratio: 最小片段尺寸比例，默认0.02（2%）
        """
        self.image, _ = ImageHandler.normalize_input(image_path)

        self.height, self.width = self.image.shape[:2]
        self.threshold_ratio = threshold_ratio
        self.min_segment_ratio = min_segment_ratio
        self.horizontal_lines = []
        self.vertical_lines = []

        # 计算最小片段尺寸
        self.min_width = int(self.width * min_segment_ratio)
        self.min_height = int(self.height * min_segment_ratio)

    def set_lines(self, horizontal_lines: List[Tuple[int, int, int, int]],
                  vertical_lines: List[Tuple[int, int, int, int]]):
        """
        设置水平线和垂直线坐标

        Args:
            horizontal_lines: 水平线列表，每个元素为(x1, y1, x2, y2)
            vertical_lines: 垂直线列表，每个元素为(x1, y1, x2, y2)
        """
        self.horizontal_lines = horizontal_lines
        self.vertical_lines = vertical_lines

    def _get_valid_cut_lines(self) -> Tuple[List[int], List[int]]:
        """
        获取有效的切割线（长度达到阈值的线条）

        Returns:
            (valid_horizontal_y, valid_vertical_x): 有效的水平切割线Y坐标和垂直切割线X坐标
        """
        self.horizontal_lines = self._preprocess_lines(self.horizontal_lines, axis='h')
        self.vertical_lines = self._preprocess_lines(self.vertical_lines, axis='v')

        valid_horizontal_y = []
        valid_vertical_x = []

        # 检查水平线
        for x1, y1, x2, y2 in self.horizontal_lines:
            line_length = abs(x2 - x1)
            if line_length >= self.width * 0.3:
                # 取线段中点的Y坐标作为切割线
                cut_y = (y1 + y2) // 2
                if cut_y not in valid_horizontal_y:
                    valid_horizontal_y.append(cut_y)

        # 检查垂直线
        for x1, y1, x2, y2 in self.vertical_lines:
            line_length = abs(y2 - y1)
            if line_length >= self.height * 0.6:
                # 取线段中点的X坐标作为切割线
                cut_x = (x1 + x2) // 2
                if cut_x not in valid_vertical_x:
                    valid_vertical_x.append(cut_x)

        # 排序并去重
        valid_horizontal_y = sorted(list(set(valid_horizontal_y)))
        valid_vertical_x = sorted(list(set(valid_vertical_x)))

        logger.info(f"有效切割线: {len(valid_horizontal_y)} 条水平线, {len(valid_vertical_x)} 条垂直线")

        return valid_horizontal_y, valid_vertical_x

    def _optimize_cut_positions(self, target_width: int, target_height: int) -> Tuple[List[int], List[int]]:
        """
        根据目标尺寸优化切割位置，确保生成的片段尺寸接近目标尺寸

        Args:
            target_width: 目标宽度
            target_height: 目标高度

        Returns:
            (cut_x_positions, cut_y_positions): 优化后的切割位置
        """
        valid_horizontal_y, valid_vertical_x = self._get_valid_cut_lines()

        # 添加边界
        all_y_positions = [0] + valid_horizontal_y + [self.height]
        all_x_positions = [0] + valid_vertical_x + [self.width]

        # 去重并排序
        all_y_positions = sorted(list(set(all_y_positions)))
        all_x_positions = sorted(list(set(all_x_positions)))

        # 选择最优的水平切割位置
        optimal_y_positions = self._select_optimal_positions(
            all_y_positions, target_height, self.height
        )

        # 选择最优的垂直切割位置
        optimal_x_positions = self._select_optimal_positions(
            all_x_positions, target_width, self.width
        )

        logger.info(f"优化后切割位置:")
        logger.info(f"  水平切割点: {optimal_y_positions}")
        logger.info(f"  垂直切割点: {optimal_x_positions}")

        return optimal_x_positions, optimal_y_positions

    def _select_optimal_positions(self, positions: List[int], target_size: int, total_size: int) -> List[int]:
        """
        选择最优的切割位置，使生成的片段尺寸尽可能接近目标尺寸

        Args:
            positions: 所有可能的切割位置
            target_size: 目标尺寸
            total_size: 总尺寸

        Returns:
            优化后的切割位置列表
        """
        if len(positions) <= 2:  # 只有起始和结束位置
            return positions

        optimal_positions = [positions[0]]  # 起始位置
        current_pos = positions[0]

        for i in range(1, len(positions) - 1):
            next_pos = positions[i]
            segment_size = next_pos - current_pos

            # 如果当前片段大小接近目标大小，或者下一个片段会过大，则在此处切割
            if (segment_size >= target_size * 0.8 and segment_size <= target_size * 1.5) or \
                    (i + 1 < len(positions) and positions[i + 1] - current_pos > target_size * 2):
                optimal_positions.append(next_pos)
                current_pos = next_pos

        optimal_positions.append(positions[-1])  # 结束位置

        return optimal_positions

    def _filter_small_segments(self, segments: List[Tuple[int, int, int, int]]) -> List[Tuple[int, int, int, int]]:
        """
        过滤掉过小的片段

        Args:
            segments: 片段列表，每个元素为(x1, y1, x2, y2)

        Returns:
            过滤后的片段列表
        """
        filtered_segments = []

        for x1, y1, x2, y2 in segments:
            width = x2 - x1
            height = y2 - y1

            # 检查是否满足最小尺寸要求
            if width >= self.min_width and height >= self.min_height:
                filtered_segments.append((x1, y1, x2, y2))
            else:
                logger.info(f"过滤小片段: 尺寸 {width}x{height} < 最小要求 {self.min_width}x{self.min_height}")

        logger.info(f"过滤前: {len(segments)} 个片段, 过滤后: {len(filtered_segments)} 个片段")
        return filtered_segments

    def cut_image(
        self,
        target_width: int,
        target_height: int,
        output_dir: str = None,
        show_info: bool = False
    ) -> Tuple[List[np.ndarray], List[Tuple[int, int, int, int]]]:
        """
        切割图片

        Args:
            target_width: 目标宽度
            target_height: 目标高度
            output_dir: 输出目录
            show_info: 是否显示切割信息

        Returns:
            切割后的图片文件路径列表
        """

        logger.info(f"目标尺寸: {target_width}x{target_height}")

        # 获取优化后的切割位置
        cut_x_positions, cut_y_positions = self._optimize_cut_positions(target_width, target_height)

        # 生成所有可能的片段
        all_segments = []
        for i in range(len(cut_y_positions) - 1):
            for j in range(len(cut_x_positions) - 1):
                y1, y2 = cut_y_positions[i], cut_y_positions[i + 1]
                x1, x2 = cut_x_positions[j], cut_x_positions[j + 1]
                all_segments.append((x1, y1, x2, y2))

        # 过滤小片段
        valid_segments = self._filter_small_segments(all_segments)

        crop_images = [self.image[y1:y2, x1:x2] for (x1, y1, x2, y2) in valid_segments]

        if output_dir:

            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 保存有效片段
            for idx, cropped_image in enumerate(crop_images):
                crop_height, crop_width = cropped_image.shape[:2]

                if crop_width > 0 and crop_height > 0:
                    output_filename = f"segment_{idx:03d}_{crop_width}x{crop_height}.png"
                    output_path = os.path.join(output_dir, output_filename)
                    cv2.imwrite(output_path, cropped_image)

                    # 计算与目标尺寸的差异
                    if show_info:
                        width_diff = abs(crop_width - target_width)
                        height_diff = abs(crop_height - target_height)
                        width_ratio = crop_width / target_width
                        height_ratio = crop_height / target_height

                        logger.info(f"保存片段 {idx:03d}: {output_filename}")
                        logger.info(f"  实际尺寸: {crop_width}x{crop_height}")
                        logger.info(f"  尺寸比例: {width_ratio:.2f}x{height_ratio:.2f}")
                        logger.info(f"  尺寸差异: {width_diff}x{height_diff}")

        return crop_images, valid_segments

    def get_size_statistics(self, target_width: int, target_height: int) -> dict:
        """
        获取切割后片段的尺寸统计信息

        Args:
            target_width: 目标宽度
            target_height: 目标高度

        Returns:
            统计信息字典
        """
        cut_x_positions, cut_y_positions = self._optimize_cut_positions(target_width, target_height)

        # 生成所有可能的片段
        all_segments = []
        for i in range(len(cut_y_positions) - 1):
            for j in range(len(cut_x_positions) - 1):
                y1, y2 = cut_y_positions[i], cut_y_positions[i + 1]
                x1, x2 = cut_x_positions[j], cut_x_positions[j + 1]
                all_segments.append((x1, y1, x2, y2))

        # 过滤小片段
        valid_segments = self._filter_small_segments(all_segments)

        # 计算统计信息
        widths = [x2 - x1 for x1, y1, x2, y2 in valid_segments]
        heights = [y2 - y1 for x1, y1, x2, y2 in valid_segments]

        stats = {
            'total_segments': len(valid_segments),
            'width_stats': {
                'min': min(widths) if widths else 0,
                'max': max(widths) if widths else 0,
                'avg': sum(widths) / len(widths) if widths else 0,
                'target': target_width
            },
            'height_stats': {
                'min': min(heights) if heights else 0,
                'max': max(heights) if heights else 0,
                'avg': sum(heights) / len(heights) if heights else 0,
                'target': target_height
            }
        }

        logger.info(f"图片尺寸: {self.width}x{self.height}")
        logger.info(f"最小片段尺寸: {self.min_width}x{self.min_height}")

        return stats

    def visualize_cut_lines(self, target_width: int, target_height: int, output_path: str = "cut_preview.png"):
        """
        可视化切割线预览

        Args:
            target_width: 目标宽度
            target_height: 目标高度
            output_path: 预览图片保存路径
        """
        preview_image = self.image.copy()
        cut_x_positions, cut_y_positions = self._optimize_cut_positions(target_width, target_height)

        # 绘制垂直切割线（红色）
        for x in cut_x_positions[1:-1]:  # 排除边界
            cv2.line(preview_image, (x, 0), (x, self.height), (0, 0, 255), 2)

        # 绘制水平切割线（蓝色）
        for y in cut_y_positions[1:-1]:  # 排除边界
            cv2.line(preview_image, (0, y), (self.width, y), (255, 0, 0), 2)

        # 绘制片段编号和尺寸信息
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.5
        color = (0, 255, 0)  # 绿色
        thickness = 1

        segment_idx = 0
        for i in range(len(cut_y_positions) - 1):
            for j in range(len(cut_x_positions) - 1):
                y1, y2 = cut_y_positions[i], cut_y_positions[i + 1]
                x1, x2 = cut_x_positions[j], cut_x_positions[j + 1]

                width = x2 - x1
                height = y2 - y1

                # 检查是否为有效片段
                if width >= self.min_width and height >= self.min_height:
                    center_x = (x1 + x2) // 2
                    center_y = (y1 + y2) // 2

                    text = f"{segment_idx}"
                    size_text = f"{width}x{height}"

                    cv2.putText(preview_image, text, (center_x - 10, center_y - 5),
                                font, font_scale, color, thickness)
                    cv2.putText(preview_image, size_text, (center_x - 30, center_y + 15),
                                font, font_scale * 0.7, color, thickness)

                    segment_idx += 1

        cv2.imwrite(output_path, preview_image)

        # 获取统计信息
        stats = self.get_size_statistics(target_width, target_height)

        logger.info(f"切割预览已保存到: {output_path}")
        logger.info(f"统计信息:")
        logger.info(f"  有效片段数量: {stats['total_segments']}")
        logger.info(
            f"  宽度范围: {stats['width_stats']['min']}-{stats['width_stats']['max']} (目标: {target_width}, 平均: {stats['width_stats']['avg']:.1f})")
        logger.info(
            f"  高度范围: {stats['height_stats']['min']}-{stats['height_stats']['max']} (目标: {target_height}, 平均: {stats['height_stats']['avg']:.1f})")

    def _preprocess_lines(self, lines: List[Tuple[int, int, int, int]], axis: str) -> List[Tuple[int, int, int, int]]:
        """
        对线条进行预处理增强：补全缺失、合并近似、去除噪声

        Args:
            lines: 线条坐标列表 (x1, y1, x2, y2)
            axis: 'h' 表示水平线, 'v' 表示垂直线

        Returns:
            增强后的线条列表
        """
        if not lines:
            return []

        processed = []
        lines = sorted(lines, key=lambda l: (l[1], l[0]) if axis == 'h' else (l[0], l[1]))

        merged = []
        tol = 5  # 合并容差
        gap_tol = 12  # 缺口补全阈值

        for line in lines:
            if not merged:
                merged.append(line)
                continue

            last = merged[-1]
            if axis == 'h':  # 水平线处理
                if abs(line[1] - last[1]) <= tol:  # y 坐标接近
                    # 如果线段接近或有小缺口 -> 合并补全
                    if line[0] <= last[2] + gap_tol:
                        new_line = (min(last[0], line[0]), last[1], max(last[2], line[2]), last[3])
                        merged[-1] = new_line
                    else:
                        merged.append(line)
                else:
                    merged.append(line)
            else:  # 垂直线处理
                if abs(line[0] - last[0]) <= tol:  # x 坐标接近
                    if line[1] <= last[3] + gap_tol:
                        new_line = (last[0], min(last[1], line[1]), last[2], max(last[3], line[3]))
                        merged[-1] = new_line
                    else:
                        merged.append(line)
                else:
                    merged.append(line)

        processed.extend(merged)

        logger.info(
            f"线条预处理完成: {len(lines)} -> {len(processed)} 条 "
            f"({'水平' if axis == 'h' else '垂直'}) (已合并 + 补全缺口)"
        )
        return processed


# 使用示例
def example_usage():
    """使用示例"""

    # 示例线条数据
    horizontal_lines = [
        (10, 100, 990, 100),  # 长水平线
        (10, 200, 990, 200),  # 长水平线
        (10, 300, 990, 300),  # 长水平线
        (10, 400, 990, 400),  # 长水平线
        (50, 150, 200, 150),  # 短水平线（会被过滤）
    ]

    vertical_lines = [
        (200, 10, 200, 490),  # 长垂直线
        (400, 10, 400, 490),  # 长垂直线
        (600, 10, 600, 490),  # 长垂直线
        (800, 10, 800, 490),  # 长垂直线
        (300, 50, 300, 150),  # 短垂直线（会被过滤）
    ]

    try:
        # 初始化切割器，设置最小片段比例为2%
        cutter = TableImageCutter("table_image.jpg",
                                  threshold_ratio=0.9,
                                  min_segment_ratio=0.02)

        # 设置线条
        cutter.set_lines(horizontal_lines, vertical_lines)

        # 生成切割预览
        target_w, target_h = 300, 200
        cutter.visualize_cut_lines(target_width=target_w, target_height=target_h,
                                   output_path="cut_preview.png")

        # 执行切割
        output_files = cutter.cut_image(target_width=target_w, target_height=target_h,
                                        output_dir="cut_segments")

        logger.info(f"\n✅ 切割完成！共生成 {len(output_files)} 个图片片段:")
        for file_path in output_files:
            logger.info(f"  - {file_path}")

    except Exception as e:
        logger.info(f"❌ 错误: {e}")


if __name__ == "__main__":
    example_usage()