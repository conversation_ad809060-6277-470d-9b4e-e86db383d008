from enum import Enum
from typing import List, Tuple, Optional
import math
import numpy as np
import cv2
from dataclasses import dataclass

from PIL import ImageFont, Image, ImageDraw
from loguru import logger

from settings import ROOT
from vision.core.data_types import BoundingBox
from .grid_layout_builder import GridLayoutCalculator, GridLayoutStrategy, GridLayout


@dataclass
class CellPosition:
    """单元格在拼接图中的位置信息"""
    index: int  # 原始检测结果的索引
    x: int  # 在拼接图中的x坐标
    y: int  # 在拼接图中的y坐标
    width: int  # 单元格宽度
    height: int  # 单元格高度


class ResizeStrategy(Enum):
    """尺寸调整策略"""
    UNIFORM_MAX = "uniform_max"  # 统一到最大尺寸
    UNIFORM_TARGET = "uniform_target"  # 统一到指定尺寸
    ADAPTIVE_PADDING = "adaptive_padding"  # 自适应填充到统一尺寸
    ASPECT_RATIO_PRESERVE = "preserve_aspect"  # 保持宽高比
    GRID_ADAPTIVE = "grid_adaptive"  # 网格自适应
    MAX_SIZE_CENTER = "max_size_center"  # 最大尺寸居中


class GridImageBuilder:
    """网格图像构建器 - 负责将多个图像区域拼接成网格图像"""

    def __init__(
            self,
            resize_strategy: ResizeStrategy = ResizeStrategy.MAX_SIZE_CENTER,
            layout_strategy: GridLayoutStrategy = GridLayoutStrategy.ASPECT_RATIO_OPTIMAL,
            target_size: Optional[Tuple[int, int]] = None,
            min_size: Tuple[int, int] = (50, 30),
            max_size: Tuple[int, int] = (400, 300),
            background_color: Tuple[int, int, int] = (255, 255, 255),
            padding_color: Tuple[int, int, int] = (240, 240, 240),
            # 布局参数
            fixed_columns: int = None,
            fixed_rows: int = None,
            target_canvas_aspect_ratio: float = None,
            max_columns: int = 8,
            max_rows: int = 8,
            auto_strategy: bool = True,
            gap: int = None,  # 如果提供了gap，将同时设置x_gap和y_gap
            x_gap: int = 50,  # 水平间隙
            y_gap: int = 50,  # 垂直间隙
            # 文本显示参数
            text_font_scale: float = 0.5,  # 文字大小
            text_color: Tuple[int, int, int] = (0, 0, 0),  # 文本颜色
            text_thickness: int = 1,  # 文字粗细
            text_bg_color: Optional[Tuple[int, int, int]] = (255, 255, 255),  # 文本背景色
            text_padding: int = 2,  # 文本内边距
    ):
        """
        Args:
            x_gap: 水平间隙（列与列之间）
            y_gap: 垂直间隙（行与行之间）
            resize_strategy: 尺寸调整策略
            layout_strategy: 网格布局策略
            target_size: 目标尺寸（用于UNIFORM_TARGET策略）
            min_size: 最小尺寸限制
            max_size: 最大尺寸限制
            background_color: 画布背景色
            padding_color: 填充颜色
            fixed_columns: 固定列数
            fixed_rows: 固定行数
            target_canvas_aspect_ratio: 目标画布宽高比
            max_columns: 最大列数
            max_rows: 最大行数
            auto_strategy: 是否自动选择最优布局策略
            gap: 兼容性参数，如果提供则同时设置x_gap和y_gap
            text_font_scale: 文字大小
            text_color: 文本颜色
            text_thickness: 文字粗细
            text_bg_color: 文本背景色，None表示无背景
            text_padding: 文本内边距
        """
        # 处理gap兼容性
        if gap is not None:
            self.x_gap = gap
            self.y_gap = gap
        else:
            self.x_gap = x_gap
            self.y_gap = y_gap

        self.resize_strategy = resize_strategy
        self.layout_strategy = layout_strategy
        self.target_size = target_size
        self.min_size = min_size
        self.max_size = max_size
        self.background_color = background_color
        self.padding_color = padding_color
        self.auto_strategy = auto_strategy

        # 文本显示参数
        self.text_font_scale = text_font_scale
        self.text_color = text_color
        self.text_thickness = text_thickness
        self.text_bg_color = text_bg_color
        self.text_padding = text_padding

        # 创建布局计算器
        self.layout_calculator = GridLayoutCalculator(
            strategy=layout_strategy,
            fixed_columns=fixed_columns,
            fixed_rows=fixed_rows,
            target_canvas_aspect_ratio=target_canvas_aspect_ratio,
            max_columns=max_columns,
            max_rows=max_rows
        )

        # 初始化字体
        self.font = None
        self._init_font()

    def _init_font(self):
        """初始化字体"""
        font_path = ROOT / 'fonts/simhei.ttf'
        try:
            self.font = ImageFont.truetype(str(font_path), 26)
        except (OSError, IOError):
            logger.warning(f"无法加载字体文件：{font_path}，使用默认字体")
            self.font = ImageFont.load_default()

    def extract_regions(self, detections: List[BoundingBox], source_image: np.ndarray) -> List[np.ndarray]:
        """从源图像中提取检测区域"""
        regions = []
        for detection in detections:
            # 确保坐标在图像范围内
            y1 = max(0, detection.y1)
            y2 = min(source_image.shape[0], detection.y2)
            x1 = max(0, detection.x1)
            x2 = min(source_image.shape[1], detection.x2)

            # 提取区域
            if x2 > x1 and y2 > y1:
                region = source_image[y1:y2, x1:x2]
                regions.append(region)
            else:
                # 如果坐标无效，创建一个最小尺寸的白色图像
                dummy_region = np.full((self.min_size[1], self.min_size[0], 3), 255, dtype=np.uint8)
                regions.append(dummy_region)

        return regions

    def build_grid_image(self, images: List[np.ndarray], texts: Optional[List[str]] = None) -> Tuple[
        np.ndarray, GridLayout, List[CellPosition]]:
        """
        构建网格图像

        Args:
            images: 图像列表
            texts: 文本列表，长度应与images一致，如果为None则不显示文本

        Returns:
            Tuple[网格图像, 布局信息, 位置信息列表]
        """
        if not images:
            raise ValueError("No images provided")

        # 验证texts参数
        if texts is not None and len(texts) != len(images):
            raise ValueError(f"Length of texts ({len(texts)}) must match length of images ({len(images)})")

        # 根据策略处理图像和计算布局
        layout, processed_images = self._calculate_adaptive_layout(images)

        # 创建拼接图像
        grid_image, positions = self._create_stitched_image(processed_images, layout)

        # 如果提供了文本，在网格图像上绘制文本
        if texts is not None:
            grid_image = self._draw_texts_on_grid(grid_image, positions, texts)

        return grid_image, layout, positions

    def _draw_texts_on_grid(self, grid_image: np.ndarray, positions: List[CellPosition],
                            texts: List[str]) -> np.ndarray:
        """在网格图像上绘制文本"""
        result_image = grid_image.copy()

        # 将 OpenCV 图像转换为 PIL 图像
        pil_img = Image.fromarray(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_img)

        for position, text in zip(positions, texts):
            if not text:  # 跳过空文本
                continue

            # 计算文本位置（在图像下方）
            text_x = position.x + 5  # 距离左边界5像素
            text_y = position.y + position.height + 15  # 距离图像底部15像素

            # 获取文本尺寸（使用 PIL 的 textbbox 方法）
            bbox = draw.textbbox((text_x, text_y), text, font=self.font)
            text_width = bbox[2] - bbox[0]  # 右边界 - 左边界
            text_height = bbox[3] - bbox[1]  # 下边界 - 上边界

            # 确保文本不超出画布范围
            canvas_height, canvas_width = result_image.shape[:2]
            if text_y > canvas_height - 5:  # 如果文本会超出画布底部
                text_y = position.y - 5  # 将文本移到图像上方

            if text_x + text_width > canvas_width:  # 如果文本会超出画布右侧
                text_x = canvas_width - text_width - 5  # 调整到画布内

            # 重新计算 bbox（因为位置可能已调整）
            bbox = draw.textbbox((text_x, text_y), text, font=self.font)

            # 绘制文本背景（如果设置了背景色）
            if self.text_bg_color is not None:
                bg_x1 = max(0, int(bbox[0] - self.text_padding))
                bg_y1 = max(0, int(bbox[1] - self.text_padding))
                bg_x2 = min(canvas_width, int(bbox[2] + self.text_padding))
                bg_y2 = min(canvas_height, int(bbox[3] + self.text_padding))

                # 将颜色从 BGR 转换为 RGB
                bg_color_rgb = tuple(reversed(self.text_bg_color)) if isinstance(self.text_bg_color,
                                                                                 tuple) else self.text_bg_color
                draw.rectangle([bg_x1, bg_y1, bg_x2, bg_y2], fill=bg_color_rgb)

            # 绘制文本
            text_color_rgb = tuple(reversed(self.text_color)) if isinstance(self.text_color, tuple) else self.text_color
            draw.text((text_x, text_y), text, font=self.font, fill=text_color_rgb)

            # 将 PIL 图像转换回 OpenCV 格式
        result_image = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
        return result_image

    def _calculate_adaptive_layout(self, images: List[np.ndarray]) -> Tuple[GridLayout, List[np.ndarray]]:
        """根据策略计算自适应布局"""
        strategy_map = {
            ResizeStrategy.UNIFORM_TARGET: self._uniform_target_layout,
            ResizeStrategy.ADAPTIVE_PADDING: self._adaptive_padding_layout,
            ResizeStrategy.ASPECT_RATIO_PRESERVE: self._aspect_ratio_preserve_layout,
            ResizeStrategy.GRID_ADAPTIVE: self._grid_adaptive_layout,
            ResizeStrategy.MAX_SIZE_CENTER: self._max_size_center_layout,
            ResizeStrategy.UNIFORM_MAX: self._uniform_max_layout,
        }

        strategy_func = strategy_map.get(self.resize_strategy, self._uniform_max_layout)
        return strategy_func(images)

    def _create_smart_grid_layout(
            self,
            num_items: int,
            cell_width: int,
            cell_height: int,
            images: List[np.ndarray] = None
    ) -> GridLayout:
        """使用智能布局策略创建网格布局"""

        # 如果启用自动策略，分析并选择最优策略
        if self.auto_strategy:
            optimal_strategy = self.layout_calculator.analyze_optimal_strategy(
                num_items, cell_width, cell_height, self.x_gap, self.y_gap
            )
            # 临时更新策略
            original_strategy = self.layout_calculator.strategy
            self.layout_calculator.strategy = optimal_strategy

        try:
            # 收集图像形状信息（如果需要）
            image_shapes = None
            if images:
                image_shapes = [(img.shape[1], img.shape[0]) for img in images]

            # 计算布局
            layout = self.layout_calculator.calculate_grid_layout(
                num_items, cell_width, cell_height, self.x_gap, self.y_gap, image_shapes
            )

            return layout

        finally:
            # 恢复原始策略
            if self.auto_strategy:
                self.layout_calculator.strategy = original_strategy

    def _uniform_target_layout(self, images: List[np.ndarray]) -> Tuple[GridLayout, List[np.ndarray]]:
        """统一到目标尺寸"""
        if not self.target_size:
            raise ValueError("target_size must be specified for UNIFORM_TARGET strategy")

        cell_width, cell_height = self.target_size
        processed_images = [cv2.resize(img, (cell_width, cell_height)) for img in images]

        layout = self._create_smart_grid_layout(len(images), cell_width, cell_height, images)
        return layout, processed_images

    def _adaptive_padding_layout(self, images: List[np.ndarray]) -> Tuple[GridLayout, List[np.ndarray]]:
        """自适应填充 - 保持原始比例，用填充统一尺寸"""
        # 计算目标尺寸
        if self.target_size:
            target_width, target_height = self.target_size
        else:
            # 使用75分位数尺寸避免极端值影响
            widths = [img.shape[1] for img in images]
            heights = [img.shape[0] for img in images]
            target_width = int(np.percentile(widths, 75))
            target_height = int(np.percentile(heights, 75))

        # 限制在合理范围内
        target_width = max(self.min_size[0], min(target_width, self.max_size[0]))
        target_height = max(self.min_size[1], min(target_height, self.max_size[1]))

        processed_images = []
        for img in images:
            processed_img = self._resize_with_padding(img, (target_width, target_height))
            processed_images.append(processed_img)

        layout = self._create_smart_grid_layout(len(images), target_width, target_height, images)
        return layout, processed_images

    def _max_size_center_layout(self, images: List[np.ndarray]) -> Tuple[GridLayout, List[np.ndarray]]:
        """最大尺寸居中布局策略"""
        if not images:
            raise ValueError("No images provided")

        # 找到最大宽度和高度
        max_width = max(img.shape[1] for img in images)
        max_height = max(img.shape[0] for img in images)

        # 应用尺寸限制
        cell_width = max(self.min_size[0], min(max_width, self.max_size[0]))
        cell_height = max(self.min_size[1], min(max_height, self.max_size[1]))

        processed_images = []

        for img in images:
            h, w = img.shape[:2]

            # 创建目标cell
            cell = np.full((cell_height, cell_width, 3), self.padding_color, dtype=np.uint8)

            # 如果图像尺寸超过cell尺寸，需要缩放
            if w > cell_width or h > cell_height:
                scale = min(cell_width / w, cell_height / h)
                new_width = int(w * scale)
                new_height = int(h * scale)
                scaled_img = cv2.resize(img, (new_width, new_height))
            else:
                scaled_img = img
                new_width, new_height = w, h

            # 计算居中位置
            start_x = (cell_width - new_width) // 2
            start_y = (cell_height - new_height) // 2
            end_x = start_x + new_width
            end_y = start_y + new_height

            # 将图像放置到cell中心
            cell[start_y:end_y, start_x:end_x] = scaled_img
            processed_images.append(cell)

        layout = self._create_smart_grid_layout(len(images), cell_width, cell_height, images)
        return layout, processed_images

    def _uniform_max_layout(self, images: List[np.ndarray]) -> Tuple[GridLayout, List[np.ndarray]]:
        """统一到最大尺寸（原始方法）"""
        # 找到所有图像的最大宽度和高度
        max_width = max(img.shape[1] for img in images)
        max_height = max(img.shape[0] for img in images)

        # 应用尺寸限制
        max_width = max(self.min_size[0], min(max_width, self.max_size[0]))
        max_height = max(self.min_size[1], min(max_height, self.max_size[1]))

        # 将所有图像resize到最大尺寸
        processed_images = []
        for img in images:
            resized_img = cv2.resize(img, (max_width, max_height))
            processed_images.append(resized_img)

        layout = self._create_smart_grid_layout(len(images), max_width, max_height, images)
        return layout, processed_images

    def _resize_with_padding(self, image: np.ndarray, target_size: Tuple[int, int]) -> np.ndarray:
        """保持宽高比的resize，不足部分用padding填充"""
        target_width, target_height = target_size
        h, w = image.shape[:2]

        # 计算缩放比例
        scale = min(target_width / w, target_height / h)

        # 新的尺寸
        new_width = int(w * scale)
        new_height = int(h * scale)

        # 缩放图像
        if scale != 1.0:
            resized = cv2.resize(image, (new_width, new_height))
        else:
            resized = image.copy()

        # 计算padding
        pad_x = (target_width - new_width) // 2
        pad_y = (target_height - new_height) // 2

        # 创建目标画布
        result = np.full((target_height, target_width, 3), self.padding_color, dtype=np.uint8)

        # 放置图像
        result[pad_y:pad_y + new_height, pad_x:pad_x + new_width] = resized

        return result

    def _create_stitched_image(
            self,
            processed_images: List[np.ndarray],
            layout: GridLayout
    ) -> Tuple[np.ndarray, List[CellPosition]]:
        """从已处理的图像创建拼接图"""
        canvas = np.full((layout.canvas_height, layout.canvas_width, 3), self.background_color, dtype=np.uint8)
        positions = []

        for idx, img in enumerate(processed_images):
            row = idx // layout.cols
            col = idx % layout.cols

            # 使用分别的x_gap和y_gap计算位置
            x = col * (layout.cell_width + layout.x_gap)
            y = row * (layout.cell_height + layout.y_gap)

            h, w = img.shape[:2]
            canvas[y:y + h, x:x + w] = img

            positions.append(CellPosition(index=idx, x=x, y=y, width=w, height=h))

        return canvas, positions

    # 其他方法保持不变...
    def _aspect_ratio_preserve_layout(self, images: List[np.ndarray]) -> Tuple[GridLayout, List[np.ndarray]]:
        """保持宽高比，等比缩放到合适尺寸"""
        # 分析所有图像的尺寸分布
        sizes = [(img.shape[1], img.shape[0]) for img in images]
        areas = [w * h for w, h in sizes]
        median_area = np.median(areas)
        target_area = median_area * 1.2

        processed_images = []
        max_width = 0
        max_height = 0

        for img in images:
            h, w = img.shape[:2]
            current_area = w * h

            if current_area > 0:
                scale = math.sqrt(target_area / current_area)
                scale = max(0.5, min(scale, 2.0))  # 限制缩放范围

                new_width = int(w * scale)
                new_height = int(h * scale)

                # 应用尺寸限制
                new_width = max(self.min_size[0], min(new_width, self.max_size[0]))
                new_height = max(self.min_size[1], min(new_height, self.max_size[1]))

                resized_img = cv2.resize(img, (new_width, new_height))
            else:
                resized_img = img
                new_width, new_height = w, h

            processed_images.append(resized_img)
            max_width = max(max_width, new_width)
            max_height = max(max_height, new_height)

        # 用padding统一到最大尺寸
        final_images = []
        for img in processed_images:
            padded_img = self._resize_with_padding(img, (max_width, max_height))
            final_images.append(padded_img)

        layout = self._create_smart_grid_layout(len(images), max_width, max_height, images)
        return layout, final_images

    def _grid_adaptive_layout(self, images: List[np.ndarray]) -> Tuple[GridLayout, List[np.ndarray]]:
        """网格自适应 - 每个cell可以有不同尺寸"""
        # 这个方法比较复杂，暂时保持原有逻辑，但使用新的gap设置
        num_items = len(images)
        cols = math.ceil(math.sqrt(num_items))
        rows = math.ceil(num_items / cols)

        processed_images = []
        row_heights = []
        col_widths = [0] * cols

        for row in range(rows):
            row_images = []
            row_max_height = 0

            for col in range(cols):
                idx = row * cols + col
                if idx < len(images):
                    img = images[idx]
                    h, w = img.shape[:2]

                    # 简单的尺寸调整
                    if w > self.max_size[0] or h > self.max_size[1]:
                        scale = min(self.max_size[0] / w, self.max_size[1] / h)
                        new_w, new_h = int(w * scale), int(h * scale)
                        img = cv2.resize(img, (new_w, new_h))
                        w, h = new_w, new_h

                    row_images.append(img)
                    row_max_height = max(row_max_height, h)
                    col_widths[col] = max(col_widths[col], w)

            # 统一行高
            for i, img in enumerate(row_images):
                col_idx = row * cols + i
                if col_idx < cols:
                    padded_img = self._resize_with_padding(img, (col_widths[col_idx], row_max_height))
                    processed_images.append(padded_img)

            row_heights.append(row_max_height)

        # 计算画布尺寸（使用不同的x_gap和y_gap）
        canvas_width = sum(col_widths) + (cols - 1) * self.x_gap
        canvas_height = sum(row_heights) + (rows - 1) * self.y_gap

        layout = GridLayout(
            rows=rows,
            cols=cols,
            cell_width=max(col_widths),
            cell_height=max(row_heights),
            x_gap=self.x_gap,
            y_gap=self.y_gap,
            canvas_width=canvas_width,
            canvas_height=canvas_height
        )

        return layout, processed_images