from enum import Enum
from typing import List, <PERSON>ple
import math
import numpy as np
from dataclasses import dataclass


@dataclass
class GridLayout:
    """网格布局配置"""
    rows: int
    cols: int
    cell_width: int
    cell_height: int
    x_gap: int  # 水平间隙
    y_gap: int  # 垂直间隙
    canvas_width: int
    canvas_height: int


class GridLayoutStrategy(Enum):
    """网格布局策略"""
    SQUARE = "square"  # 正方形网格（原方法）
    ASPECT_RATIO_OPTIMAL = "aspect_optimal"  # 根据图像宽高比优化
    FIXED_COLUMNS = "fixed_columns"  # 固定列数
    FIXED_ROWS = "fixed_rows"  # 固定行数
    TARGET_ASPECT_RATIO = "target_aspect"  # 目标画布宽高比
    MINIMIZE_CANVAS_AREA = "min_area"  # 最小化画布面积
    BALANCED_DIMENSIONS = "balanced"  # 平衡行列数


class GridLayoutCalculator:
    """网格布局计算器"""

    def __init__(
            self,
            strategy: GridLayoutStrategy = GridLayoutStrategy.ASPECT_RATIO_OPTIMAL,
            fixed_columns: int = None,
            fixed_rows: int = None,
            target_canvas_aspect_ratio: float = None,
            max_columns: int = 8,
            max_rows: int = 8
    ):
        """
        Args:
            strategy: 布局策略
            fixed_columns: 固定列数（用于FIXED_COLUMNS策略）
            fixed_rows: 固定行数（用于FIXED_ROWS策略）
            target_canvas_aspect_ratio: 目标画布宽高比（用于TARGET_ASPECT_RATIO策略）
            max_columns: 最大列数限制
            max_rows: 最大行数限制
        """
        self.strategy = strategy
        self.fixed_columns = fixed_columns
        self.fixed_rows = fixed_rows
        self.target_canvas_aspect_ratio = target_canvas_aspect_ratio
        self.max_columns = max_columns
        self.max_rows = max_rows

    def calculate_grid_layout(
            self,
            num_items: int,
            cell_width: int,
            cell_height: int,
            x_gap: int,
            y_gap: int,
            image_shapes: List[Tuple[int, int]] = None
    ) -> GridLayout:
        """
        计算网格布局

        Args:
            num_items: 项目数量
            cell_width: 单元格宽度
            cell_height: 单元格高度
            x_gap: 水平间隙
            y_gap: 垂直间隙
            image_shapes: 图像形状列表 [(width, height), ...]，用于某些策略

        Returns:
            GridLayout: 网格布局配置
        """
        if num_items <= 0:
            raise ValueError("num_items must be positive")

        strategy_map = {
            GridLayoutStrategy.SQUARE: self._calculate_square_layout,
            GridLayoutStrategy.ASPECT_RATIO_OPTIMAL: self._calculate_aspect_optimal_layout,
            GridLayoutStrategy.FIXED_COLUMNS: self._calculate_fixed_columns_layout,
            GridLayoutStrategy.FIXED_ROWS: self._calculate_fixed_rows_layout,
            GridLayoutStrategy.TARGET_ASPECT_RATIO: self._calculate_target_aspect_layout,
            GridLayoutStrategy.MINIMIZE_CANVAS_AREA: self._calculate_min_area_layout,
            GridLayoutStrategy.BALANCED_DIMENSIONS: self._calculate_balanced_layout,
        }

        strategy_func = strategy_map.get(self.strategy, self._calculate_square_layout)
        rows, cols = strategy_func(num_items, cell_width, cell_height, x_gap, y_gap, image_shapes)

        # 应用限制
        cols = min(cols, self.max_columns)
        rows = min(rows, self.max_rows)

        # 重新计算以确保能容纳所有items
        if rows * cols < num_items:
            rows = math.ceil(num_items / cols)

        # 计算画布尺寸
        canvas_width = cols * cell_width + (cols - 1) * x_gap
        canvas_height = rows * cell_height + (rows - 1) * y_gap

        return GridLayout(
            rows=rows,
            cols=cols,
            cell_width=cell_width,
            cell_height=cell_height,
            x_gap=x_gap,
            y_gap=y_gap,
            canvas_width=canvas_width,
            canvas_height=canvas_height
        )

    def _calculate_square_layout(
            self,
            num_items: int,
            cell_width: int,
            cell_height: int,
            x_gap: int,
            y_gap: int,
            image_shapes: List[Tuple[int, int]] = None
    ) -> Tuple[int, int]:
        """正方形网格布局（原方法）"""
        grid_size = math.ceil(math.sqrt(num_items))
        return grid_size, grid_size

    def _calculate_aspect_optimal_layout(
            self,
            num_items: int,
            cell_width: int,
            cell_height: int,
            x_gap: int,
            y_gap: int,
            image_shapes: List[Tuple[int, int]] = None
    ) -> Tuple[int, int]:
        """
        根据cell的宽高比优化布局
        考虑不同的x_gap和y_gap对最终画布比例的影响
        """
        # 计算有效的单元格尺寸（包含间隙）
        effective_cell_width = cell_width + x_gap
        effective_cell_height = cell_height + y_gap
        effective_aspect_ratio = effective_cell_width / effective_cell_height if effective_cell_height > 0 else 1.0

        # 计算理想的行列比例
        ideal_row_col_ratio = 1 / effective_aspect_ratio

        # 寻找最接近理想比例的行列组合
        best_rows, best_cols = 1, num_items
        best_score = float('inf')

        for cols in range(1, num_items + 1):
            rows = math.ceil(num_items / cols)

            # 计算实际的行列比例
            actual_ratio = rows / cols

            # 计算与理想比例的差距
            ratio_diff = abs(actual_ratio - ideal_row_col_ratio)

            # 计算画布面积（考虑间隙）
            canvas_width = cols * cell_width + (cols - 1) * x_gap
            canvas_height = rows * cell_height + (rows - 1) * y_gap
            canvas_area = canvas_width * canvas_height

            # 综合评分（比例差距 + 面积惩罚）
            score = ratio_diff + canvas_area * 1e-8

            if score < best_score:
                best_score = score
                best_rows, best_cols = rows, cols

        return best_rows, best_cols

    def _calculate_fixed_columns_layout(
            self,
            num_items: int,
            cell_width: int,
            cell_height: int,
            x_gap: int,
            y_gap: int,
            image_shapes: List[Tuple[int, int]] = None
    ) -> Tuple[int, int]:
        """固定列数布局"""
        if not self.fixed_columns or self.fixed_columns <= 0:
            return self._calculate_square_layout(num_items, cell_width, cell_height, x_gap, y_gap)

        cols = min(self.fixed_columns, num_items)
        rows = math.ceil(num_items / cols)
        return rows, cols

    def _calculate_fixed_rows_layout(
            self,
            num_items: int,
            cell_width: int,
            cell_height: int,
            x_gap: int,
            y_gap: int,
            image_shapes: List[Tuple[int, int]] = None
    ) -> Tuple[int, int]:
        """固定行数布局"""
        if not self.fixed_rows or self.fixed_rows <= 0:
            return self._calculate_square_layout(num_items, cell_width, cell_height, x_gap, y_gap)

        rows = min(self.fixed_rows, num_items)
        cols = math.ceil(num_items / rows)
        return rows, cols

    def _calculate_target_aspect_layout(
            self,
            num_items: int,
            cell_width: int,
            cell_height: int,
            x_gap: int,
            y_gap: int,
            image_shapes: List[Tuple[int, int]] = None
    ) -> Tuple[int, int]:
        """目标画布宽高比布局"""
        if not self.target_canvas_aspect_ratio or self.target_canvas_aspect_ratio <= 0:
            return self._calculate_square_layout(num_items, cell_width, cell_height, x_gap, y_gap)

        target_ratio = self.target_canvas_aspect_ratio  # width/height

        best_rows, best_cols = 1, num_items
        best_diff = float('inf')

        for cols in range(1, num_items + 1):
            rows = math.ceil(num_items / cols)

            # 计算画布宽高比（考虑不同的x_gap和y_gap）
            canvas_width = cols * cell_width + (cols - 1) * x_gap
            canvas_height = rows * cell_height + (rows - 1) * y_gap
            actual_ratio = canvas_width / canvas_height if canvas_height > 0 else float('inf')

            # 计算与目标比例的差距
            ratio_diff = abs(actual_ratio - target_ratio)

            if ratio_diff < best_diff:
                best_diff = ratio_diff
                best_rows, best_cols = rows, cols

        return best_rows, best_cols

    def _calculate_min_area_layout(
            self,
            num_items: int,
            cell_width: int,
            cell_height: int,
            x_gap: int,
            y_gap: int,
            image_shapes: List[Tuple[int, int]] = None
    ) -> Tuple[int, int]:
        """最小化画布面积布局"""
        best_rows, best_cols = 1, num_items
        min_area = float('inf')

        for cols in range(1, num_items + 1):
            rows = math.ceil(num_items / cols)

            # 计算画布面积（考虑不同的x_gap和y_gap）
            canvas_width = cols * cell_width + (cols - 1) * x_gap
            canvas_height = rows * cell_height + (rows - 1) * y_gap
            area = canvas_width * canvas_height

            if area < min_area:
                min_area = area
                best_rows, best_cols = rows, cols

        return best_rows, best_cols

    def _calculate_balanced_layout(
            self,
            num_items: int,
            cell_width: int,
            cell_height: int,
            x_gap: int,
            y_gap: int,
            image_shapes: List[Tuple[int, int]] = None
    ) -> Tuple[int, int]:
        """平衡行列数布局 - 尽量让行数和列数接近"""
        best_rows, best_cols = 1, num_items
        min_diff = float('inf')

        for cols in range(1, num_items + 1):
            rows = math.ceil(num_items / cols)

            # 计算行列数差距
            diff = abs(rows - cols)

            # 偏向较小的差距
            if diff < min_diff:
                min_diff = diff
                best_rows, best_cols = rows, cols
            elif diff == min_diff:
                # 如果差距相同，选择面积更小的
                canvas_width = cols * cell_width + (cols - 1) * x_gap
                canvas_height = rows * cell_height + (rows - 1) * y_gap
                current_area = canvas_width * canvas_height

                best_canvas_width = best_cols * cell_width + (best_cols - 1) * x_gap
                best_canvas_height = best_rows * cell_height + (best_rows - 1) * y_gap
                best_area = best_canvas_width * best_canvas_height

                if current_area < best_area:
                    best_rows, best_cols = rows, cols

        return best_rows, best_cols

    @staticmethod
    def analyze_optimal_strategy(
            num_items: int,
            cell_width: int,
            cell_height: int,
            x_gap: int,
            y_gap: int,
            image_shapes: List[Tuple[int, int]] = None
    ) -> GridLayoutStrategy:
        """
        分析并推荐最优的布局策略
        考虑不同的x_gap和y_gap对策略选择的影响
        """
        # 计算有效的单元格宽高比（包含间隙）
        effective_cell_width = cell_width + x_gap
        effective_cell_height = cell_height + y_gap
        effective_aspect_ratio = effective_cell_width / effective_cell_height if effective_cell_height > 0 else 1.0

        # 如果有效cell的宽高比极端（很宽或很高），使用aspect_optimal
        if effective_aspect_ratio > 2.0 or effective_aspect_ratio < 0.5:
            return GridLayoutStrategy.ASPECT_RATIO_OPTIMAL

        # 如果x_gap和y_gap差异很大，使用aspect_optimal来平衡
        gap_ratio = max(x_gap, y_gap) / max(min(x_gap, y_gap), 1)
        if gap_ratio > 3.0:
            return GridLayoutStrategy.ASPECT_RATIO_OPTIMAL

        # 如果items数量较少，使用balanced
        if num_items <= 6:
            return GridLayoutStrategy.BALANCED_DIMENSIONS

        # 如果items数量较多，使用min_area
        if num_items > 20:
            return GridLayoutStrategy.MINIMIZE_CANVAS_AREA

        # 默认使用aspect_optimal
        return GridLayoutStrategy.ASPECT_RATIO_OPTIMAL